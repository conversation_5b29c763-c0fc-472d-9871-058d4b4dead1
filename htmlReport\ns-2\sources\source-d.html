


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenRequestPageDTO</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">backend.rest.model</a>
</div>

<h1>Coverage Summary for Class: CitizenRequestPageDTO (backend.rest.model)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">CitizenRequestPageDTO</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    85.2%
  </span>
  <span class="absValue">
    (23/27)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    45.5%
  </span>
  <span class="absValue">
    (10/22)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    67.2%
  </span>
  <span class="absValue">
    (41/61)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package backend.rest.model;
&nbsp;
&nbsp;import java.net.URI;
&nbsp;import java.util.Objects;
&nbsp;import backend.rest.model.CitizenRequestDTO;
&nbsp;import com.fasterxml.jackson.annotation.JsonProperty;
&nbsp;import com.fasterxml.jackson.annotation.JsonCreator;
&nbsp;import com.fasterxml.jackson.annotation.JsonTypeName;
&nbsp;import java.util.ArrayList;
&nbsp;import java.util.Arrays;
&nbsp;import java.util.List;
&nbsp;import org.springframework.lang.Nullable;
&nbsp;import java.time.OffsetDateTime;
&nbsp;import jakarta.validation.Valid;
&nbsp;import jakarta.validation.constraints.*;
&nbsp;import org.hibernate.validator.constraints.*;
&nbsp;import io.swagger.v3.oas.annotations.media.Schema;
&nbsp;
&nbsp;
&nbsp;import java.util.*;
&nbsp;import jakarta.annotation.Generated;
&nbsp;
&nbsp;/**
&nbsp; * CitizenRequestPageDTO
&nbsp; */
&nbsp;
&nbsp;@JsonTypeName(&quot;CitizenRequestPage&quot;)
&nbsp;@Generated(value = &quot;org.openapitools.codegen.languages.SpringCodegen&quot;, date = &quot;2025-07-16T14:11:30.627767400+02:00[Europe/Brussels]&quot;, comments = &quot;Generator version: 7.12.0&quot;)
<b class="fc">&nbsp;public class CitizenRequestPageDTO {</b>
&nbsp;
<b class="fc">&nbsp;  @Valid</b>
&nbsp;  private List&lt;@Valid CitizenRequestDTO&gt; content = new ArrayList&lt;&gt;();
&nbsp;
&nbsp;  private @Nullable Integer pageNumber;
&nbsp;
&nbsp;  private @Nullable Integer pageSize;
&nbsp;
&nbsp;  private @Nullable Integer totalPage;
&nbsp;
&nbsp;  private @Nullable Integer totalElements;
&nbsp;
&nbsp;  private @Nullable Boolean isFirst;
&nbsp;
&nbsp;  private @Nullable Boolean isLast;
&nbsp;
&nbsp;  public CitizenRequestPageDTO content(List&lt;@Valid CitizenRequestDTO&gt; content) {
<b class="nc">&nbsp;    this.content = content;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenRequestPageDTO addContentItem(CitizenRequestDTO contentItem) {
<b class="pc">&nbsp;    if (this.content == null) {</b>
<b class="nc">&nbsp;      this.content = new ArrayList&lt;&gt;();</b>
&nbsp;    }
<b class="fc">&nbsp;    this.content.add(contentItem);</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get content
&nbsp;   * @return content
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;content&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;content&quot;)
&nbsp;  public List&lt;@Valid CitizenRequestDTO&gt; getContent() {
<b class="fc">&nbsp;    return content;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setContent(List&lt;@Valid CitizenRequestDTO&gt; content) {
<b class="fc">&nbsp;    this.content = content;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenRequestPageDTO pageNumber(Integer pageNumber) {
<b class="fc">&nbsp;    this.pageNumber = pageNumber;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get pageNumber
&nbsp;   * @return pageNumber
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;pageNumber&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;pageNumber&quot;)
&nbsp;  public Integer getPageNumber() {
<b class="fc">&nbsp;    return pageNumber;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setPageNumber(Integer pageNumber) {
<b class="fc">&nbsp;    this.pageNumber = pageNumber;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenRequestPageDTO pageSize(Integer pageSize) {
<b class="fc">&nbsp;    this.pageSize = pageSize;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get pageSize
&nbsp;   * @return pageSize
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;pageSize&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;pageSize&quot;)
&nbsp;  public Integer getPageSize() {
<b class="fc">&nbsp;    return pageSize;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setPageSize(Integer pageSize) {
<b class="fc">&nbsp;    this.pageSize = pageSize;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenRequestPageDTO totalPage(Integer totalPage) {
<b class="fc">&nbsp;    this.totalPage = totalPage;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get totalPage
&nbsp;   * @return totalPage
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;totalPage&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;totalPage&quot;)
&nbsp;  public Integer getTotalPage() {
<b class="fc">&nbsp;    return totalPage;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setTotalPage(Integer totalPage) {
<b class="fc">&nbsp;    this.totalPage = totalPage;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenRequestPageDTO totalElements(Integer totalElements) {
<b class="fc">&nbsp;    this.totalElements = totalElements;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get totalElements
&nbsp;   * @return totalElements
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;totalElements&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;totalElements&quot;)
&nbsp;  public Integer getTotalElements() {
<b class="fc">&nbsp;    return totalElements;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setTotalElements(Integer totalElements) {
<b class="fc">&nbsp;    this.totalElements = totalElements;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenRequestPageDTO isFirst(Boolean isFirst) {
<b class="fc">&nbsp;    this.isFirst = isFirst;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get isFirst
&nbsp;   * @return isFirst
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;isFirst&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;isFirst&quot;)
&nbsp;  public Boolean getIsFirst() {
<b class="fc">&nbsp;    return isFirst;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setIsFirst(Boolean isFirst) {
<b class="fc">&nbsp;    this.isFirst = isFirst;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenRequestPageDTO isLast(Boolean isLast) {
<b class="fc">&nbsp;    this.isLast = isLast;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get isLast
&nbsp;   * @return isLast
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;isLast&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;isLast&quot;)
&nbsp;  public Boolean getIsLast() {
<b class="fc">&nbsp;    return isLast;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setIsLast(Boolean isLast) {
<b class="fc">&nbsp;    this.isLast = isLast;</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public boolean equals(Object o) {
<b class="pc">&nbsp;    if (this == o) {</b>
<b class="nc">&nbsp;      return true;</b>
&nbsp;    }
<b class="pc">&nbsp;    if (o == null || getClass() != o.getClass()) {</b>
<b class="nc">&nbsp;      return false;</b>
&nbsp;    }
<b class="fc">&nbsp;    CitizenRequestPageDTO citizenRequestPage = (CitizenRequestPageDTO) o;</b>
<b class="pc">&nbsp;    return Objects.equals(this.content, citizenRequestPage.content) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.pageNumber, citizenRequestPage.pageNumber) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.pageSize, citizenRequestPage.pageSize) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.totalPage, citizenRequestPage.totalPage) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.totalElements, citizenRequestPage.totalElements) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.isFirst, citizenRequestPage.isFirst) &amp;&amp;</b>
<b class="fc">&nbsp;        Objects.equals(this.isLast, citizenRequestPage.isLast);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public int hashCode() {
<b class="nc">&nbsp;    return Objects.hash(content, pageNumber, pageSize, totalPage, totalElements, isFirst, isLast);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public String toString() {
<b class="nc">&nbsp;    StringBuilder sb = new StringBuilder();</b>
<b class="nc">&nbsp;    sb.append(&quot;class CitizenRequestPageDTO {\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    content: &quot;).append(toIndentedString(content)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    pageNumber: &quot;).append(toIndentedString(pageNumber)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    pageSize: &quot;).append(toIndentedString(pageSize)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    totalPage: &quot;).append(toIndentedString(totalPage)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    totalElements: &quot;).append(toIndentedString(totalElements)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    isFirst: &quot;).append(toIndentedString(isFirst)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    isLast: &quot;).append(toIndentedString(isLast)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;}&quot;);</b>
<b class="nc">&nbsp;    return sb.toString();</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Convert the given object to string with each line indented by 4 spaces
&nbsp;   * (except the first line).
&nbsp;   */
&nbsp;  private String toIndentedString(Object o) {
<b class="nc">&nbsp;    if (o == null) {</b>
<b class="nc">&nbsp;      return &quot;null&quot;;</b>
&nbsp;    }
<b class="nc">&nbsp;    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</b>
&nbsp;  }
&nbsp;}
&nbsp;
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
