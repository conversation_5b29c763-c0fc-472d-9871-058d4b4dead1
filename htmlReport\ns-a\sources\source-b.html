


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenInfoEntity</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.citizeninfo.model</a>
</div>

<h1>Coverage Summary for Class: CitizenInfoEntity (be.fgov.onerva.person.backend.citizeninfo.model)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
</tr>
  <tr>
    <td class="name">CitizenInfoEntity$HibernateInstantiator$Ftd4truh</td>
  </tr>
  <tr>
    <td class="name">CitizenInfoEntity$HibernateProxy$9JxCSc80</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.citizeninfo.model;
&nbsp;
&nbsp;import lombok.*;
&nbsp;
&nbsp;import jakarta.persistence.*;
&nbsp;
&nbsp;@Getter
&nbsp;@Builder
&nbsp;@AllArgsConstructor
&nbsp;@NoArgsConstructor
&nbsp;@Entity
&nbsp;@Table(name = &quot;keybox_ds&quot;, schema = &quot;dbo&quot;)
&nbsp;public class CitizenInfoEntity {
&nbsp;    @Id
&nbsp;    @Column(name = &quot;num_pens&quot;)
&nbsp;    private int numPens;
&nbsp;
&nbsp;    private int numBox;
&nbsp;    private boolean flagPurge;
&nbsp;    private int lastId;
&nbsp;
&nbsp;    private boolean flagToPurge;
&nbsp;
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
