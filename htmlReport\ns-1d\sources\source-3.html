


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > PersonCreatedStatus</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.msg.v1</a>
</div>

<h1>Coverage Summary for Class: PersonCreatedStatus (be.fgov.onerva.person.msg.v1)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">PersonCreatedStatus</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    66.7%
  </span>
  <span class="absValue">
    (4/6)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    50%
  </span>
  <span class="absValue">
    (5/10)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.msg.v1;
&nbsp;
&nbsp;
<b class="fc">&nbsp;public enum PersonCreatedStatus {</b>
<b class="fc">&nbsp;  CREATED((String)&quot;CREATED&quot;), EXISTS((String)&quot;EXISTS&quot;), INVALID_SSIN((String)&quot;INVALID_SSIN&quot;), ERROR((String)&quot;ERROR&quot;);</b>
&nbsp;
&nbsp;  private String value;
&nbsp;
<b class="fc">&nbsp;  PersonCreatedStatus(String value) {</b>
<b class="fc">&nbsp;    this.value = value;</b>
&nbsp;  }
&nbsp;
&nbsp;  public String getValue() {
<b class="nc">&nbsp;    return value;</b>
&nbsp;  }
&nbsp;
&nbsp;  public static PersonCreatedStatus fromValue(String value) {
<b class="nc">&nbsp;    for (PersonCreatedStatus e : PersonCreatedStatus.values()) {</b>
<b class="nc">&nbsp;      if (e.value.equals(value)) {</b>
<b class="nc">&nbsp;        return e;</b>
&nbsp;      }
&nbsp;    }
<b class="nc">&nbsp;    throw new IllegalArgumentException(&quot;Unexpected value &#39;&quot; + value + &quot;&#39;&quot;);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public String toString() {
<b class="fc">&nbsp;    return String.valueOf(value);</b>
&nbsp;  }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
