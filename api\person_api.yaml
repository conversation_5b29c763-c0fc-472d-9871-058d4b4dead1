openapi: 3.0.3
info:
  title: Person API
  description: Person API
  version: 1.0.0
servers:
  - url: '{protocol}://{hostname}:{port}/{basePath}/'
    variables:
      protocol:
        enum:
          - https
          - http
        default: http
      hostname:
        default: localhost
      port:
        default: '8080'
      basePath:
        default: api
tags:
  - name: Citizen
paths:
  /citizen/{niss}:
    parameters:
      - in: path
        name: niss
        required: true
        schema:
          type: string
        examples:
          200_OKAY:
            value: '***********'
    get:
      description: Get a citizen base on her niss
      operationId: getByNiss
      tags:
        - Citizen
      responses:
        '200':
          description: Citizen
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Citizen'
              examples:
                200_OKAY:
                  value:
                    niss: '***********'
                    firstname: NAME1
                    lastname: LASTNAME1
                    numbox: 47000
                    zipCode: 1180
                    pensionNumber: 999231999
    put:
      description: Update a citizen with a given niss
      operationId: updateCitizen
      tags:
        - Citizen
      parameters:
        - name: username
          in: query
          required: true
          description: 'The username that made the change for audit purposes.'
          schema:
            type: string
      requestBody:
        required: true
        content:
          '*/*':
            schema:
              $ref: '#/components/schemas/CitizenUpdateRequest'
      responses:
        '204':
          description: Citizen update request accepted
          headers:
            Location:
              description: Location header pointing to the update request
              schema:
                type: string
        '400':
          description: The request was invalid.
        '404':
          description: Citizen not found

  /citizen/numbox/{numbox}:
    get:
      description: Get a citizen base on her numbox
      operationId: getByNumbox
      tags:
        - Citizen
      parameters:
        - in: path
          name: numbox
          required: true
          schema:
            type: integer
            format: int32
          examples:
            200_OKAY:
              value: 47000
      responses:
        '200':
          description: Citizen
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Citizen'
              examples:
                200_OKAY:
                  value:
                    niss: '***********'
                    firstname: NAME1
                    lastname: LASTNAME1
                    numbox: 47000
                    zipCode: 1180
                    pensionNumber: 991231999

  /citizen:
    post:
      description: Create a new Citizen
      operationId: createCitizen
      tags:
        - Citizen
      parameters:
        - in: query
          required: true
          name: businessDomain
          schema:
            type: string
            enum:
              - ADMISSIBILITY
          description: Reference the business domain where the citizen need to be created
        - in: query
          required: true
          name: allowance
          schema:
            type: boolean
          description: True citizen will request allowance, false a citizen will not immediately request allowance
      requestBody:
        required: true
        content:
          '*/*':
            schema:
              $ref: '#/components/schemas/CitizenCreationRequest'
      responses:
        '201':
          description: A valid request to create a new citizen has been created
        '400':
          description: An error occurred during the processing of the request
        '409':
          description: The requested citizen already exists
    get:
      description: Search citizen based on criteria
      operationId: searchCitizen
      tags:
        - Citizen
      parameters:
        - in: query
          name: query
          required: true
          description: |-
            Uses RSQL syntax to search through citizens (See: https://molgenis.gitbooks.io/molgenis/content/guide-rsql.html") 
            Available fields are:
              - numBox
              - fullName
              - id (That refers to numPens)
          schema:
            type: string
          examples:
            200_OKAY:
              value: "fullName=='LASTNAME1*'"
        - $ref: '#/components/parameters/pageParam'
        - $ref: '#/components/parameters/sizeParam'
      responses:
        '200':
          description: Citizen
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CitizenPage'
              examples:
                200_OKAY:
                  value:
                    content:
                      - niss: '***********'
                        firstname: NAME1
                        lastname: LASTNAME1
                        numbox: 47000
                        zipCode: 1180
                        pensionNumber: 991231999
                    pageNumber: 0
                    pageSize: 10
                    totalPage: 1
                    totalElements: 1
                    isFirst: true
                    isLast: true

  '/citizen/info':
    get:
      description: Search citizens based on a list of SSIN or ids
      operationId: searchCitizenInfo
      tags:
        - CitizenInfo
      parameters:
        - in: query
          name: ssins
          description: Get citizen info using a list of ssins. Cannot be used with citizenId.
          schema:
            type: array
            items:
              type: string
          examples:
            200_OKAY:
              value:
                - '57121414240'
            PERSON_809:
              value:
                - '***********'
            PERSON_833:
              value:
                - '88080880833'
        - in: query
          name: citizenId
          description: Search citizens by id(s). CitizenId is the MFX numbox. Cannot be used with ssins.
          schema:
            type: array
            items:
              type: integer
              minimum: 1
          examples:
            PERSON_123:
              value:
                - 123
        - in: query
          name: dataReturned
          deprecated: true
          description: This parameter is never used!
          schema:
            type: string
            default: 'SUMMARY'
        - in: query
          name: pageNumber
          deprecated: true
          description: Paging doesn't make sense with a list of ids.
          schema:
            type: integer
            minimum: 0
            default: 0
          examples:
            200_OKAY:
              value: 0
        - in: query
          name: pageSize
          deprecated: true
          description: Paging doesn't make sense with a list of ids.
          schema:
            type: integer
            default: 10
          examples:
            200_OKAY:
              value: 10
      responses:
        '200':
          description: CitizenInfo
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CitizenInfoPage'
              examples:
                200_NO_DATA:
                  value:
                    pageSize: 10
                    pageNumber: 0
                    totalPage: 1
                    totalElements: 0
                    isFirst: true
                    isLast: true
                    content: []
                200_OKAY:
                  value:
                    pageSize: 10
                    pageNumber: 0
                    totalPage: 1
                    totalElements: 1
                    isFirst: true
                    isLast: true
                    content:
                      - id: null
                        ssin: '57121414240'
                        numPens: 0
                        lastName: person
                        firstName: test
                        address: 'Rue de St Gilles 36 1'
                        postalCode: 1060
                        addressObj:
                          street: 'Rue de St Gilles'
                          number: '36'
                          box: '1'
                          zip: '1060'
                          city: 'St Gilles'
                          countryCode: 1
                          validFrom: '2021-10-08'
                        numBox: 0
                        unemploymentOffice: 0
                        iban: '****************'
                        bankAccount:
                          iban: '****************'
                          bic: 'KREDBEBB'
                          holder: 'John Doe'
                          validFrom: '2021-10-08'
                        paymentMode: 1
                        language: 'fr'
                        sex: 'm'
                        flagNation: 150
                        flagVCpte: null
                        email: null
                        emailReg: '<EMAIL>'
                        flagPurge: 'n'
                        lastModifDate: ********
                        telephoneOnem: null
                        gsmOnem: null
                        telephoneReg: '**********'
                        gsmReg: null
                        birthDate: null
                        deceasedDate: null
                        bisNumber: null
                        employmentContract: 1
                        unionDue:
                          mandateActive: true
                          validFrom: '2024-01-01'
                        OP: 0
                PERSON_123:
                  $ref: '#/components/examples/CITIZEN_123'
                PERSON_809:
                  $ref: '#/components/examples/CITIZEN_123'
                PERSON_833:
                  $ref: '#/components/examples/CITIZEN_123'

  '/v2/citizen/info':
    get:
      deprecated: true
      description: Use the searchCitizenInfo.
      operationId: searchCitizenInfov2
      tags:
        - CitizenInfoV2
      parameters:
        - in: query
          name: ssins
          required: true
          description: |-
            Get citizen info using a list of ssins
          style: form
          explode: false
          schema:
            type: array
            items:
              type: string
          examples:
            200_OKAY:
              value:
                - '57121414240'
        - in: query
          name: dataReturned
          schema:
            type: string
            default: 'SUMMARY'
        - $ref: '#/components/parameters/pageParam'
        - $ref: '#/components/parameters/sizeParam'
      responses:
        '200':
          description: CitizenInfo
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CitizenInfoPageV2'
              examples:
                200_NO_DATA:
                  value:
                    pageSize: 10
                    pageNumber: 0
                    totalPage: 1
                    totalElements: 0
                    isFirst: true
                    isLast: true
                    content: []
                200_OKAY:
                  value:
                    pageSize: 10
                    pageNumber: 0
                    totalPage: 1
                    totalElements: 1
                    isFirst: true
                    isLast: true
                    content:
                      - id: 0
                        ssin: '57121414240'
                        numPens: 0
                        lastName: person
                        firstName: test
                        address: Saint-Gilles
                        postalCode: 1060
                        numBox: 0
                        OP: 0
                        unemploymentOffice: 0
                        iban: ''
                        language: ''
                        sex: 'every day'
                        flagNation: 0
                        rvaCountryCode: 1
                        flagVCpte: 0
                        email: ''
                        emailReg: ''
                        flagToPurge: ''
                        lastModifDate: 20220101
                        telephoneOnem: ''
                        gsmOnem: ''
                        telephoneReg: ''
                        gsmReg: ''
                        deceasedDate: 2000-01-01
                        bisNumber:
                          - ''

  '/citizen/requests':
    get:
      description: Search citizens requests based on a query
      operationId: searchCitizenRequests
      tags:
        - CitizenRequests
      parameters:
        - $ref: '#/components/parameters/queryParam'
        - $ref: '#/components/parameters/pageParam'
        - $ref: '#/components/parameters/sizeParam'
        - $ref: '#/components/parameters/sortParam'
      x-spring-paginated: true
      responses:
        '200':
          description: CitizenRequests
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CitizenRequestPage'
              examples:
                200_OKAY:
                  value:
                    content:
                      id: 1
                      created: 2000-01-01T00:00:00.000+01:00
                      niss: '88123180897'
                      firstname: Jean
                      lastname: Dupond
                      sent: false
                      updated: 2001-01-01T00:00:00.000+01:00
                      retryCount: 0
                      returnCode: 1
                      error: ''
                    pageNumber: 0
                    pageSize: 1
                    totalPage: 1
                    totalElements: 1
                    isFirst: true
                    isLast: true
  /citizen/requests/{id}:
    get:
      description: Get a citizen request by id
      operationId: getById
      tags:
        - CitizenRequests
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: number
            format: int64
          examples:
            200_OKAY:
              value: 1
      responses:
        '200':
          description: CitizenRequest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CitizenRequest'
              examples:
                200_OKAY:
                  value:
                    id: 1
                    created: 2000-01-01T00:00:00.000+01:00
                    niss: '88123180897'
                    firstname: LASTNAME1
                    lastname: NAME1
                    sent: false
                    updated: null
                    retryCount: 0
                    returnCode: null
                    error: null

components:
  schemas:
    Page:
      type: object
      properties:
        content:
          type: array
          items:
            type: object
        pageNumber:
          type: integer
        pageSize:
          type: integer
        totalPage:
          type: integer
        totalElements:
          type: integer
        isFirst:
          type: boolean
        isLast:
          type: boolean

    CitizenCreationRequest:
      type: object
      description: Describe how to request
      properties:
        niss:
          type: string
        firstname:
          type: string
        lastname:
          type: string
        fallbackUrl:
          type: string
          deprecated: true
          description: "Deprecated. A POST will be done on this endpoint with the following request body {\"status \":  \"SUCCESS \ FAILED\"}"
        correlationId:
          type: string
          maxLength: 50
          description: 'An optional ID of your choice to correlate with.'
    CitizenUpdateRequest:
      type: object
      description: Citizen update request
      required:
        - address
        - validFrom
      properties:
        address:
          $ref: '#/components/schemas/ForeignAddress'
          nullable: false
        nationalityCode:
          description: 'CBSS Nationality code (see Lookups)'
          type: integer
          minimum: 100
          maximum: 999
        validFrom:
          type: string
          format: date
          nullable: false
        correlationId:
          type: string
          maxLength: 50
          description: 'An optional ID of your choice to correlate with.'
        birthDate:
          type: string
          format: date
        unemploymentOffice:
          type: integer
          format: int32
          description: Unemployment office code
        languageCode:
          type: integer
          minimum: 1
          maximum: 3
          description: 'Language code: 1=fr, 2=nl, 3=de'
        bankInfo:
          $ref: '#/components/schemas/BankUpdateRequest'
        unionDueInfo:
          $ref: '#/components/schemas/UnionDueUpdateRequest'
    Citizen:
      type: object
      description: Citizen information
      properties:
        niss:
          type: string
        firstname:
          type: string
        lastname:
          type: string
        numbox:
          type: integer
        zipCode:
          type: integer
        pensionNumber:
          type: integer
        agent:
          type: boolean

    CitizenPage:
      allOf:
        - $ref: '#/components/schemas/Page'
        - type: object
          properties:
            content:
              default: []
              items:
                $ref: '#/components/schemas/Citizen'
    CitizenRequest:
      type: object
      description: Citizen creation request
      required:
        - id
        - created
        - niss
        - sent
        - retryCount
        - type
      properties:
        id:
          type: number
          format: int64
        created:
          type: string
          format: date-time
        niss:
          type: string
        firstname:
          type: string
        lastname:
          type: string
        correlationId:
          type: string
          nullable: true
        sent:
          type: boolean
        updated:
          type: string
          format: date-time
          nullable: true
        retryCount:
          type: integer
        returnCode:
          type: integer
          nullable: true
        error:
          type: string
          nullable: true
        type:
          type: string
          enum:
            - CREATE
            - UPDATE
        nationalityCode:
          type: integer
          nullable: true
        paymentType:
          $ref: '#/components/schemas/PaymentType'
          nullable: true
        unionDue:
          type: boolean
          nullable: true
        valueDate:
          type: string
          format: date
          nullable: true
        address:
          $ref: '#/components/schemas/Address'
          nullable: true
        username:
          type: string
          nullable: true
      x-class-extra-annotation: '@com.fasterxml.jackson.annotation.JsonInclude(com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL)'
    CitizenRequestPage:
      allOf:
        - $ref: '#/components/schemas/Page'
        - type: object
          properties:
            content:
              default: []
              items:
                $ref: '#/components/schemas/CitizenRequest'
    CitizenInfo:
      type: object
      properties:
        id:
          deprecated: true
          description: Was never implemented.
          type: number
          format: int64
          nullable: true
        ssin:
          type: string
        numPens:
          type: number
        lastName:
          type: string
        firstName:
          type: string
        address:
          type: string
          description: the address line, see addressObj for more info
        postalCode:
          type: string
          deprecated: true
          description: use the addressObj instead
        addressObj:
          $ref: '#/components/schemas/ForeignAddress'
        numBox:
          type: number
          format: int32
        OP:
          type: number
        unemploymentOffice:
          type: number
        iban:
          type: string
          nullable: true
          deprecated: true
          description: use the bankAccount instead
        bankAccount:
          $ref: '#/components/schemas/BankAccount'
        paymentMode:
          type: integer
          description: List of possible values http://services/lookupwppt/lookups/signaletic/SignaleticPaymentWayList.seam
        language:
          type: string
          nullable: true
        sex:
          type: string
        flagNation:
          type: number
        flagVCpte:
          deprecated: true
          description: Was never implemented.
          type: number
          nullable: true
        email:
          type: string
          nullable: true
        emailReg:
          type: string
          nullable: true
        flagToPurge:
          type: string
        lastModifDate:
          type: integer
        telephoneOnem:
          type: string
          nullable: true
        gsmOnem:
          type: string
          nullable: true
        telephoneReg:
          type: string
          nullable: true
        gsmReg:
          type: string
          nullable: true
        birthDate:
          type: string
          format: date
          nullable: true
        deceasedDate:
          type: string
          format: date
          nullable: true
        bisNumber:
          nullable: true
          type: array
          items:
            type: string
        employmentContract:
          type: integer
          description: Type of employment contract. List of possible values http://services/lookupwppt/lookups/signaletic/SignaleticWorkContractTypeList.seam
        unionDue:
          description: The citizen mandates or not the union to pay the union dues with his allowance money. If no mandate was ever found this will be null.
          type: object
          nullable: true
          required:
            - mandateActive
            - validFrom
          properties:
            mandateActive:
              type: boolean
              description: Is the union dues mandate active or not.
            validFrom:
              type: string
              format: date
              description: The date on which the mandate was activated or deactivated.

    CitizenInfoV2:
      deprecated: true
      type: object
      properties:
        dateMcpte:
          type: string
          format: date
          nullable: true
        communeDateValid:
          type: string
          format: date
          nullable: true
        nationDateValid:
          type: string
          format: date
          nullable: true
        nationBcss:
          type: number
        id:
          type: number
          format: int64
          nullable: true
        ssin:
          type: string
        numPens:
          type: number
        lastName:
          type: string
        firstName:
          type: string
        address:
          type: string
        postalCode:
          type: string
        rvaCountryCode:
          type: integer
          nullable: false
        numBox:
          type: number
          format: int32
        OP:
          type: number
        unemploymentOffice:
          type: number
        iban:
          type: string
        language:
          type: string
          nullable: true
        sex:
          type: string
        flagPurge:
          type: string
        flagNation:
          type: number
        flagVCpte:
          type: number
        email:
          type: string
          nullable: true
        telephoneOnem:
          type: string
          nullable: true
        gsmOnem:
          type: string
          nullable: true
        telephoneReg:
          type: string
          nullable: true
        gsmReg:
          type: string
          nullable: true
        deceasedDate:
          type: string
          format: date
          nullable: true
        bisNumber:
          type: array
          items:
            type: string
          nullable: true

    CitizenInfoPage:
      allOf:
        - $ref: '#/components/schemas/Page'
        - type: object
          properties:
            content:
              default: []
              items:
                $ref: '#/components/schemas/CitizenInfo'
    DataReturned:
      type: string
      enum:
        - FULL
        - SUMMARY
    CitizenInfoPageV2:
      deprecated: true
      allOf:
        - $ref: '#/components/schemas/Page'
        - type: object
          properties:
            content:
              default: []
              items:
                $ref: '#/components/schemas/CitizenInfoV2'
    Address:
      type: object
      required:
        - street
        - zip
        - city
      properties:
        street:
          type: string
          nullable: false
          maxLength: 30
          description: 'Street name and number'
        number:
          type: string
          maxLength: 10
          description: 'House number'
        box:
          type: string
          maxLength: 10
          description: 'Postal box'
        zip:
          type: string
          nullable: false
          maxLength: 10
          description: 'Postal code (4 digits for Belgian addresses, up to 10 characters for foreign addresses)'
          example: '1000'
        city:
          type: string
          nullable: false
          maxLength: 35
          description: 'City name'
        countryCode:
          type: integer
          minimum: 1
          maximum: 999
          description: 'Country code (1 for Belgium, see country lookup service for others)'
          default: 1
          example: 1

    BankAccount:
      type: object
      properties:
        iban:
          type: string
        bic:
          type: string
          nullable: true
        holder:
          type: string
          description: The name of the bank account holder
          nullable: true
        validFrom:
          type: string
          format: date

    ForeignAddress:
      type: object
      required:
        - street
        - zip
        - city
        - countryCode
        - validFrom
      properties:
        street:
          type: string
        number:
          type: string
          nullable: true
        box:
          type: string
          nullable: true
        zip:
          type: string
        city:
          type: string
          nullable: true
        countryCode:
          type: integer
          description: NEO country code. List of possible values http://services/lookupwppt/lookups/common/CountryList.seam
        validFrom:
          type: string
          format: date

    Language:
      type: integer
      enum:
        - 1
        - 2
        - 3
      x-enum-varnames:
        - fr
        - nl
        - de

    IsoLanguage:
      type: string
      enum:
        - de
        - fr
        - nl

    PaymentType:
      type: string
      enum:
        - BANK_TRANSFER
        - CIRCULAR_CHEQUE
        - OTHER_BANK_TRANSFER

    Sex:
      type: integer
      enum:
        - 0
        - 1

      x-enum-varnames:
        - f
        - m

    Flag:
      type: integer
      enum:
        - 0
        - 1

      x-enum-varnames:
        - n
        - y

    BankUpdateRequest:
      type: object
      description: Bank information update request
      required:
        - iban
        - paymentType
        - validFrom
      properties:
        iban:
          type: string
          pattern: '^[A-Z]{2}[0-9]{2}[A-Z0-9]{1,30}$'
          maxLength: 34
          description: 'International Bank Account Number'
          example: '****************'
        bic:
          type: string
          pattern: '^[A-Z0-9]{8,11}$'
          maxLength: 11
          description: 'Bank Identifier Code (mandatory for non-Belgian IBANs)'
          example: 'BBRUBEBCXXX'
        accountHolder:
          type: string
          maxLength: 30
          description: 'Account holder name'
          example: 'DUPONT JACQUES'
        paymentType:
          $ref: '#/components/schemas/PaymentType'
          description: 'Payment method'
        validFrom:
          type: string
          format: date
          description: 'Valid from date for bank information'

    UnionDueUpdateRequest:
      type: object
      description: Union due mandate update request
      required:
        - unionDue
      properties:
        unionDue:
          type: boolean
          description: 'Union due mandate active (true) or inactive (false)'
        validFrom:
          type: string
          format: date
          description: 'Valid from date for union due mandate (optional, uses main valueDate if not provided)'

    SignaleticUpdateRequest:
      type: object
      description: Signaletic data update request (address and personal information)
      properties:
        address:
          $ref: '#/components/schemas/Address'
          description: 'Address information'
        birthDate:
          type: string
          pattern: '^\d{8}$'
          description: 'Birth date in YYYYMMDD format'
          example: '********'
        languageCode:
          type: integer
          minimum: 1
          maximum: 3
          description: 'Language code: 1=fr, 2=nl, 3=de'
        nationalityCode:
          type: integer
          minimum: 100
          maximum: 999
          description: 'CBSS Nationality code'
        valueDate:
          type: string
          format: date
          description: 'Value date for signaletic data (optional, uses main valueDate if not provided)'

  parameters:
    queryParam:
      in: query
      name: query
      required: false
      description: |-
        Uses RSQL syntax to search through citizens requests (See: https://molgenis.gitbooks.io/molgenis/content/guide-rsql.html")
      schema:
        type: string
      examples:
        200_OKAY:
          value: 'firstname==^LASTNAME1*;lastname=ilike=EL;lastname=ilike=MUSTAPHA'
    pageParam:
      in: query
      name: pageNumber
      schema:
        type: integer
        minimum: 0
        default: 0
      examples:
        200_OKAY:
          value: 0
    sizeParam:
      in: query
      name: pageSize
      schema:
        type: integer
        default: 10
      examples:
        200_OKAY:
          value: 10
    sortParam:
      in: query
      name: sort
      required: false
      description: |-
        The format 'property,property(,ASC|DESC)(,IgnoreCase)'. 
        Use multiple sort parameters if you want to switch direction or case sensitivity.
        Example: 'sort=firstname&sort=lastname,asc&sort=city,ignorecase'
      schema:
        type: string
      examples:
        200_OKAY:
          value: 'lastname,asc'

  examples:
    CITIZEN_123:
      value:
        pageNumber: 0
        pageSize: 10
        totalPage: 1
        totalElements: 1
        isFirst: true
        isLast: true
        content:
          - ssin: '88080880833'
            numPens: *********
            lastName: Thumas
            firstName: Marie
            address: Kerkstraat 12B
            postalCode: '1030'
            addressObj:
              street: Kerkstraat
              number: 12B
              box: null
              zip: '1030'
              city: null
              countryCode: 1
              validFrom: '2005-11-21'
            numBox: 123
            OP: 3111
            unemploymentOffice: 11
            iban: '****************'
            bankAccount:
              iban: '****************'
              bic: 'GKCCBEBB'
              holder: null
              validFrom: '2004-01-14'
            paymentMode: 1
            language: 'fr'
            sex: 'f'
            flagNation: 150
            flagPurge: 'n'
            deceasedDate: 2000-01-01
            lastModifDate: ********
            employmentContract: 2
            unionDue:
              mandateActive: true
              validFrom: 2005-09-01
            bisNumber:
              - '***********'
