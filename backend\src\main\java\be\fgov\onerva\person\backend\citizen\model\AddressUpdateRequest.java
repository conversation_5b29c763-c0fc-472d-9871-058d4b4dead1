package be.fgov.onerva.person.backend.citizen.model;

import jakarta.persistence.Column;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Getter
@Builder
@ToString
@NoArgsConstructor(access = AccessLevel.PACKAGE)
@AllArgsConstructor(access = AccessLevel.PACKAGE)
public class AddressUpdateRequest {
  @NotBlank
  private String street;

  private String number;

  private String box;
  @Column(length = 10)
  private String zip;
  @NotBlank
  @Column(length = 35)
  private String city;
  @Min(1)
  @Max(999)
  private Integer countryCode;
}
