


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > RestExceptionHandler</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.web</a>
</div>

<h1>Coverage Summary for Class: RestExceptionHandler (be.fgov.onerva.person.backend.web)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">RestExceptionHandler</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    77.8%
  </span>
  <span class="absValue">
    (7/9)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    25%
  </span>
  <span class="absValue">
    (1/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    68%
  </span>
  <span class="absValue">
    (17/25)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.web;
&nbsp;
&nbsp;import jakarta.validation.ConstraintViolationException;
&nbsp;import lombok.extern.slf4j.Slf4j;
&nbsp;import org.apache.commons.lang3.exception.ExceptionUtils;
&nbsp;import org.springframework.http.*;
&nbsp;import org.springframework.http.converter.HttpMessageNotReadableException;
&nbsp;import org.springframework.web.bind.MethodArgumentNotValidException;
&nbsp;import org.springframework.web.bind.annotation.ExceptionHandler;
&nbsp;import org.springframework.web.bind.annotation.RestControllerAdvice;
&nbsp;import org.springframework.web.context.request.WebRequest;
&nbsp;import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;
&nbsp;
&nbsp;import java.util.List;
&nbsp;
<b class="fc">&nbsp;@Slf4j</b>
&nbsp;@RestControllerAdvice
<b class="fc">&nbsp;public class RestExceptionHandler extends ResponseEntityExceptionHandler {</b>
&nbsp;
&nbsp;    @ExceptionHandler({
&nbsp;            IllegalArgumentException.class,
&nbsp;            UnsupportedOperationException.class
&nbsp;    })
&nbsp;    ResponseEntity&lt;Object&gt; handleRuntimeException(RuntimeException e, WebRequest req) {
<b class="fc">&nbsp;        var pd = createProblemDetail(e, HttpStatus.BAD_REQUEST, e.getMessage(), null, null, req);</b>
<b class="fc">&nbsp;        return handleExceptionInternal(e, pd, new HttpHeaders(), HttpStatus.BAD_REQUEST, req);</b>
&nbsp;    }
&nbsp;
&nbsp;    @Override
&nbsp;    protected ResponseEntity&lt;Object&gt; handleMethodArgumentNotValid(MethodArgumentNotValidException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
<b class="nc">&nbsp;        Object[] details = ex.getDetailMessageArguments();</b>
<b class="nc">&nbsp;        ex.getBody().setProperty(&quot;global&quot;, details[0]);</b>
<b class="nc">&nbsp;        ex.getBody().setProperty(&quot;fields&quot;, details[1]);</b>
<b class="nc">&nbsp;        return handleExceptionInternal(ex, null, headers, status, request);</b>
&nbsp;    }
&nbsp;
&nbsp;    @Override
&nbsp;    protected ResponseEntity&lt;Object&gt; handleHttpMessageNotReadable(HttpMessageNotReadableException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
<b class="fc">&nbsp;        ProblemDetail body = createProblemDetail(ex, status, ex.getMessage(), null, null, request);</b>
&nbsp;
<b class="fc">&nbsp;        return handleExceptionInternal(ex, body, headers, status, request);</b>
&nbsp;    }
&nbsp;
&nbsp;    @ExceptionHandler
&nbsp;    ResponseEntity&lt;Object&gt; handleConstraintViolationException(ConstraintViolationException cve, WebRequest req) {
<b class="fc">&nbsp;        List&lt;String&gt; violations = cve.getConstraintViolations().stream().map(</b>
<b class="fc">&nbsp;                cv -&gt; cv.getPropertyPath() + &quot;: &quot; + cv.getMessage()</b>
<b class="fc">&nbsp;        ).toList();</b>
<b class="fc">&nbsp;        var pd = createProblemDetail(cve, HttpStatus.BAD_REQUEST, cve.getMessage(), null, null, req);</b>
<b class="fc">&nbsp;        pd.setTitle(&quot;Constraint Violation&quot;);</b>
<b class="fc">&nbsp;        pd.setProperty(&quot;violations&quot;, violations);</b>
&nbsp;
<b class="fc">&nbsp;        return handleExceptionInternal(cve, pd, new HttpHeaders(), HttpStatus.BAD_REQUEST, req);</b>
&nbsp;    }
&nbsp;
&nbsp;    @ExceptionHandler
&nbsp;    ResponseEntity&lt;Object&gt; catchAllException(Exception ex, WebRequest req) {
<b class="nc">&nbsp;        var pd = createProblemDetail(ex, HttpStatus.INTERNAL_SERVER_ERROR, ex.getMessage(), null, null, req);</b>
&nbsp;
<b class="nc">&nbsp;        return handleExceptionInternal(ex, pd, new HttpHeaders(), HttpStatus.INTERNAL_SERVER_ERROR, req);</b>
&nbsp;    }
&nbsp;
&nbsp;    @Override
&nbsp;    protected ResponseEntity&lt;Object&gt; handleExceptionInternal(Exception ex, Object body, HttpHeaders headers, HttpStatusCode statusCode, WebRequest request) {
<b class="fc">&nbsp;        Throwable rootCause = ExceptionUtils.getRootCause(ex);</b>
<b class="pc">&nbsp;        if (statusCode.is4xxClientError()) {</b>
<b class="fc">&nbsp;            log.info(rootCause.getMessage(), rootCause);</b>
<b class="nc">&nbsp;        } else if (statusCode.is5xxServerError()) {</b>
<b class="nc">&nbsp;            log.error(rootCause.getMessage(), rootCause);</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        return super.handleExceptionInternal(ex, body, headers, statusCode, request);</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
