


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > AddressUpdateRequest</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.citizen.model</a>
</div>

<h1>Coverage Summary for Class: AddressUpdateRequest (be.fgov.onerva.person.backend.citizen.model)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
</tr>
<tr>
  <td class="name">AddressUpdateRequest</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.citizen.model;
&nbsp;
&nbsp;import jakarta.persistence.Column;
&nbsp;import jakarta.validation.constraints.Max;
&nbsp;import jakarta.validation.constraints.Min;
&nbsp;import jakarta.validation.constraints.NotBlank;
&nbsp;import lombok.AccessLevel;
&nbsp;import lombok.AllArgsConstructor;
&nbsp;import lombok.Builder;
&nbsp;import lombok.Getter;
&nbsp;import lombok.NoArgsConstructor;
&nbsp;import lombok.ToString;
&nbsp;
&nbsp;@Getter
&nbsp;@Builder
&nbsp;@ToString
&nbsp;@NoArgsConstructor(access = AccessLevel.PACKAGE)
&nbsp;@AllArgsConstructor(access = AccessLevel.PACKAGE)
&nbsp;public class AddressUpdateRequest {
&nbsp;  @NotBlank
&nbsp;  private String street;
&nbsp;
&nbsp;  private String number;
&nbsp;
&nbsp;  private String box;
&nbsp;  @Column(length = 10)
&nbsp;  private String zip;
&nbsp;  @NotBlank
&nbsp;  @Column(length = 35)
&nbsp;  private String city;
&nbsp;  @Min(1)
&nbsp;  @Max(999)
&nbsp;  private Integer countryCode;
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
