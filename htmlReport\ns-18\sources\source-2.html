


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > PersonMfxResponse</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.request.model</a>
</div>

<h1>Coverage Summary for Class: PersonMfxResponse (be.fgov.onerva.person.backend.request.model)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">PersonMfxResponse</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (10/10)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    78.3%
  </span>
  <span class="absValue">
    (47/60)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    81.8%
  </span>
  <span class="absValue">
    (63/77)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.request.model;
&nbsp;
&nbsp;import lombok.AccessLevel;
&nbsp;import lombok.Builder;
&nbsp;import lombok.Getter;
&nbsp;import lombok.ToString;
&nbsp;
&nbsp;import static be.fgov.onerva.person.backend.request.model.PersonRequest.*;
&nbsp;
&nbsp;@Builder(access = AccessLevel.PRIVATE)
&nbsp;@Getter
&nbsp;@ToString
&nbsp;public class PersonMfxResponse {
&nbsp;
&nbsp;    static final int TYPE_LENGTH = 4;
&nbsp;
&nbsp;    private PersonRequestType type;
&nbsp;    private long id;
&nbsp;    private String inss;
&nbsp;    private String names;
&nbsp;    private int errorCode;
&nbsp;    private boolean v1;
&nbsp;
&nbsp;    public String getErrorMessage() {
<b class="fc">&nbsp;        return type == PersonRequestType.CREATE ? createErrorCodes() : updateErrorCodes();</b>
&nbsp;    }
&nbsp;
&nbsp;    String updateErrorCodes() {
<b class="pc">&nbsp;        return switch (errorCode) {</b>
<b class="nc">&nbsp;            case 0 -&gt; null;</b>
<b class="fc">&nbsp;            case -1 -&gt; &quot;NISS must be numeric&quot;;</b>
<b class="fc">&nbsp;            case -2 -&gt; &quot;Check digit NISS not correct&quot;;</b>
<b class="fc">&nbsp;            case -3 -&gt; &quot;Functional code must be: 0001 or 0002&quot;;</b>
<b class="fc">&nbsp;            case -4 -&gt; &quot;The person must be known in MFX (keybox_ds)&quot;;</b>
<b class="fc">&nbsp;            case -300 -&gt; &quot;VALUE DATE is not a valid date&quot;;</b>
<b class="fc">&nbsp;            case -1003 -&gt; &quot;The derived pension number from NISS must be known in MFX (keybox_ds)&quot;;</b>
<b class="fc">&nbsp;            case -1005 -&gt; &quot;Data inconsistency in MFX : the person is known in keybox_ds but not in general_ds&quot;;</b>
<b class="fc">&nbsp;            case -1100 -&gt; &quot;The street name is mandatory&quot;;</b>
<b class="fc">&nbsp;            case -1101 -&gt; &quot;The zip code is mandatory&quot;;</b>
<b class="fc">&nbsp;            case -1102 -&gt; &quot;The zip code must be known in the lookup table&quot;;</b>
&nbsp;            case -1103 -&gt;
<b class="fc">&nbsp;                &quot;The NIS code of the new address has been found, but the unemployment office number for this address is not available in the lookup table&quot;;</b>
<b class="fc">&nbsp;            case -1104 -&gt; &quot;The union due must be either true or false&quot;;</b>
<b class="fc">&nbsp;            case -1105 -&gt; &quot;Data inconsistency in MFX: the person is not found in general_ds.&quot;;</b>
<b class="fc">&nbsp;            case -1106 -&gt; &quot;The payment type is not correct&quot;;</b>
&nbsp;            case -1108 -&gt;
<b class="fc">&nbsp;                &quot;The value date must be aligned with the value date of the unemployement file in sectop_ds, commune_ds and siggen_ds.&quot;;</b>
&nbsp;            case -1110 -&gt;
<b class="fc">&nbsp;                &quot;The value date must be aligned with the value date of the unemployement file in sectop_ds, commune_ds and siggen_ds.&quot;;</b>
&nbsp;            case -1111 -&gt;
<b class="fc">&nbsp;                &quot;The value date must be aligned with the value date of the unemployement file in sectop_ds, commune_ds and siggen_ds.&quot;;</b>
<b class="fc">&nbsp;            case -1112 -&gt; &quot;The nationality code must be known in the lookup table&quot;;</b>
&nbsp;            case -1113 -&gt;
<b class="fc">&nbsp;                &quot;The NIS code of the new address must be linked to the same unemployement office than the previous address&quot;;</b>
&nbsp;            // New error codes for foreign addresses
<b class="nc">&nbsp;            case -1120 -&gt; &quot;Country code is required for foreign addresses&quot;;</b>
<b class="nc">&nbsp;            case -1121 -&gt; &quot;Country code must be known in the lookup table&quot;;</b>
<b class="nc">&nbsp;            case -1122 -&gt; &quot;City is required for foreign addresses&quot;;</b>
<b class="nc">&nbsp;            case -1123 -&gt; &quot;Foreign zip code format is invalid&quot;;</b>
&nbsp;            // New error codes for bank information
<b class="nc">&nbsp;            case -1130 -&gt; &quot;IBAN format is invalid&quot;;</b>
<b class="nc">&nbsp;            case -1131 -&gt; &quot;IBAN checksum validation failed&quot;;</b>
<b class="nc">&nbsp;            case -1132 -&gt; &quot;BIC is required for non-Belgian IBANs&quot;;</b>
<b class="nc">&nbsp;            case -1133 -&gt; &quot;BIC format is invalid&quot;;</b>
<b class="nc">&nbsp;            case -1134 -&gt; &quot;Account holder name is required&quot;;</b>
<b class="nc">&nbsp;            case -1135 -&gt; &quot;Bank value date is invalid&quot;;</b>
&nbsp;            // New error codes for language and birth date
<b class="nc">&nbsp;            case -1140 -&gt; &quot;Language code is invalid (must be 1, 2, or 3)&quot;;</b>
<b class="nc">&nbsp;            case -1141 -&gt; &quot;Birth date format is invalid (must be YYYYMMDD)&quot;;</b>
&nbsp;            // Technical errors
<b class="fc">&nbsp;            case -9105 -&gt; &quot;Technical error in MFX&quot;;</b>
<b class="fc">&nbsp;            case -9003 -&gt; &quot;Technical error in MFX&quot;;</b>
<b class="fc">&nbsp;            default -&gt; &quot;Unknown code: &quot; + errorCode;</b>
&nbsp;        };
&nbsp;    }
&nbsp;
&nbsp;    String createErrorCodes() {
<b class="pc">&nbsp;        return switch (errorCode) {</b>
<b class="fc">&nbsp;            case -1 -&gt; &quot;Invalid INSS&quot;;</b>
<b class="fc">&nbsp;            case -2 -&gt; &quot;Citizen already exists&quot;;</b>
<b class="fc">&nbsp;            case 1 -&gt; null;</b>
<b class="fc">&nbsp;            default -&gt; &quot;Unknown code: &quot; + errorCode;</b>
&nbsp;        };
&nbsp;    }
&nbsp;
&nbsp;    public boolean isSuccess() {
<b class="fc">&nbsp;        return type == PersonRequestType.CREATE ? errorCode == -2 || errorCode == 1 : errorCode == 0;</b>
&nbsp;    }
&nbsp;
&nbsp;    public static PersonMfxResponse from(String response) {
<b class="fc">&nbsp;        if (response.startsWith(UPDATE) || response.startsWith(CREATE)) {</b>
<b class="fc">&nbsp;            return PersonMfxResponse.builder()</b>
<b class="fc">&nbsp;                    .type(type(response))</b>
<b class="fc">&nbsp;                    .v1(false)</b>
<b class="fc">&nbsp;                    .id(id(response, false))</b>
<b class="fc">&nbsp;                    .names(names(response, false))</b>
<b class="fc">&nbsp;                    .inss(inss(response, false))</b>
<b class="fc">&nbsp;                    .errorCode(errorCode(response, false))</b>
<b class="fc">&nbsp;                    .build();</b>
&nbsp;        } else {
<b class="fc">&nbsp;            return PersonMfxResponse.builder()</b>
<b class="fc">&nbsp;                    .type(PersonRequestType.CREATE)</b>
<b class="fc">&nbsp;                    .v1(true)</b>
<b class="fc">&nbsp;                    .id(id(response, true))</b>
<b class="fc">&nbsp;                    .names(names(response, true))</b>
<b class="fc">&nbsp;                    .inss(inss(response, true))</b>
<b class="fc">&nbsp;                    .errorCode(errorCode(response, true))</b>
<b class="fc">&nbsp;                    .build();</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    static PersonRequestType type(String response) {
<b class="fc">&nbsp;        String typeCode = response.substring(0, TYPE_LENGTH);</b>
<b class="pc">&nbsp;        return switch (typeCode) {</b>
<b class="fc">&nbsp;            case CREATE -&gt; PersonRequestType.CREATE;</b>
<b class="fc">&nbsp;            case UPDATE -&gt; PersonRequestType.UPDATE;</b>
<b class="nc">&nbsp;            default -&gt; throw new IllegalArgumentException(&quot;Unknown type: &quot; + typeCode);</b>
&nbsp;        };
&nbsp;    }
&nbsp;
&nbsp;    static String names(String response, boolean v1) {
<b class="fc">&nbsp;        int start = v1 ? 0 : TYPE_LENGTH + ID_LENGTH;</b>
<b class="fc">&nbsp;        int end = start + NAMES_LENGTH;</b>
<b class="fc">&nbsp;        return response.substring(start, end).trim();</b>
&nbsp;    }
&nbsp;
&nbsp;    static String inss(String response, boolean v1) {
<b class="fc">&nbsp;        int start = v1 ? NAMES_LENGTH : TYPE_LENGTH + ID_LENGTH + NAMES_LENGTH;</b>
<b class="fc">&nbsp;        int end = start + INSS_LENGTH;</b>
<b class="fc">&nbsp;        return response.substring(start, end);</b>
&nbsp;    }
&nbsp;
&nbsp;    static long id(String response, boolean v1) {
<b class="fc">&nbsp;        int start = v1 ? NAMES_LENGTH + INSS_LENGTH : TYPE_LENGTH;</b>
<b class="fc">&nbsp;        int end = start + ID_LENGTH;</b>
<b class="fc">&nbsp;        return Long.parseLong(response.substring(start, end));</b>
&nbsp;    }
&nbsp;
&nbsp;    static int errorCode(String response, boolean v1) {
<b class="fc">&nbsp;        int start = v1 ? NAMES_LENGTH + INSS_LENGTH + ID_LENGTH : TYPE_LENGTH + ID_LENGTH + NAMES_LENGTH + INSS_LENGTH;</b>
<b class="fc">&nbsp;        int end = start + CODE_LENGTH;</b>
<b class="fc">&nbsp;        return Integer.parseInt(response.substring(start, end));</b>
&nbsp;    }
&nbsp;
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
