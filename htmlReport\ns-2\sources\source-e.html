


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenUpdateRequestDTO</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">backend.rest.model</a>
</div>

<h1>Coverage Summary for Class: CitizenUpdateRequestDTO (backend.rest.model)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">CitizenUpdateRequestDTO</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    75.8%
  </span>
  <span class="absValue">
    (25/33)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/24)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    43.7%
  </span>
  <span class="absValue">
    (31/71)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package backend.rest.model;
&nbsp;
&nbsp;import java.net.URI;
&nbsp;import java.util.Objects;
&nbsp;import backend.rest.model.BankUpdateRequestDTO;
&nbsp;import backend.rest.model.ForeignAddressDTO;
&nbsp;import backend.rest.model.UnionDueUpdateRequestDTO;
&nbsp;import com.fasterxml.jackson.annotation.JsonProperty;
&nbsp;import com.fasterxml.jackson.annotation.JsonCreator;
&nbsp;import com.fasterxml.jackson.annotation.JsonTypeName;
&nbsp;import java.time.LocalDate;
&nbsp;import org.springframework.format.annotation.DateTimeFormat;
&nbsp;import org.springframework.lang.Nullable;
&nbsp;import java.time.OffsetDateTime;
&nbsp;import jakarta.validation.Valid;
&nbsp;import jakarta.validation.constraints.*;
&nbsp;import org.hibernate.validator.constraints.*;
&nbsp;import io.swagger.v3.oas.annotations.media.Schema;
&nbsp;
&nbsp;
&nbsp;import java.util.*;
&nbsp;import jakarta.annotation.Generated;
&nbsp;
&nbsp;/**
&nbsp; * Citizen update request
&nbsp; */
&nbsp;
&nbsp;@Schema(name = &quot;CitizenUpdateRequest&quot;, description = &quot;Citizen update request&quot;)
&nbsp;@JsonTypeName(&quot;CitizenUpdateRequest&quot;)
&nbsp;@Generated(value = &quot;org.openapitools.codegen.languages.SpringCodegen&quot;, date = &quot;2025-07-16T14:11:30.627767400+02:00[Europe/Brussels]&quot;, comments = &quot;Generator version: 7.12.0&quot;)
&nbsp;public class CitizenUpdateRequestDTO {
&nbsp;
&nbsp;  private ForeignAddressDTO address;
&nbsp;
&nbsp;  private @Nullable Integer nationalityCode;
&nbsp;
&nbsp;  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
&nbsp;  private LocalDate validFrom;
&nbsp;
&nbsp;  private @Nullable String correlationId;
&nbsp;
&nbsp;  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
&nbsp;  private @Nullable LocalDate birthDate;
&nbsp;
&nbsp;  private @Nullable Integer unemploymentOffice;
&nbsp;
&nbsp;  private @Nullable Integer languageCode;
&nbsp;
&nbsp;  private @Nullable BankUpdateRequestDTO bankInfo;
&nbsp;
&nbsp;  private @Nullable UnionDueUpdateRequestDTO unionDueInfo;
&nbsp;
&nbsp;  public CitizenUpdateRequestDTO() {
<b class="fc">&nbsp;    super();</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Constructor with only required parameters
&nbsp;   */
<b class="nc">&nbsp;  public CitizenUpdateRequestDTO(ForeignAddressDTO address, LocalDate validFrom) {</b>
<b class="nc">&nbsp;    this.address = address;</b>
<b class="nc">&nbsp;    this.validFrom = validFrom;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenUpdateRequestDTO address(ForeignAddressDTO address) {
<b class="fc">&nbsp;    this.address = address;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get address
&nbsp;   * @return address
&nbsp;   */
&nbsp;  @NotNull @Valid 
&nbsp;  @Schema(name = &quot;address&quot;, requiredMode = Schema.RequiredMode.REQUIRED)
&nbsp;  @JsonProperty(&quot;address&quot;)
&nbsp;  public ForeignAddressDTO getAddress() {
<b class="fc">&nbsp;    return address;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setAddress(ForeignAddressDTO address) {
<b class="fc">&nbsp;    this.address = address;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenUpdateRequestDTO nationalityCode(Integer nationalityCode) {
<b class="fc">&nbsp;    this.nationalityCode = nationalityCode;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * CBSS Nationality code (see Lookups)
&nbsp;   * minimum: 100
&nbsp;   * maximum: 999
&nbsp;   * @return nationalityCode
&nbsp;   */
&nbsp;  @Min(100) @Max(999) 
&nbsp;  @Schema(name = &quot;nationalityCode&quot;, description = &quot;CBSS Nationality code (see Lookups)&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;nationalityCode&quot;)
&nbsp;  public Integer getNationalityCode() {
<b class="fc">&nbsp;    return nationalityCode;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setNationalityCode(Integer nationalityCode) {
<b class="fc">&nbsp;    this.nationalityCode = nationalityCode;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenUpdateRequestDTO validFrom(LocalDate validFrom) {
<b class="fc">&nbsp;    this.validFrom = validFrom;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get validFrom
&nbsp;   * @return validFrom
&nbsp;   */
&nbsp;  @NotNull @Valid 
&nbsp;  @Schema(name = &quot;validFrom&quot;, requiredMode = Schema.RequiredMode.REQUIRED)
&nbsp;  @JsonProperty(&quot;validFrom&quot;)
&nbsp;  public LocalDate getValidFrom() {
<b class="fc">&nbsp;    return validFrom;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setValidFrom(LocalDate validFrom) {
<b class="fc">&nbsp;    this.validFrom = validFrom;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenUpdateRequestDTO correlationId(String correlationId) {
<b class="fc">&nbsp;    this.correlationId = correlationId;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * An optional ID of your choice to correlate with.
&nbsp;   * @return correlationId
&nbsp;   */
&nbsp;  @Size(max = 50) 
&nbsp;  @Schema(name = &quot;correlationId&quot;, description = &quot;An optional ID of your choice to correlate with.&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;correlationId&quot;)
&nbsp;  public String getCorrelationId() {
<b class="fc">&nbsp;    return correlationId;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setCorrelationId(String correlationId) {
<b class="fc">&nbsp;    this.correlationId = correlationId;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenUpdateRequestDTO birthDate(LocalDate birthDate) {
<b class="nc">&nbsp;    this.birthDate = birthDate;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get birthDate
&nbsp;   * @return birthDate
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;birthDate&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;birthDate&quot;)
&nbsp;  public LocalDate getBirthDate() {
<b class="fc">&nbsp;    return birthDate;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setBirthDate(LocalDate birthDate) {
<b class="fc">&nbsp;    this.birthDate = birthDate;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenUpdateRequestDTO unemploymentOffice(Integer unemploymentOffice) {
<b class="nc">&nbsp;    this.unemploymentOffice = unemploymentOffice;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Unemployment office code
&nbsp;   * @return unemploymentOffice
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;unemploymentOffice&quot;, description = &quot;Unemployment office code&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;unemploymentOffice&quot;)
&nbsp;  public Integer getUnemploymentOffice() {
<b class="fc">&nbsp;    return unemploymentOffice;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setUnemploymentOffice(Integer unemploymentOffice) {
<b class="fc">&nbsp;    this.unemploymentOffice = unemploymentOffice;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenUpdateRequestDTO languageCode(Integer languageCode) {
<b class="nc">&nbsp;    this.languageCode = languageCode;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Language code: 1=fr, 2=nl, 3=de
&nbsp;   * minimum: 1
&nbsp;   * maximum: 3
&nbsp;   * @return languageCode
&nbsp;   */
&nbsp;  @Min(1) @Max(3) 
&nbsp;  @Schema(name = &quot;languageCode&quot;, description = &quot;Language code: 1=fr, 2=nl, 3=de&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;languageCode&quot;)
&nbsp;  public Integer getLanguageCode() {
<b class="fc">&nbsp;    return languageCode;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setLanguageCode(Integer languageCode) {
<b class="fc">&nbsp;    this.languageCode = languageCode;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenUpdateRequestDTO bankInfo(BankUpdateRequestDTO bankInfo) {
<b class="fc">&nbsp;    this.bankInfo = bankInfo;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get bankInfo
&nbsp;   * @return bankInfo
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;bankInfo&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;bankInfo&quot;)
&nbsp;  public BankUpdateRequestDTO getBankInfo() {
<b class="fc">&nbsp;    return bankInfo;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setBankInfo(BankUpdateRequestDTO bankInfo) {
<b class="fc">&nbsp;    this.bankInfo = bankInfo;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenUpdateRequestDTO unionDueInfo(UnionDueUpdateRequestDTO unionDueInfo) {
<b class="fc">&nbsp;    this.unionDueInfo = unionDueInfo;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get unionDueInfo
&nbsp;   * @return unionDueInfo
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;unionDueInfo&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;unionDueInfo&quot;)
&nbsp;  public UnionDueUpdateRequestDTO getUnionDueInfo() {
<b class="fc">&nbsp;    return unionDueInfo;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setUnionDueInfo(UnionDueUpdateRequestDTO unionDueInfo) {
<b class="fc">&nbsp;    this.unionDueInfo = unionDueInfo;</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public boolean equals(Object o) {
<b class="nc">&nbsp;    if (this == o) {</b>
<b class="nc">&nbsp;      return true;</b>
&nbsp;    }
<b class="nc">&nbsp;    if (o == null || getClass() != o.getClass()) {</b>
<b class="nc">&nbsp;      return false;</b>
&nbsp;    }
<b class="nc">&nbsp;    CitizenUpdateRequestDTO citizenUpdateRequest = (CitizenUpdateRequestDTO) o;</b>
<b class="nc">&nbsp;    return Objects.equals(this.address, citizenUpdateRequest.address) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.nationalityCode, citizenUpdateRequest.nationalityCode) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.validFrom, citizenUpdateRequest.validFrom) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.correlationId, citizenUpdateRequest.correlationId) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.birthDate, citizenUpdateRequest.birthDate) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.unemploymentOffice, citizenUpdateRequest.unemploymentOffice) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.languageCode, citizenUpdateRequest.languageCode) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.bankInfo, citizenUpdateRequest.bankInfo) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.unionDueInfo, citizenUpdateRequest.unionDueInfo);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public int hashCode() {
<b class="nc">&nbsp;    return Objects.hash(address, nationalityCode, validFrom, correlationId, birthDate, unemploymentOffice, languageCode, bankInfo, unionDueInfo);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public String toString() {
<b class="nc">&nbsp;    StringBuilder sb = new StringBuilder();</b>
<b class="nc">&nbsp;    sb.append(&quot;class CitizenUpdateRequestDTO {\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    address: &quot;).append(toIndentedString(address)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    nationalityCode: &quot;).append(toIndentedString(nationalityCode)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    validFrom: &quot;).append(toIndentedString(validFrom)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    correlationId: &quot;).append(toIndentedString(correlationId)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    birthDate: &quot;).append(toIndentedString(birthDate)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    unemploymentOffice: &quot;).append(toIndentedString(unemploymentOffice)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    languageCode: &quot;).append(toIndentedString(languageCode)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    bankInfo: &quot;).append(toIndentedString(bankInfo)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    unionDueInfo: &quot;).append(toIndentedString(unionDueInfo)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;}&quot;);</b>
<b class="nc">&nbsp;    return sb.toString();</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Convert the given object to string with each line indented by 4 spaces
&nbsp;   * (except the first line).
&nbsp;   */
&nbsp;  private String toIndentedString(Object o) {
<b class="nc">&nbsp;    if (o == null) {</b>
<b class="nc">&nbsp;      return &quot;null&quot;;</b>
&nbsp;    }
<b class="nc">&nbsp;    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</b>
&nbsp;  }
&nbsp;}
&nbsp;
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
