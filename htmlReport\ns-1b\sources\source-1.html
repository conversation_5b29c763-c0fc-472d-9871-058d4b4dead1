


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CountryCodeValidator</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.validation</a>
</div>

<h1>Coverage Summary for Class: CountryCodeValidator (be.fgov.onerva.person.backend.validation)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">CountryCodeValidator</td>
<td class="coverageStat">
  <span class="percent">
    57.1%
  </span>
  <span class="absValue">
    (4/7)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    13.6%
  </span>
  <span class="absValue">
    (3/22)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    31.6%
  </span>
  <span class="absValue">
    (12/38)
  </span>
</td>
</tr>
  <tr>
    <td class="name">CountryCodeValidator$ValidationResult</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/6)
  </span>
</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    36.4%
  </span>
  <span class="absValue">
    (4/11)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    13.6%
  </span>
  <span class="absValue">
    (3/22)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    27.3%
  </span>
  <span class="absValue">
    (12/44)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.validation;
&nbsp;
&nbsp;import be.fgov.onerva.person.backend.lookup.LookupClient;
&nbsp;import be.fgov.onerva.person.backend.lookup.model.LookupData;
&nbsp;import lombok.RequiredArgsConstructor;
&nbsp;import lombok.extern.slf4j.Slf4j;
&nbsp;import org.springframework.cache.annotation.Cacheable;
&nbsp;import org.springframework.stereotype.Component;
&nbsp;
&nbsp;import java.util.List;
&nbsp;import java.util.Set;
&nbsp;import java.util.stream.Collectors;
&nbsp;
&nbsp;/**
&nbsp; * Country code validator that uses the LookupClient to verify country codes
&nbsp; * against the CBSS nationality codes lookup service.
&nbsp; */
&nbsp;@Component
&nbsp;@RequiredArgsConstructor
<b class="fc">&nbsp;@Slf4j</b>
&nbsp;public class CountryCodeValidator {
&nbsp;
&nbsp;    private final LookupClient lookupClient;
&nbsp;
&nbsp;    /**
&nbsp;     * Validates a country code against the CBSS nationality codes lookup service.
&nbsp;     *
&nbsp;     * @param countryCode the country code to validate (can be null)
&nbsp;     * @return true if the country code is valid, false otherwise
&nbsp;     */
&nbsp;    public boolean isValid(Integer countryCode) {
<b class="pc">&nbsp;        if (countryCode == null) {</b>
<b class="nc">&nbsp;            return false;</b>
&nbsp;        }
&nbsp;
&nbsp;        try {
<b class="fc">&nbsp;            Set&lt;String&gt; validCodes = getValidCountryCodes();</b>
<b class="fc">&nbsp;            return validCodes.contains(countryCode.toString());</b>
&nbsp;        } catch (Exception e) {
<b class="nc">&nbsp;            log.error(&quot;Error validating country code {}: {}&quot;, countryCode, e.getMessage());</b>
<b class="nc">&nbsp;            return false;</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Validates a country code and provides detailed error information.
&nbsp;     *
&nbsp;     * @param countryCode the country code to validate
&nbsp;     * @return ValidationResult containing validation status and error details
&nbsp;     */
&nbsp;    public ValidationResult validateWithDetails(Integer countryCode) {
<b class="nc">&nbsp;        if (countryCode == null) {</b>
<b class="nc">&nbsp;            return new ValidationResult(false, &quot;Country code cannot be null&quot;);</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        if (countryCode &lt; 1 || countryCode &gt; 999) {</b>
<b class="nc">&nbsp;            return new ValidationResult(false, &quot;Country code must be between 1 and 999&quot;);</b>
&nbsp;        }
&nbsp;
&nbsp;        try {
<b class="nc">&nbsp;            Set&lt;String&gt; validCodes = getValidCountryCodes();</b>
<b class="nc">&nbsp;            if (validCodes.contains(countryCode.toString())) {</b>
<b class="nc">&nbsp;                return new ValidationResult(true, &quot;Country code is valid&quot;);</b>
&nbsp;            } else {
<b class="nc">&nbsp;                return new ValidationResult(false, </b>
<b class="nc">&nbsp;                    String.format(&quot;Country code %d is not found in CBSS nationality codes&quot;, countryCode));</b>
&nbsp;            }
&nbsp;        } catch (Exception e) {
<b class="nc">&nbsp;            log.error(&quot;Error validating country code {}: {}&quot;, countryCode, e.getMessage());</b>
<b class="nc">&nbsp;            return new ValidationResult(false, </b>
<b class="nc">&nbsp;                String.format(&quot;Error validating country code: %s&quot;, e.getMessage()));</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Checks if a country code represents Belgium.
&nbsp;     * Belgium is typically represented by country code 1 in CBSS.
&nbsp;     *
&nbsp;     * @param countryCode the country code to check
&nbsp;     * @return true if the country code represents Belgium, false otherwise
&nbsp;     */
&nbsp;    public boolean isBelgium(Integer countryCode) {
<b class="pc">&nbsp;        return countryCode != null &amp;&amp; countryCode == 1;</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Checks if a country code represents a foreign country (non-Belgium).
&nbsp;     *
&nbsp;     * @param countryCode the country code to check
&nbsp;     * @return true if the country code represents a foreign country, false otherwise
&nbsp;     */
&nbsp;    public boolean isForeign(Integer countryCode) {
<b class="nc">&nbsp;        return countryCode != null &amp;&amp; countryCode != 1 &amp;&amp; isValid(countryCode);</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Gets all valid country codes from the lookup service.
&nbsp;     * Results are cached to improve performance.
&nbsp;     *
&nbsp;     * @return Set of valid country code strings
&nbsp;     */
&nbsp;    @Cacheable(&quot;countryCodes&quot;)
&nbsp;    public Set&lt;String&gt; getValidCountryCodes() {
<b class="fc">&nbsp;        log.debug(&quot;Fetching country codes from lookup service&quot;);</b>
<b class="fc">&nbsp;        List&lt;LookupData&gt; nationalityCodes = lookupClient.findAllOnemCountryCodes();</b>
&nbsp;        
<b class="fc">&nbsp;        Set&lt;String&gt; codes = nationalityCodes.stream()</b>
<b class="fc">&nbsp;                .map(LookupData::getCode)</b>
<b class="fc">&nbsp;                .collect(Collectors.toSet());</b>
&nbsp;        
<b class="fc">&nbsp;        log.debug(&quot;Retrieved {} country codes from lookup service&quot;, codes.size());</b>
<b class="fc">&nbsp;        return codes;</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Gets the description for a country code in the specified language.
&nbsp;     *
&nbsp;     * @param countryCode the country code
&nbsp;     * @param language the language (&quot;fr&quot; for French, &quot;nl&quot; for Dutch)
&nbsp;     * @return the country description or null if not found
&nbsp;     */
&nbsp;    public String getCountryDescription(Integer countryCode, String language) {
<b class="nc">&nbsp;        if (countryCode == null) {</b>
<b class="nc">&nbsp;            return null;</b>
&nbsp;        }
&nbsp;
&nbsp;        try {
<b class="nc">&nbsp;            List&lt;LookupData&gt; nationalityCodes = lookupClient.findAllNationalityCodes();</b>
<b class="nc">&nbsp;            return nationalityCodes.stream()</b>
<b class="nc">&nbsp;                    .filter(lookup -&gt; lookup.getCode().equals(countryCode.toString()))</b>
<b class="nc">&nbsp;                    .findFirst()</b>
<b class="nc">&nbsp;                    .map(lookup -&gt; &quot;fr&quot;.equalsIgnoreCase(language) ? lookup.getDescFr() : lookup.getDescNl())</b>
<b class="nc">&nbsp;                    .orElse(null);</b>
&nbsp;        } catch (Exception e) {
<b class="nc">&nbsp;            log.error(&quot;Error getting country description for code {}: {}&quot;, countryCode, e.getMessage());</b>
<b class="nc">&nbsp;            return null;</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Result of country code validation with detailed information.
&nbsp;     */
&nbsp;    public static class ValidationResult {
&nbsp;        private final boolean valid;
&nbsp;        private final String message;
&nbsp;
<b class="nc">&nbsp;        public ValidationResult(boolean valid, String message) {</b>
<b class="nc">&nbsp;            this.valid = valid;</b>
<b class="nc">&nbsp;            this.message = message;</b>
&nbsp;        }
&nbsp;
&nbsp;        public boolean isValid() {
<b class="nc">&nbsp;            return valid;</b>
&nbsp;        }
&nbsp;
&nbsp;        public String getMessage() {
<b class="nc">&nbsp;            return message;</b>
&nbsp;        }
&nbsp;
&nbsp;        @Override
&nbsp;        public String toString() {
<b class="nc">&nbsp;            return String.format(&quot;ValidationResult{valid=%s, message=&#39;%s&#39;}&quot;, valid, message);</b>
&nbsp;        }
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
