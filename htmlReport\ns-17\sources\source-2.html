


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > PersonEventMapperImpl</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.request.mapper</a>
</div>

<h1>Coverage Summary for Class: PersonEventMapperImpl (be.fgov.onerva.person.backend.request.mapper)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">PersonEventMapperImpl</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    60%
  </span>
  <span class="absValue">
    (3/5)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    16.7%
  </span>
  <span class="absValue">
    (4/24)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    49%
  </span>
  <span class="absValue">
    (24/49)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.request.mapper;
&nbsp;
&nbsp;import be.fgov.onerva.person.backend.request.model.PersonMfxResponse;
&nbsp;import be.fgov.onerva.person.backend.request.model.PersonRequest;
&nbsp;import be.fgov.onerva.person.msg.v1.PersonCreated;
&nbsp;import be.fgov.onerva.person.msg.v1.PersonCreatedPayload;
&nbsp;import be.fgov.onerva.person.msg.v1.PersonUpdated;
&nbsp;import be.fgov.onerva.person.msg.v1.PersonUpdatedPayload;
&nbsp;import javax.annotation.processing.Generated;
&nbsp;
&nbsp;@Generated(
&nbsp;    value = &quot;org.mapstruct.ap.MappingProcessor&quot;,
&nbsp;    date = &quot;2025-07-16T14:12:59+0200&quot;,
&nbsp;    comments = &quot;version: 1.6.3, compiler: javac, environment: Java 21.0.6 (Eclipse Adoptium)&quot;
&nbsp;)
<b class="fc">&nbsp;public class PersonEventMapperImpl implements PersonEventMapper {</b>
&nbsp;
&nbsp;    @Override
&nbsp;    public PersonCreatedPayload mapToCloudEventCreate(PersonRequest request, PersonMfxResponse response) {
<b class="pc">&nbsp;        if ( request == null &amp;&amp; response == null ) {</b>
<b class="nc">&nbsp;            return null;</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        PersonCreatedPayload personCreatedPayload = new PersonCreatedPayload();</b>
&nbsp;
<b class="fc">&nbsp;        personCreatedPayload.setId( java.util.UUID.randomUUID().toString() );</b>
<b class="fc">&nbsp;        personCreatedPayload.setTime( java.time.LocalDateTime.now().toString() );</b>
<b class="fc">&nbsp;        personCreatedPayload.setSpecversion( &quot;1.0&quot; );</b>
<b class="fc">&nbsp;        personCreatedPayload.setSource( &quot;RVAONEM_PERSON_API&quot; );</b>
<b class="fc">&nbsp;        personCreatedPayload.setDatacontenttype( &quot;application/json&quot; );</b>
<b class="fc">&nbsp;        personCreatedPayload.setType( &quot;be.fgov.onerva.person.msg.v1.PersonCreated&quot; );</b>
<b class="fc">&nbsp;        personCreatedPayload.setData( mapToCreate(request, response) );</b>
&nbsp;
<b class="fc">&nbsp;        return personCreatedPayload;</b>
&nbsp;    }
&nbsp;
&nbsp;    @Override
&nbsp;    public PersonCreated mapToCreate(PersonRequest request, PersonMfxResponse response) {
<b class="pc">&nbsp;        if ( request == null &amp;&amp; response == null ) {</b>
<b class="nc">&nbsp;            return null;</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        PersonCreated personCreated = new PersonCreated();</b>
&nbsp;
<b class="pc">&nbsp;        if ( request != null ) {</b>
<b class="fc">&nbsp;            personCreated.setId( request.getId() );</b>
<b class="fc">&nbsp;            personCreated.setSsin( request.getNiss() );</b>
<b class="fc">&nbsp;            personCreated.setFirstname( request.getFirstname() );</b>
<b class="fc">&nbsp;            personCreated.setLastname( request.getLastname() );</b>
<b class="fc">&nbsp;            personCreated.setCorrelationId( request.getCorrelationId() );</b>
&nbsp;        }
<b class="pc">&nbsp;        if ( response != null ) {</b>
<b class="fc">&nbsp;            personCreated.setSuccess( response.isSuccess() );</b>
<b class="fc">&nbsp;            personCreated.setNames( response.getNames() );</b>
&nbsp;        }
<b class="fc">&nbsp;        personCreated.setStatus( personCreatedStatus(response.getErrorCode()) );</b>
&nbsp;
<b class="fc">&nbsp;        return personCreated;</b>
&nbsp;    }
&nbsp;
&nbsp;    @Override
&nbsp;    public PersonUpdatedPayload mapToCloudEventUpdate(PersonRequest request, PersonMfxResponse response) {
<b class="nc">&nbsp;        if ( request == null &amp;&amp; response == null ) {</b>
<b class="nc">&nbsp;            return null;</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        PersonUpdatedPayload personUpdatedPayload = new PersonUpdatedPayload();</b>
&nbsp;
<b class="nc">&nbsp;        personUpdatedPayload.setId( java.util.UUID.randomUUID().toString() );</b>
<b class="nc">&nbsp;        personUpdatedPayload.setTime( java.time.LocalDateTime.now().toString() );</b>
<b class="nc">&nbsp;        personUpdatedPayload.setSpecversion( &quot;1.0&quot; );</b>
<b class="nc">&nbsp;        personUpdatedPayload.setSource( &quot;RVAONEM_PERSON_API&quot; );</b>
<b class="nc">&nbsp;        personUpdatedPayload.setDatacontenttype( &quot;application/json&quot; );</b>
<b class="nc">&nbsp;        personUpdatedPayload.setType( &quot;be.fgov.onerva.person.msg.v1.PersonUpdated&quot; );</b>
<b class="nc">&nbsp;        personUpdatedPayload.setData( mapToUpdate(request, response) );</b>
&nbsp;
<b class="nc">&nbsp;        return personUpdatedPayload;</b>
&nbsp;    }
&nbsp;
&nbsp;    @Override
&nbsp;    public PersonUpdated mapToUpdate(PersonRequest request, PersonMfxResponse response) {
<b class="nc">&nbsp;        if ( request == null &amp;&amp; response == null ) {</b>
<b class="nc">&nbsp;            return null;</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        PersonUpdated personUpdated = new PersonUpdated();</b>
&nbsp;
<b class="nc">&nbsp;        if ( request != null ) {</b>
<b class="nc">&nbsp;            personUpdated.setId( request.getId() );</b>
<b class="nc">&nbsp;            personUpdated.setSsin( request.getNiss() );</b>
<b class="nc">&nbsp;            personUpdated.setCorrelationId( request.getCorrelationId() );</b>
&nbsp;        }
<b class="nc">&nbsp;        if ( response != null ) {</b>
<b class="nc">&nbsp;            personUpdated.setErrorCode( response.getErrorCode() );</b>
<b class="nc">&nbsp;            personUpdated.setSuccess( response.isSuccess() );</b>
<b class="nc">&nbsp;            personUpdated.setNames( response.getNames() );</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        return personUpdated;</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
