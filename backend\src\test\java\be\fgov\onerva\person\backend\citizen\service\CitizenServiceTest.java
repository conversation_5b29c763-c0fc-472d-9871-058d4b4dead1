package be.fgov.onerva.person.backend.citizen.service;

import be.fgov.onerva.common.utils.PensionNumberUtils;
import be.fgov.onerva.person.backend.citizen.model.AddressUpdateRequest;
import be.fgov.onerva.person.backend.citizen.model.BusinessDomain;
import be.fgov.onerva.person.backend.citizen.model.CitizenCreationRequest;
import be.fgov.onerva.person.backend.citizen.model.CitizenEntity;
import be.fgov.onerva.person.backend.citizen.model.CitizenUpdateRequest;
import be.fgov.onerva.person.backend.citizen.model.UnionDueUpdateInfo;
import be.fgov.onerva.person.backend.request.model.BankUpdateInfo;
import be.fgov.onerva.person.backend.citizen.persistence.CitizenRepository;
import be.fgov.onerva.person.backend.lookup.LookupClient;
import be.fgov.onerva.person.backend.request.service.PersonRequestService;
import be.fgov.onerva.person.backend.validation.CountryCodeValidator;
import be.fgov.onerva.person.backend.validation.IbanValidator;
import be.fgov.onerva.person.backend.validation.NationalityCodeValidator;
import be.fgov.onerva.wave.api.UserApi;
import be.fgov.onerva.wave.model.UserCriteria;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.web.ErrorResponseException;
import org.springframework.web.client.HttpClientErrorException;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CitizenServiceTest {
        @Mock
        private CitizenRepository citizenRepository;
        @Mock
        private PersonRequestService personRequestService;
        @Mock
        private LookupClient lookupClient;
        @Mock
        private UserApi userApi;
        @Mock
        private IbanValidator ibanValidator;
        @Mock
        private CountryCodeValidator countryCodeValidator;
        @Mock
        private NationalityCodeValidator nationalityCodeValidator;
        @InjectMocks
        private CitizenService citizenService;

        @Nested
        class CreateCitizen {
                @Test
                void createCitizenWithAllowanceIsUnsupported() {
                        CitizenCreationRequest request = CitizenCreationRequest.builder().allowance(true).build();

                        assertThrows(UnsupportedOperationException.class, () -> citizenService.createCitizen(request));
                }

                @Test
                void createCitizenOnlyForAdmissibilityDomain() {
                        CitizenCreationRequest request = CitizenCreationRequest.builder().allowance(false).build();

                        assertThrows(UnsupportedOperationException.class, () -> citizenService.createCitizen(request));
                }

                @Test
                void createCitizenWithInvalidNissIsForbidden() {
                        CitizenCreationRequest request = CitizenCreationRequest.builder()
                                        .allowance(false)
                                        .domain(BusinessDomain.ADMISSIBILITY)
                                        .niss("***********")
                                        .build();

                        assertThrows(IllegalArgumentException.class, () -> citizenService.createCitizen(request));
                }

                @Test
                void createCitizenOnlyNonExistingCitizen() {
                        CitizenCreationRequest request = CitizenCreationRequest.builder()
                                        .allowance(false)
                                        .domain(BusinessDomain.ADMISSIBILITY)
                                        .niss("***********")
                                        .firstname("John")
                                        .lastname("Doe")
                                        .build();

                        when(citizenRepository.existsById(any())).thenReturn(true);

                        assertThrows(CitizenExistsException.class, () -> citizenService.createCitizen(request));

                        var numPens = PensionNumberUtils.convertFromInss(***********L);
                        verify(citizenRepository).existsById(numPens);
                        verifyNoInteractions(personRequestService);
                }

                @Test
                void createCitizen() {
                        CitizenCreationRequest request = CitizenCreationRequest.builder()
                                        .allowance(false)
                                        .domain(BusinessDomain.ADMISSIBILITY)
                                        .niss("***********")
                                        .firstname("John")
                                        .lastname("Doe")
                                        .correlationId("my-uuid")
                                        .build();

                        when(citizenRepository.existsById(any())).thenReturn(false);

                        citizenService.createCitizen(request);

                        verify(personRequestService).createMinimalPersonInfo("John", "Doe", "***********", "my-uuid");
                }
        }

        @Nested
        class GetByNumbox {
                @Test
                void getByNumbox() {
                        assertThrows(CitizenNotFoundException.class, () -> citizenService.getByNumbox(404));

                        when(citizenRepository.findByNumBox(any())).thenReturn(List.of(CitizenEntity.builder()
                                        .id(2330009)
                                        .numBox(7113390)
                                        .lastId(1)
                                        .build(),
                                        CitizenEntity.builder().id(8330510).numBox(7113390).lastId(9).build()));

                        assertThat(citizenService.getByNumbox(7113390).getId()).isEqualTo(8330510);

                        verify(citizenRepository).findByNumBox(404);
                        verify(citizenRepository).findByNumBox(7113390);
                }
        }

        @Nested
        class UpdateCitizen {
                @Test
                void update_citizen_invalid_inss() {
                        var request = update().niss("102030001123").build();

                        var ex = assertThrows(IllegalArgumentException.class,
                                        () -> citizenService.updateCitizen(request));
                        assertThat(ex).hasMessage("Invalid inss: 102030001123");

                        verifyNoInteractions(citizenRepository, personRequestService, lookupClient, userApi);
                }

                @Test
                void update_citizen_not_found() {
                        var request = update().build();

                        assertThrows(CitizenNotFoundException.class, () -> citizenService.updateCitizen(request));

                        verify(citizenRepository).existsById(809010101);
                        verifyNoInteractions(personRequestService, lookupClient, userApi);
                }

                @Test
                void update_citizen_invalid_nationality_code() {
                        when(citizenRepository.existsById(any())).thenReturn(true);
                        when(nationalityCodeValidator.isValid(any(Integer.class))).thenReturn(false);

                        var request = update().nationalityCode(666).build();

                        var ex = assertThrows(IllegalArgumentException.class,
                                        () -> citizenService.updateCitizen(request));
                        assertThat(ex).hasMessage("Invalid nationality code: 666");

                        verify(citizenRepository).existsById(809010101);
                        verifyNoInteractions(personRequestService, userApi);
                }

                @Test
                void update_citizen_invalid_zip_code() {
                        when(citizenRepository.existsById(any())).thenReturn(true);
                        when(countryCodeValidator.isValid(any(Integer.class))).thenReturn(true);
                        when(countryCodeValidator.isBelgium(any(Integer.class))).thenReturn(true);

                        var request = update().address(AddressUpdateRequest.builder().countryCode(1).zip("XXX").build())
                                        .build();

                        var ex = assertThrows(IllegalArgumentException.class,
                                        () -> citizenService.updateCitizen(request));
                        assertThat(ex).hasMessage("Invalid Belgian zip code format: XXX. Must be 4 digits");

                        verify(citizenRepository).existsById(809010101);
                        verifyNoInteractions(personRequestService, userApi);
                }

                @Test
                void update_citizen_user_api_401() {
                        when(citizenRepository.existsById(any())).thenReturn(true);
                        when(userApi.searchUsers(any()))
                                        .thenThrow(HttpClientErrorException.create(HttpStatus.UNAUTHORIZED,
                                                        "Unauthorized",
                                                        new HttpHeaders(),
                                                        new byte[0],
                                                        null));

                        var request = update().build();

                        var ex = assertThrows(ErrorResponseException.class,
                                        () -> citizenService.updateCitizen(request));
                        assertThat(ex.getStatusCode()).isEqualTo(HttpStatus.UNAUTHORIZED);
                        assertThat(ex.getCause()).isInstanceOf(HttpClientErrorException.Unauthorized.class);

                        verify(citizenRepository).existsById(809010101);

                        verify(userApi).searchUsers(new UserCriteria().username("jdoe"));
                        verifyNoInteractions(personRequestService);
                }

                @Test
                void update_citizen_user_api_404() {
                        when(citizenRepository.existsById(any())).thenReturn(true);
                        when(userApi.searchUsers(any())).thenThrow(HttpClientErrorException.create(HttpStatus.NOT_FOUND,
                                        "Not Found",
                                        new HttpHeaders(),
                                        new byte[0],
                                        null));

                        var request = update().build();

                        var ex = assertThrows(IllegalArgumentException.class,
                                        () -> citizenService.updateCitizen(request));
                        assertThat(ex.getCause()).isInstanceOf(HttpClientErrorException.NotFound.class);
                        assertThat(ex.getMessage()).isEqualTo("Could not find user: jdoe");

                        verify(citizenRepository).existsById(809010101);
                        verifyNoInteractions(personRequestService);
                }

                CitizenUpdateRequest.CitizenUpdateRequestBuilder update() {
                        return CitizenUpdateRequest.builder()
                                        .address(AddressUpdateRequest.builder()
                                                        .street("Main street")
                                                        .number("1A")
                                                        .zip("1000")
                                                        .city("BXL")
                                                        .build())
                                        .niss("***********")
                                        .correlationId("my-bigint")
                                        .username("jdoe");
                }
        }

        @Nested
        @DisplayName("Zip Code Validation Tests")
        class ZipCodeValidationTests {

                @Test
                @DisplayName("Should not validate when address is null")
                void validateZipCode_addressNull_shouldNotThrow() {
                        when(citizenRepository.existsById(any())).thenReturn(true);

                        var request = CitizenUpdateRequest.builder()
                                        .niss("***********")
                                        .username("jdoe")
                                        .correlationId("test")
                                        .validFrom(LocalDate.now())
                                        .build();

                        // Should not throw any exception when address is null
                        citizenService.updateCitizen(request);

                        verify(citizenRepository).existsById(809010101);
                }

                @Test
                @DisplayName("Should not validate when zip is null")
                void validateZipCode_zipNull_shouldNotThrow() {
                        when(citizenRepository.existsById(any())).thenReturn(true);
                        when(countryCodeValidator.isValid(any(Integer.class))).thenReturn(true);

                        var request = CitizenUpdateRequest.builder()
                                        .niss("***********")
                                        .address(AddressUpdateRequest.builder()
                                                        .street("Main street")
                                                        .number("1A")
                                                        .city("BXL")
                                                        .countryCode(1)
                                                        .build())
                                        .username("jdoe")
                                        .correlationId("test")
                                        .validFrom(LocalDate.now())
                                        .build();

                        // Should not throw any exception when zip is null
                        citizenService.updateCitizen(request);

                        verify(citizenRepository).existsById(809010101);
                }

                @Test
                @DisplayName("Should throw exception for invalid Belgian zip code")
                void validateZipCode_invalidBelgianZip_shouldThrow() {
                        when(citizenRepository.existsById(any())).thenReturn(true);
                        when(countryCodeValidator.isValid(any(Integer.class))).thenReturn(true);
                        when(countryCodeValidator.isBelgium(any(Integer.class))).thenReturn(true);
                        when(lookupClient.findBelgianZipCode(anyInt())).thenReturn(Collections.emptyList());

                        var request = CitizenUpdateRequest.builder()
                                        .niss("***********")
                                        .address(AddressUpdateRequest.builder()
                                                        .street("Main street")
                                                        .number("1A")
                                                        .zip("9999")
                                                        .city("BXL")
                                                        .countryCode(1)
                                                        .build())
                                        .username("jdoe")
                                        .correlationId("test")
                                        .validFrom(LocalDate.now())
                                        .build();

                        var ex = assertThrows(IllegalArgumentException.class,
                                        () -> citizenService.updateCitizen(request));
                        assertThat(ex).hasMessage("Invalid Belgian zip code: 9999");

                        verify(lookupClient).findBelgianZipCode(9999);
                }

                @Test
                @DisplayName("Should throw exception for foreign zip code too long")
                void validateZipCode_foreignZipTooLong_shouldThrow() {
                        when(citizenRepository.existsById(any())).thenReturn(true);
                        when(countryCodeValidator.isValid(any(Integer.class))).thenReturn(true);
                        when(countryCodeValidator.isBelgium(any(Integer.class))).thenReturn(false);

                        var request = CitizenUpdateRequest.builder()
                                        .niss("***********")
                                        .address(AddressUpdateRequest.builder()
                                                        .street("Main street")
                                                        .number("1A")
                                                        .zip("***********") // 11 characters, exceeds 10 limit
                                                        .city("Paris")
                                                        .countryCode(2)
                                                        .build())
                                        .username("jdoe")
                                        .correlationId("test")
                                        .validFrom(LocalDate.now())
                                        .build();

                        var ex = assertThrows(IllegalArgumentException.class,
                                        () -> citizenService.updateCitizen(request));
                        assertThat(ex).hasMessage("Foreign zip code cannot exceed 10 characters: ***********");
                }
        }

        @Nested
        @DisplayName("Bank Information Validation Tests")
        class BankInfoValidationTests {

                @Test
                @DisplayName("Should throw exception for invalid IBAN")
                void validateBankInfo_invalidIban_shouldThrow() {
                        when(citizenRepository.existsById(any())).thenReturn(true);
                        when(countryCodeValidator.isValid(any(Integer.class))).thenReturn(true);
                        when(ibanValidator.isValid(anyString())).thenReturn(false);

                        var request = CitizenUpdateRequest.builder()
                                        .niss("***********")
                                        .address(AddressUpdateRequest.builder()
                                                        .street("Main street")
                                                        .number("1A")
                                                        .zip("1000")
                                                        .city("BXL")
                                                        .countryCode(1)
                                                        .build())
                                        .bankInfo(BankUpdateInfo.builder()
                                                        .iban("INVALID_IBAN")
                                                        .validFrom(LocalDate.now())
                                                        .build())
                                        .username("jdoe")
                                        .correlationId("test")
                                        .validFrom(LocalDate.now())
                                        .build();

                        var ex = assertThrows(IllegalArgumentException.class,
                                        () -> citizenService.updateCitizen(request));
                        assertThat(ex).hasMessage("Invalid IBAN: INVALID_IBAN");

                        verify(ibanValidator).isValid("INVALID_IBAN");
                }

                @Test
                @DisplayName("Should throw exception when BIC is mandatory for non-Belgian IBAN but missing")
                void validateBankInfo_bicMandatoryForNonBelgianIban_shouldThrow() {
                        when(citizenRepository.existsById(any())).thenReturn(true);
                        when(countryCodeValidator.isValid(any(Integer.class))).thenReturn(true);
                        when(ibanValidator.isValid(anyString())).thenReturn(true);
                        when(countryCodeValidator.isBelgium(any(Integer.class))).thenReturn(false);

                        var request = CitizenUpdateRequest.builder()
                                        .niss("***********")
                                        .address(AddressUpdateRequest.builder()
                                                        .street("Main street")
                                                        .number("1A")
                                                        .zip("75001")
                                                        .city("Paris")
                                                        .countryCode(2) // France
                                                        .build())
                                        .bankInfo(BankUpdateInfo.builder()
                                                        .iban("***************************")
                                                        .bic(null) // Missing BIC for non-Belgian IBAN
                                                        .validFrom(LocalDate.now())
                                                        .build())
                                        .username("jdoe")
                                        .correlationId("test")
                                        .validFrom(LocalDate.now())
                                        .build();

                        var ex = assertThrows(IllegalArgumentException.class,
                                        () -> citizenService.updateCitizen(request));
                        assertThat(ex).hasMessage("BIC is mandatory for non-Belgian IBANs");
                }

                @Test
                @DisplayName("Should throw exception when BIC is empty for non-Belgian IBAN")
                void validateBankInfo_bicEmptyForNonBelgianIban_shouldThrow() {
                        when(citizenRepository.existsById(any())).thenReturn(true);
                        when(countryCodeValidator.isValid(any(Integer.class))).thenReturn(true);
                        when(ibanValidator.isValid(anyString())).thenReturn(true);
                        when(countryCodeValidator.isBelgium(any(Integer.class))).thenReturn(false);

                        var request = CitizenUpdateRequest.builder()
                                        .niss("***********")
                                        .address(AddressUpdateRequest.builder()
                                                        .street("Main street")
                                                        .number("1A")
                                                        .zip("75001")
                                                        .city("Paris")
                                                        .countryCode(2) // France
                                                        .build())
                                        .bankInfo(BankUpdateInfo.builder()
                                                        .iban("***************************")
                                                        .bic("   ") // Empty BIC for non-Belgian IBAN
                                                        .validFrom(LocalDate.now())
                                                        .build())
                                        .username("jdoe")
                                        .correlationId("test")
                                        .validFrom(LocalDate.now())
                                        .build();

                        var ex = assertThrows(IllegalArgumentException.class,
                                        () -> citizenService.updateCitizen(request));
                        assertThat(ex).hasMessage("BIC is mandatory for non-Belgian IBANs");
                }
        }

        @Nested
        @DisplayName("Union Due Information Validation Tests")
        class UnionDueInfoValidationTests {

                @Test
                @DisplayName("Should throw exception when union due status is null")
                void validateUnionDueInfo_unionDueStatusNull_shouldThrow() {
                        when(citizenRepository.existsById(any())).thenReturn(true);
                        when(countryCodeValidator.isValid(any(Integer.class))).thenReturn(true);

                        var request = CitizenUpdateRequest.builder()
                                        .niss("***********")
                                        .address(AddressUpdateRequest.builder()
                                                        .street("Main street")
                                                        .number("1A")
                                                        .zip("1000")
                                                        .city("BXL")
                                                        .countryCode(1)
                                                        .build())
                                        .unionDueInfo(UnionDueUpdateInfo.builder()
                                                        .unionDue(null) // Null union due status
                                                        .validFrom(LocalDate.now())
                                                        .build())
                                        .username("jdoe")
                                        .correlationId("test")
                                        .validFrom(LocalDate.now())
                                        .build();

                        var ex = assertThrows(IllegalArgumentException.class,
                                        () -> citizenService.updateCitizen(request));
                        assertThat(ex).hasMessage("Union due status is required when union due info is provided");
                }

                @Test
                @DisplayName("Should throw exception when valid from date is null")
                void validateUnionDueInfo_validFromNull_shouldThrow() {
                        when(citizenRepository.existsById(any())).thenReturn(true);
                        when(countryCodeValidator.isValid(any(Integer.class))).thenReturn(true);

                        var request = CitizenUpdateRequest.builder()
                                        .niss("***********")
                                        .address(AddressUpdateRequest.builder()
                                                        .street("Main street")
                                                        .number("1A")
                                                        .zip("1000")
                                                        .city("BXL")
                                                        .countryCode(1)
                                                        .build())
                                        .unionDueInfo(UnionDueUpdateInfo.builder()
                                                        .unionDue(true)
                                                        .validFrom(null) // Null valid from date
                                                        .build())
                                        .username("jdoe")
                                        .correlationId("test")
                                        .validFrom(LocalDate.now())
                                        .build();

                        var ex = assertThrows(IllegalArgumentException.class,
                                        () -> citizenService.updateCitizen(request));
                        assertThat(ex).hasMessage("Valid from date is required for union due info");
                }
        }
}
