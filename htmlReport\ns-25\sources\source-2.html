


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > LookupData</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.lookup.model</a>
</div>

<h1>Coverage Summary for Class: LookupData (be.fgov.onerva.person.backend.lookup.model)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
</tr>
<tr>
  <td class="name">LookupData</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.lookup.model;
&nbsp;
&nbsp;import lombok.Getter;
&nbsp;import lombok.ToString;
&nbsp;import lombok.experimental.SuperBuilder;
&nbsp;import lombok.extern.jackson.Jacksonized;
&nbsp;
&nbsp;import java.time.LocalDate;
&nbsp;
&nbsp;@SuperBuilder
&nbsp;@Getter @ToString
&nbsp;@Jacksonized
&nbsp;public class LookupData {
&nbsp;    private int id;
&nbsp;    private String code;
&nbsp;    private String descFr;
&nbsp;    private String descNl;
&nbsp;    private LocalDate beginDate;
&nbsp;    private LocalDate endDate;
&nbsp;}
&nbsp;
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
