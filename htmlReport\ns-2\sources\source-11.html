


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > ForeignAddressDTO</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">backend.rest.model</a>
</div>

<h1>Coverage Summary for Class: ForeignAddressDTO (backend.rest.model)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">ForeignAddressDTO</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    85.2%
  </span>
  <span class="absValue">
    (23/27)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    45%
  </span>
  <span class="absValue">
    (9/20)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    64.6%
  </span>
  <span class="absValue">
    (42/65)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package backend.rest.model;
&nbsp;
&nbsp;import java.net.URI;
&nbsp;import java.util.Objects;
&nbsp;import com.fasterxml.jackson.annotation.JsonProperty;
&nbsp;import com.fasterxml.jackson.annotation.JsonCreator;
&nbsp;import com.fasterxml.jackson.annotation.JsonTypeName;
&nbsp;import java.time.LocalDate;
&nbsp;import org.springframework.format.annotation.DateTimeFormat;
&nbsp;import org.springframework.lang.Nullable;
&nbsp;import java.time.OffsetDateTime;
&nbsp;import jakarta.validation.Valid;
&nbsp;import jakarta.validation.constraints.*;
&nbsp;import org.hibernate.validator.constraints.*;
&nbsp;import io.swagger.v3.oas.annotations.media.Schema;
&nbsp;
&nbsp;
&nbsp;import java.util.*;
&nbsp;import jakarta.annotation.Generated;
&nbsp;
&nbsp;/**
&nbsp; * ForeignAddressDTO
&nbsp; */
&nbsp;
&nbsp;@JsonTypeName(&quot;ForeignAddress&quot;)
&nbsp;@Generated(value = &quot;org.openapitools.codegen.languages.SpringCodegen&quot;, date = &quot;2025-07-16T14:11:30.627767400+02:00[Europe/Brussels]&quot;, comments = &quot;Generator version: 7.12.0&quot;)
&nbsp;public class ForeignAddressDTO {
&nbsp;
&nbsp;  private String street;
&nbsp;
<b class="fc">&nbsp;  private @Nullable String number = null;</b>
&nbsp;
<b class="fc">&nbsp;  private @Nullable String box = null;</b>
&nbsp;
&nbsp;  private String zip;
&nbsp;
<b class="fc">&nbsp;  private String city = null;</b>
&nbsp;
&nbsp;  private Integer countryCode;
&nbsp;
&nbsp;  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
&nbsp;  private LocalDate validFrom;
&nbsp;
&nbsp;  public ForeignAddressDTO() {
<b class="fc">&nbsp;    super();</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Constructor with only required parameters
&nbsp;   */
<b class="nc">&nbsp;  public ForeignAddressDTO(String street, String zip, String city, Integer countryCode, LocalDate validFrom) {</b>
<b class="nc">&nbsp;    this.street = street;</b>
<b class="nc">&nbsp;    this.zip = zip;</b>
<b class="nc">&nbsp;    this.city = city;</b>
<b class="nc">&nbsp;    this.countryCode = countryCode;</b>
<b class="nc">&nbsp;    this.validFrom = validFrom;</b>
&nbsp;  }
&nbsp;
&nbsp;  public ForeignAddressDTO street(String street) {
<b class="fc">&nbsp;    this.street = street;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get street
&nbsp;   * @return street
&nbsp;   */
&nbsp;  @NotNull 
&nbsp;  @Schema(name = &quot;street&quot;, requiredMode = Schema.RequiredMode.REQUIRED)
&nbsp;  @JsonProperty(&quot;street&quot;)
&nbsp;  public String getStreet() {
<b class="fc">&nbsp;    return street;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setStreet(String street) {
<b class="fc">&nbsp;    this.street = street;</b>
&nbsp;  }
&nbsp;
&nbsp;  public ForeignAddressDTO number(String number) {
<b class="fc">&nbsp;    this.number = number;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get number
&nbsp;   * @return number
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;number&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;number&quot;)
&nbsp;  public String getNumber() {
<b class="fc">&nbsp;    return number;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setNumber(String number) {
<b class="fc">&nbsp;    this.number = number;</b>
&nbsp;  }
&nbsp;
&nbsp;  public ForeignAddressDTO box(String box) {
<b class="fc">&nbsp;    this.box = box;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get box
&nbsp;   * @return box
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;box&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;box&quot;)
&nbsp;  public String getBox() {
<b class="fc">&nbsp;    return box;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setBox(String box) {
<b class="fc">&nbsp;    this.box = box;</b>
&nbsp;  }
&nbsp;
&nbsp;  public ForeignAddressDTO zip(String zip) {
<b class="fc">&nbsp;    this.zip = zip;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get zip
&nbsp;   * @return zip
&nbsp;   */
&nbsp;  @NotNull 
&nbsp;  @Schema(name = &quot;zip&quot;, requiredMode = Schema.RequiredMode.REQUIRED)
&nbsp;  @JsonProperty(&quot;zip&quot;)
&nbsp;  public String getZip() {
<b class="fc">&nbsp;    return zip;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setZip(String zip) {
<b class="fc">&nbsp;    this.zip = zip;</b>
&nbsp;  }
&nbsp;
&nbsp;  public ForeignAddressDTO city(String city) {
<b class="fc">&nbsp;    this.city = city;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get city
&nbsp;   * @return city
&nbsp;   */
&nbsp;  @NotNull 
&nbsp;  @Schema(name = &quot;city&quot;, requiredMode = Schema.RequiredMode.REQUIRED)
&nbsp;  @JsonProperty(&quot;city&quot;)
&nbsp;  public String getCity() {
<b class="fc">&nbsp;    return city;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setCity(String city) {
<b class="fc">&nbsp;    this.city = city;</b>
&nbsp;  }
&nbsp;
&nbsp;  public ForeignAddressDTO countryCode(Integer countryCode) {
<b class="fc">&nbsp;    this.countryCode = countryCode;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * NEO country code. List of possible values http://services/lookupwppt/lookups/common/CountryList.seam
&nbsp;   * @return countryCode
&nbsp;   */
&nbsp;  @NotNull 
&nbsp;  @Schema(name = &quot;countryCode&quot;, description = &quot;NEO country code. List of possible values http://services/lookupwppt/lookups/common/CountryList.seam&quot;, requiredMode = Schema.RequiredMode.REQUIRED)
&nbsp;  @JsonProperty(&quot;countryCode&quot;)
&nbsp;  public Integer getCountryCode() {
<b class="fc">&nbsp;    return countryCode;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setCountryCode(Integer countryCode) {
<b class="fc">&nbsp;    this.countryCode = countryCode;</b>
&nbsp;  }
&nbsp;
&nbsp;  public ForeignAddressDTO validFrom(LocalDate validFrom) {
<b class="fc">&nbsp;    this.validFrom = validFrom;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get validFrom
&nbsp;   * @return validFrom
&nbsp;   */
&nbsp;  @NotNull @Valid 
&nbsp;  @Schema(name = &quot;validFrom&quot;, requiredMode = Schema.RequiredMode.REQUIRED)
&nbsp;  @JsonProperty(&quot;validFrom&quot;)
&nbsp;  public LocalDate getValidFrom() {
<b class="fc">&nbsp;    return validFrom;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setValidFrom(LocalDate validFrom) {
<b class="fc">&nbsp;    this.validFrom = validFrom;</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public boolean equals(Object o) {
<b class="pc">&nbsp;    if (this == o) {</b>
<b class="nc">&nbsp;      return true;</b>
&nbsp;    }
<b class="pc">&nbsp;    if (o == null || getClass() != o.getClass()) {</b>
<b class="nc">&nbsp;      return false;</b>
&nbsp;    }
<b class="fc">&nbsp;    ForeignAddressDTO foreignAddress = (ForeignAddressDTO) o;</b>
<b class="pc">&nbsp;    return Objects.equals(this.street, foreignAddress.street) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.number, foreignAddress.number) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.box, foreignAddress.box) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.zip, foreignAddress.zip) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.city, foreignAddress.city) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.countryCode, foreignAddress.countryCode) &amp;&amp;</b>
<b class="fc">&nbsp;        Objects.equals(this.validFrom, foreignAddress.validFrom);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public int hashCode() {
<b class="nc">&nbsp;    return Objects.hash(street, number, box, zip, city, countryCode, validFrom);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public String toString() {
<b class="nc">&nbsp;    StringBuilder sb = new StringBuilder();</b>
<b class="nc">&nbsp;    sb.append(&quot;class ForeignAddressDTO {\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    street: &quot;).append(toIndentedString(street)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    number: &quot;).append(toIndentedString(number)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    box: &quot;).append(toIndentedString(box)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    zip: &quot;).append(toIndentedString(zip)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    city: &quot;).append(toIndentedString(city)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    countryCode: &quot;).append(toIndentedString(countryCode)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    validFrom: &quot;).append(toIndentedString(validFrom)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;}&quot;);</b>
<b class="nc">&nbsp;    return sb.toString();</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Convert the given object to string with each line indented by 4 spaces
&nbsp;   * (except the first line).
&nbsp;   */
&nbsp;  private String toIndentedString(Object o) {
<b class="nc">&nbsp;    if (o == null) {</b>
<b class="nc">&nbsp;      return &quot;null&quot;;</b>
&nbsp;    }
<b class="nc">&nbsp;    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</b>
&nbsp;  }
&nbsp;}
&nbsp;
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
