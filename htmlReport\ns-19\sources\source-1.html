


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > LocalPersonResponseSimulator</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.request.service</a>
</div>

<h1>Coverage Summary for Class: LocalPersonResponseSimulator (be.fgov.onerva.person.backend.request.service)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">LocalPersonResponseSimulator</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/28)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.request.service;
&nbsp;
&nbsp;import be.fgov.onerva.person.backend.util.StringUtils;
&nbsp;import jakarta.jms.JMSException;
&nbsp;import jakarta.jms.TextMessage;
&nbsp;import lombok.RequiredArgsConstructor;
&nbsp;import lombok.extern.slf4j.Slf4j;
&nbsp;import org.springframework.beans.factory.annotation.Value;
&nbsp;import org.springframework.context.annotation.Profile;
&nbsp;import org.springframework.jms.annotation.JmsListener;
&nbsp;import org.springframework.jms.core.JmsTemplate;
&nbsp;import org.springframework.stereotype.Component;
&nbsp;
&nbsp;import static be.fgov.onerva.person.backend.util.StringUtils.truncateAndPadLeft;
&nbsp;import static be.fgov.onerva.person.backend.util.StringUtils.truncateAndPadRight;
&nbsp;
&nbsp;/**
&nbsp; * Local simulator for mainframe responses.
&nbsp; * This component is only active in the local profile and simulates the
&nbsp; * mainframe
&nbsp; * by listening to the output queue and generating appropriate success
&nbsp; * responses.
&nbsp; * It listens to the input queue and sends success responses to the output queue.
&nbsp; t.
&nbsp; */
<b class="nc">&nbsp;@Slf4j</b>
&nbsp;@Component
&nbsp;@RequiredArgsConstructor
&nbsp;@Profile(&quot;local&quot;)
&nbsp;public class LocalPersonResponseSimulator {
&nbsp;
&nbsp;    private final JmsTemplate jmsTemplate;
&nbsp;
&nbsp;    @Value(&quot;${queue.out}&quot;)
&nbsp;    private String outputQueue;
&nbsp;
&nbsp;    @Value(&quot;${local.simulated-error-code}&quot;)
&nbsp;    private Integer simulatedErrorCode;
&nbsp;
&nbsp;    /**
&nbsp;     * Listens to the output queue and simulates mainframe responses.
&nbsp;     * This method is triggered when a message is sent to the output queue.
&nbsp;     * It parses the request message, creates a success response, and sends it back
&nbsp;     * to the input queue.
&nbsp;     *
&nbsp;     * @param message The request message
&nbsp;     * @throws JMSException If there&#39;s an error processing the message
&nbsp;     */
&nbsp;    @JmsListener(destination = &quot;${queue.in}&quot;)
&nbsp;    public void simulateResponse(TextMessage message) throws JMSException {
<b class="nc">&nbsp;        String requestPayload = message.getText();</b>
<b class="nc">&nbsp;        log.debug(&quot;Received request for simulation: {}&quot;, requestPayload);</b>
&nbsp;
&nbsp;        try {
&nbsp;            // Determine the request type and create appropriate response
&nbsp;            String response;
<b class="nc">&nbsp;            if (requestPayload.startsWith(&quot;0001&quot;)) { // CREATE</b>
<b class="nc">&nbsp;                response = createSuccessResponse(requestPayload);</b>
<b class="nc">&nbsp;            } else if (requestPayload.startsWith(&quot;0002&quot;)) { // UPDATE</b>
<b class="nc">&nbsp;                response = updateSuccessResponse(requestPayload, simulatedErrorCode);</b>
&nbsp;            } else {
<b class="nc">&nbsp;                log.warn(&quot;Unknown request type: {}&quot;, requestPayload);</b>
&nbsp;                return;
&nbsp;            }
&nbsp;
&nbsp;            // Send the response back to the input queue
<b class="nc">&nbsp;            log.debug(&quot;Sending simulated response: {}&quot;, response);</b>
<b class="nc">&nbsp;            jmsTemplate.convertAndSend(outputQueue, response);</b>
&nbsp;        } catch (Exception e) {
<b class="nc">&nbsp;            log.error(&quot;Error simulating response for request: {}&quot;, requestPayload, e);</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Creates a success response for a CREATE request.
&nbsp;     * Format: names + inss + id + errorCode
&nbsp;     *
&nbsp;     * @param request The CREATE request payload
&nbsp;     * @return The success response
&nbsp;     */
&nbsp;    private String createSuccessResponse(String request) {
&nbsp;        // Constants for CREATE request format
<b class="nc">&nbsp;        String CREATE_CODE = &quot;0001&quot;;</b>
<b class="nc">&nbsp;        int ID_LEN = 19;</b>
<b class="nc">&nbsp;        int INSS_LEN = 11;</b>
<b class="nc">&nbsp;        int NAMES_LEN = 30;</b>
&nbsp;
&nbsp;        // Extract data from request
<b class="nc">&nbsp;        String id = request.substring(CREATE_CODE.length(), CREATE_CODE.length() + ID_LEN);</b>
<b class="nc">&nbsp;        String inss = request.substring(CREATE_CODE.length() + ID_LEN, CREATE_CODE.length() + ID_LEN + INSS_LEN);</b>
&nbsp;        // Skip operator code (4 chars)
<b class="nc">&nbsp;        String names = request.substring(CREATE_CODE.length() + ID_LEN + INSS_LEN + 4);</b>
&nbsp;
&nbsp;        // Format response: names + inss + id + errorCode
&nbsp;        // For CREATE, success code is 1
<b class="nc">&nbsp;        return truncateAndPadRight(names, NAMES_LEN) +</b>
&nbsp;                inss +
&nbsp;                id +
&nbsp;                &quot;+0001&quot;; // Success code for CREATE
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Creates a success response for an UPDATE request.
&nbsp;     * Format: type + id + names + inss + errorCode
&nbsp;     *
&nbsp;     * @param request The UPDATE request payload
&nbsp;     * @return The success response
&nbsp;     */
&nbsp;    private String updateSuccessResponse(String request, Integer errorCode) {
&nbsp;        // Constants for UPDATE request format
<b class="nc">&nbsp;        String UPDATE_CODE = &quot;0002&quot;;</b>
<b class="nc">&nbsp;        int ID_LEN = 19;</b>
<b class="nc">&nbsp;        int INSS_LEN = 11;</b>
<b class="nc">&nbsp;        int NAMES_LEN = 30;</b>
&nbsp;
&nbsp;        // Extract data from request (UPDATE messages are 360 chars long)
<b class="nc">&nbsp;        String id = request.substring(UPDATE_CODE.length(), UPDATE_CODE.length() + ID_LEN);</b>
<b class="nc">&nbsp;        String inss = request.substring(UPDATE_CODE.length() + ID_LEN, UPDATE_CODE.length() + ID_LEN + INSS_LEN);</b>
&nbsp;
&nbsp;        // For UPDATE requests, we&#39;ll use a simplified approach for the simulator
<b class="nc">&nbsp;        String names = truncateAndPadRight(&quot;SIMULATED,RESPONSE&quot;, NAMES_LEN);</b>
&nbsp;
&nbsp;        // Format response: type + id + names + inss + errorCode
&nbsp;        // For UPDATE, success code is 0
<b class="nc">&nbsp;        return UPDATE_CODE +</b>
&nbsp;                id +
&nbsp;                names +
&nbsp;                inss +
<b class="nc">&nbsp;                truncateAndPadLeft(errorCode.toString(), 5, &#39;0&#39;);</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
