


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > PersonRequestRepository</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.request.persistence</a>
</div>

<h1>Coverage Summary for Class: PersonRequestRepository (be.fgov.onerva.person.backend.request.persistence)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
</tr>
  <tr>
    <td class="name">PersonRequestRepository$MockitoMock$wqJ3OUcj</td>
  </tr>
  <tr>
    <td class="name">PersonRequestRepository$MockitoMock$wqJ3OUcj$auxiliary$4MtfdxwK</td>
  </tr>
  <tr>
    <td class="name">PersonRequestRepository$MockitoMock$wqJ3OUcj$auxiliary$CuFJTio9</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.request.persistence;
&nbsp;
&nbsp;import be.fgov.onerva.person.backend.request.model.PersonRequest;
&nbsp;import jakarta.persistence.LockModeType;
&nbsp;import jakarta.persistence.QueryHint;
&nbsp;import org.springframework.data.jpa.repository.*;
&nbsp;import org.springframework.data.repository.query.Param;
&nbsp;
&nbsp;import java.time.LocalDateTime;
&nbsp;import java.util.List;
&nbsp;
&nbsp;public interface PersonRequestRepository extends JpaRepository&lt;PersonRequest, Long&gt;, JpaSpecificationExecutor&lt;PersonRequest&gt; {
&nbsp;
&nbsp;    @Modifying
&nbsp;    @Query(&quot;update PersonRequest req set req.sent = true, req.updated = :timestamp where req.id = :reqId&quot;)
&nbsp;    void markAsSent(@Param(&quot;reqId&quot;) long reqId, @Param(&quot;timestamp&quot;) LocalDateTime timestamp);
&nbsp;
&nbsp;    @Modifying
&nbsp;    @Query(&quot;update PersonRequest req set req.returnCode = :code, req.error = :error, req.updated = :timestamp where req.id = :reqId&quot;)
&nbsp;    int updateStatus(@Param(&quot;reqId&quot;) long reqId, @Param(&quot;code&quot;) Integer code, @Param(&quot;error&quot;) String error, @Param(&quot;timestamp&quot;) LocalDateTime timestamp);
&nbsp;
&nbsp;    @Lock(LockModeType.PESSIMISTIC_WRITE)
&nbsp;    @QueryHints(@QueryHint(name = &quot;jakarta.persistence.lock.timeout&quot;, value = &quot;5000&quot;))
&nbsp;    List&lt;PersonRequest&gt; findFirst10BySentFalseOrderByCreatedAsc();
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
