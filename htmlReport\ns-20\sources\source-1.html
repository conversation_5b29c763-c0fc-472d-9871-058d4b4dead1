


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > ApiKeyAuth</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.wave.auth</a>
</div>

<h1>Coverage Summary for Class: ApiKeyAuth (be.fgov.onerva.wave.auth)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">ApiKeyAuth</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/8)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/10)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/19)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;/*
&nbsp; * WO facade API
&nbsp; * API to manage tasks (and processes) in a simplified manner
&nbsp; *
&nbsp; * The version of the OpenAPI document: v1
&nbsp; * 
&nbsp; *
&nbsp; * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
&nbsp; * https://openapi-generator.tech
&nbsp; * Do not edit the class manually.
&nbsp; */
&nbsp;
&nbsp;
&nbsp;package be.fgov.onerva.wave.auth;
&nbsp;
&nbsp;import org.springframework.http.HttpHeaders;
&nbsp;import org.springframework.util.MultiValueMap;
&nbsp;
&nbsp;@jakarta.annotation.Generated(value = &quot;org.openapitools.codegen.languages.JavaClientCodegen&quot;, date = &quot;2025-07-16T14:11:32.899250600+02:00[Europe/Brussels]&quot;, comments = &quot;Generator version: 7.12.0&quot;)
&nbsp;public class ApiKeyAuth implements Authentication {
&nbsp;    private final String location;
&nbsp;    private final String paramName;
&nbsp;
&nbsp;    private String apiKey;
&nbsp;    private String apiKeyPrefix;
&nbsp;
<b class="nc">&nbsp;    public ApiKeyAuth(String location, String paramName) {</b>
<b class="nc">&nbsp;        this.location = location;</b>
<b class="nc">&nbsp;        this.paramName = paramName;</b>
&nbsp;    }
&nbsp;
&nbsp;    public String getLocation() {
<b class="nc">&nbsp;        return location;</b>
&nbsp;    }
&nbsp;
&nbsp;    public String getParamName() {
<b class="nc">&nbsp;        return paramName;</b>
&nbsp;    }
&nbsp;
&nbsp;    public String getApiKey() {
<b class="nc">&nbsp;        return apiKey;</b>
&nbsp;    }
&nbsp;
&nbsp;    public void setApiKey(String apiKey) {
<b class="nc">&nbsp;        this.apiKey = apiKey;</b>
&nbsp;    }
&nbsp;
&nbsp;    public String getApiKeyPrefix() {
<b class="nc">&nbsp;        return apiKeyPrefix;</b>
&nbsp;    }
&nbsp;
&nbsp;    public void setApiKeyPrefix(String apiKeyPrefix) {
<b class="nc">&nbsp;        this.apiKeyPrefix = apiKeyPrefix;</b>
&nbsp;    }
&nbsp;
&nbsp;    @Override
&nbsp;    public void applyToParams(MultiValueMap&lt;String, String&gt; queryParams, HttpHeaders headerParams, MultiValueMap&lt;String, String&gt; cookieParams) {
<b class="nc">&nbsp;        if (apiKey == null) {</b>
&nbsp;            return;
&nbsp;        }
&nbsp;        String value;
<b class="nc">&nbsp;        if (apiKeyPrefix != null) {</b>
<b class="nc">&nbsp;            value = apiKeyPrefix + &quot; &quot; + apiKey;</b>
&nbsp;        } else {
<b class="nc">&nbsp;            value = apiKey;</b>
&nbsp;        }
<b class="nc">&nbsp;        if (location.equals(&quot;query&quot;)) {</b>
<b class="nc">&nbsp;            queryParams.add(paramName, value);</b>
<b class="nc">&nbsp;        } else if (location.equals(&quot;header&quot;)) {</b>
<b class="nc">&nbsp;            headerParams.add(paramName, value);</b>
<b class="nc">&nbsp;        } else if (location.equals(&quot;cookie&quot;)) {</b>
<b class="nc">&nbsp;            cookieParams.add(paramName, value);</b>
&nbsp;        }
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
