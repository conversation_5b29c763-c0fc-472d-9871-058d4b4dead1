


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > PersonRequestMapper</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.request.mapper</a>
</div>

<h1>Coverage Summary for Class: PersonRequestMapper (be.fgov.onerva.person.backend.request.mapper)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">PersonRequestMapper</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (2/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (2/2)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.request.mapper;
&nbsp;
&nbsp;import backend.rest.model.CitizenRequestDTO;
&nbsp;import backend.rest.model.CitizenRequestPageDTO;
&nbsp;import be.fgov.onerva.person.backend.request.model.PersonRequest;
&nbsp;import org.mapstruct.Mapper;
&nbsp;import org.mapstruct.Mapping;
&nbsp;import org.springframework.data.domain.Page;
&nbsp;
&nbsp;import java.time.LocalDateTime;
&nbsp;import java.time.OffsetDateTime;
&nbsp;import java.time.ZoneId;
&nbsp;
&nbsp;@Mapper
&nbsp;public interface PersonRequestMapper {
&nbsp;
&nbsp;    CitizenRequestDTO toDto(PersonRequest entity);
&nbsp;
&nbsp;    @Mapping(source = &quot;number&quot;, target = &quot;pageNumber&quot;)
&nbsp;    @Mapping(source = &quot;size&quot;, target = &quot;pageSize&quot;)
&nbsp;    @Mapping(source = &quot;totalPages&quot;, target = &quot;totalPage&quot;)
&nbsp;    @Mapping(source = &quot;first&quot;, target = &quot;isFirst&quot;)
&nbsp;    @Mapping(source = &quot;last&quot;, target = &quot;isLast&quot;)
&nbsp;    CitizenRequestPageDTO toDto(Page&lt;PersonRequest&gt; page);
&nbsp;
&nbsp;    static OffsetDateTime map(LocalDateTime value) {
<b class="fc">&nbsp;        if (value == null) return null;</b>
<b class="fc">&nbsp;        return value.atZone(ZoneId.of(&quot;Europe/Brussels&quot;)).toOffsetDateTime();</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
