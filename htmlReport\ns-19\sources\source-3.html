


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > PersonRequestService</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.request.service</a>
</div>

<h1>Coverage Summary for Class: PersonRequestService (be.fgov.onerva.person.backend.request.service)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">PersonRequestService</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (4/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    58.3%
  </span>
  <span class="absValue">
    (7/12)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (49/49)
  </span>
</td>
</tr>
  <tr>
    <td class="name">PersonRequestService$$SpringCGLIB$$0</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (4/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    58.3%
  </span>
  <span class="absValue">
    (7/12)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (49/49)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.request.service;
&nbsp;
&nbsp;import be.fgov.onerva.person.backend.citizen.model.AddressUpdateRequest;
&nbsp;import be.fgov.onerva.person.backend.citizen.model.CitizenUpdateRequest;
&nbsp;import be.fgov.onerva.person.backend.request.event.PersonRequestEvent;
&nbsp;import be.fgov.onerva.person.backend.request.formatter.MainframeUpdateMessageFormatter;
&nbsp;import be.fgov.onerva.person.backend.request.model.Address;
&nbsp;import be.fgov.onerva.person.backend.request.model.PersonRequest;
&nbsp;import be.fgov.onerva.person.backend.request.model.PersonRequestType;
&nbsp;import be.fgov.onerva.person.backend.request.persistence.PersonRequestRepository;
&nbsp;import be.fgov.onerva.wave.model.User;
&nbsp;import lombok.RequiredArgsConstructor;
&nbsp;import lombok.extern.slf4j.Slf4j;
&nbsp;import org.springframework.context.ApplicationEventPublisher;
&nbsp;import org.springframework.stereotype.Service;
&nbsp;import org.springframework.transaction.annotation.Transactional;
&nbsp;
&nbsp;import java.time.LocalDateTime;
&nbsp;
<b class="fc">&nbsp;@Slf4j</b>
&nbsp;@RequiredArgsConstructor
&nbsp;@Service
&nbsp;public class PersonRequestService {
&nbsp;
&nbsp;    private final PersonRequestRepository repository;
&nbsp;    private final ApplicationEventPublisher eventPublisher;
&nbsp;    private final MainframeUpdateMessageFormatter messageFormatter;
&nbsp;
&nbsp;    @Transactional(&quot;personTransactionManager&quot;)
&nbsp;    public PersonRequest createMinimalPersonInfo(String firstname, String lastname, String inss, String correlationId) {
<b class="fc">&nbsp;        var saved = repository.saveAndFlush(PersonRequest.builder()</b>
<b class="fc">&nbsp;                .type(PersonRequestType.CREATE)</b>
<b class="fc">&nbsp;                .niss(inss)</b>
<b class="fc">&nbsp;                .firstname(firstname)</b>
<b class="fc">&nbsp;                .lastname(lastname)</b>
<b class="fc">&nbsp;                .correlationId(correlationId)</b>
<b class="fc">&nbsp;                .created(LocalDateTime.now())</b>
<b class="fc">&nbsp;                .build());</b>
&nbsp;
<b class="fc">&nbsp;        eventPublisher.publishEvent(new PersonRequestEvent(saved));</b>
&nbsp;
<b class="fc">&nbsp;        return saved;</b>
&nbsp;    }
&nbsp;
&nbsp;    @Transactional(&quot;personTransactionManager&quot;)
&nbsp;    public PersonRequest updatePersonInfo(CitizenUpdateRequest update, User user) {
&nbsp;
&nbsp;        // Set default value dates if specific ones are not provided
&nbsp;
&nbsp;        // Extract union due from union due info if available
<b class="fc">&nbsp;        AddressUpdateRequest addressUpdateRequest = update.getAddress();</b>
<b class="fc">&nbsp;        var address = Address.builder()</b>
<b class="fc">&nbsp;                .street(addressUpdateRequest.getStreet())</b>
<b class="fc">&nbsp;                .number(addressUpdateRequest.getNumber())</b>
<b class="fc">&nbsp;                .box(addressUpdateRequest.getBox())</b>
<b class="fc">&nbsp;                .zip(addressUpdateRequest.getZip())</b>
<b class="fc">&nbsp;                .city(addressUpdateRequest.getCity())</b>
<b class="fc">&nbsp;                .countryCode(addressUpdateRequest.getCountryCode())</b>
<b class="fc">&nbsp;                .build();</b>
<b class="fc">&nbsp;        var unionDueInfo = update.getUnionDueInfo();</b>
<b class="fc">&nbsp;        var bankInfo = update.getBankInfo();</b>
&nbsp;
<b class="fc">&nbsp;        var saved = repository.saveAndFlush(PersonRequest.builder()</b>
<b class="fc">&nbsp;                .type(PersonRequestType.UPDATE)</b>
<b class="fc">&nbsp;                .niss(update.getNiss())</b>
<b class="fc">&nbsp;                .nationalityCode(update.getNationalityCode())</b>
<b class="fc">&nbsp;                .paymentType(update.getBankInfo().getPaymentType())</b>
<b class="fc">&nbsp;                .unionDue(unionDueInfo.getUnionDue())</b>
<b class="fc">&nbsp;                .unionDueValueDate(unionDueInfo.getValidFrom())</b>
<b class="fc">&nbsp;                .valueDate(update.getValidFrom())</b>
<b class="fc">&nbsp;                .address(address)</b>
<b class="fc">&nbsp;                .username(user.getUsername())</b>
<b class="fc">&nbsp;                .operatorCode(getOperatorCode(user))</b>
<b class="fc">&nbsp;                .correlationId(update.getCorrelationId())</b>
<b class="fc">&nbsp;                .created(LocalDateTime.now())</b>
&nbsp;                // Add new fields from CitizenUpdateRequest
<b class="pc">&nbsp;                .birthDate(update.getBirthDate() != null ? update.getBirthDate() : null)</b>
<b class="fc">&nbsp;                .languageCode(update.getLanguageCode())</b>
<b class="fc">&nbsp;                .unemploymentOffice(update.getUnemploymentOffice())</b>
&nbsp;                // Add bank info fields
<b class="pc">&nbsp;                .iban(bankInfo != null ? bankInfo.getIban() : null)</b>
<b class="pc">&nbsp;                .bic(bankInfo != null ? bankInfo.getBic() : null)</b>
<b class="pc">&nbsp;                .accountHolder(bankInfo != null ? bankInfo.getAccountHolder() : null)</b>
<b class="pc">&nbsp;                .bankInfoValueDate(bankInfo != null ? bankInfo.getValidFrom() : null)</b>
<b class="fc">&nbsp;                .build());</b>
&nbsp;
<b class="fc">&nbsp;        eventPublisher.publishEvent(new PersonRequestEvent(saved));</b>
&nbsp;
<b class="fc">&nbsp;        return saved;</b>
&nbsp;    }
&nbsp;
&nbsp;    Integer getOperatorCode(User user) {
<b class="fc">&nbsp;        if (!user.getOperatorCodes().isEmpty()) {</b>
&nbsp;            try {
<b class="fc">&nbsp;                return Integer.parseInt(user.getOperatorCodes().getFirst());</b>
&nbsp;            } catch (NumberFormatException e) {
<b class="fc">&nbsp;                log.warn(&quot;Could not convert operator code to an int! ... {}&quot;, user);</b>
&nbsp;            }
&nbsp;        }
<b class="fc">&nbsp;        return null;</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
