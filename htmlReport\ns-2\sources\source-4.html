


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenCreationRequestDTO</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">backend.rest.model</a>
</div>

<h1>Coverage Summary for Class: CitizenCreationRequestDTO (backend.rest.model)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">CitizenCreationRequestDTO</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    80%
  </span>
  <span class="absValue">
    (16/20)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/16)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    47.7%
  </span>
  <span class="absValue">
    (21/44)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package backend.rest.model;
&nbsp;
&nbsp;import java.net.URI;
&nbsp;import java.util.Objects;
&nbsp;import com.fasterxml.jackson.annotation.JsonProperty;
&nbsp;import com.fasterxml.jackson.annotation.JsonCreator;
&nbsp;import com.fasterxml.jackson.annotation.JsonTypeName;
&nbsp;import org.springframework.lang.Nullable;
&nbsp;import java.time.OffsetDateTime;
&nbsp;import jakarta.validation.Valid;
&nbsp;import jakarta.validation.constraints.*;
&nbsp;import org.hibernate.validator.constraints.*;
&nbsp;import io.swagger.v3.oas.annotations.media.Schema;
&nbsp;
&nbsp;
&nbsp;import java.util.*;
&nbsp;import jakarta.annotation.Generated;
&nbsp;
&nbsp;/**
&nbsp; * Describe how to request
&nbsp; */
&nbsp;
&nbsp;@Schema(name = &quot;CitizenCreationRequest&quot;, description = &quot;Describe how to request&quot;)
&nbsp;@JsonTypeName(&quot;CitizenCreationRequest&quot;)
&nbsp;@Generated(value = &quot;org.openapitools.codegen.languages.SpringCodegen&quot;, date = &quot;2025-07-16T14:11:30.627767400+02:00[Europe/Brussels]&quot;, comments = &quot;Generator version: 7.12.0&quot;)
<b class="fc">&nbsp;public class CitizenCreationRequestDTO {</b>
&nbsp;
&nbsp;  private @Nullable String niss;
&nbsp;
&nbsp;  private @Nullable String firstname;
&nbsp;
&nbsp;  private @Nullable String lastname;
&nbsp;
&nbsp;  @Deprecated
&nbsp;  private @Nullable String fallbackUrl;
&nbsp;
&nbsp;  private @Nullable String correlationId;
&nbsp;
&nbsp;  public CitizenCreationRequestDTO niss(String niss) {
<b class="fc">&nbsp;    this.niss = niss;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get niss
&nbsp;   * @return niss
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;niss&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;niss&quot;)
&nbsp;  public String getNiss() {
<b class="fc">&nbsp;    return niss;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setNiss(String niss) {
<b class="fc">&nbsp;    this.niss = niss;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenCreationRequestDTO firstname(String firstname) {
<b class="fc">&nbsp;    this.firstname = firstname;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get firstname
&nbsp;   * @return firstname
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;firstname&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;firstname&quot;)
&nbsp;  public String getFirstname() {
<b class="fc">&nbsp;    return firstname;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setFirstname(String firstname) {
<b class="fc">&nbsp;    this.firstname = firstname;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenCreationRequestDTO lastname(String lastname) {
<b class="fc">&nbsp;    this.lastname = lastname;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get lastname
&nbsp;   * @return lastname
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;lastname&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;lastname&quot;)
&nbsp;  public String getLastname() {
<b class="fc">&nbsp;    return lastname;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setLastname(String lastname) {
<b class="fc">&nbsp;    this.lastname = lastname;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenCreationRequestDTO fallbackUrl(String fallbackUrl) {
<b class="fc">&nbsp;    this.fallbackUrl = fallbackUrl;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Deprecated. A POST will be done on this endpoint with the following request body {\&quot;status \&quot;:  \&quot;SUCCESS  FAILED\&quot;}
&nbsp;   * @return fallbackUrl
&nbsp;   * @deprecated
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;fallbackUrl&quot;, description = &quot;Deprecated. A POST will be done on this endpoint with the following request body {\&quot;status \&quot;:  \&quot;SUCCESS  FAILED\&quot;}&quot;, deprecated = true, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;fallbackUrl&quot;)
&nbsp;  @Deprecated
&nbsp;  public String getFallbackUrl() {
<b class="fc">&nbsp;    return fallbackUrl;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * @deprecated
&nbsp;   */
&nbsp;  @Deprecated
&nbsp;  public void setFallbackUrl(String fallbackUrl) {
<b class="fc">&nbsp;    this.fallbackUrl = fallbackUrl;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenCreationRequestDTO correlationId(String correlationId) {
<b class="fc">&nbsp;    this.correlationId = correlationId;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * An optional ID of your choice to correlate with.
&nbsp;   * @return correlationId
&nbsp;   */
&nbsp;  @Size(max = 50) 
&nbsp;  @Schema(name = &quot;correlationId&quot;, description = &quot;An optional ID of your choice to correlate with.&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;correlationId&quot;)
&nbsp;  public String getCorrelationId() {
<b class="fc">&nbsp;    return correlationId;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setCorrelationId(String correlationId) {
<b class="fc">&nbsp;    this.correlationId = correlationId;</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public boolean equals(Object o) {
<b class="nc">&nbsp;    if (this == o) {</b>
<b class="nc">&nbsp;      return true;</b>
&nbsp;    }
<b class="nc">&nbsp;    if (o == null || getClass() != o.getClass()) {</b>
<b class="nc">&nbsp;      return false;</b>
&nbsp;    }
<b class="nc">&nbsp;    CitizenCreationRequestDTO citizenCreationRequest = (CitizenCreationRequestDTO) o;</b>
<b class="nc">&nbsp;    return Objects.equals(this.niss, citizenCreationRequest.niss) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.firstname, citizenCreationRequest.firstname) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.lastname, citizenCreationRequest.lastname) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.fallbackUrl, citizenCreationRequest.fallbackUrl) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.correlationId, citizenCreationRequest.correlationId);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public int hashCode() {
<b class="nc">&nbsp;    return Objects.hash(niss, firstname, lastname, fallbackUrl, correlationId);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public String toString() {
<b class="nc">&nbsp;    StringBuilder sb = new StringBuilder();</b>
<b class="nc">&nbsp;    sb.append(&quot;class CitizenCreationRequestDTO {\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    niss: &quot;).append(toIndentedString(niss)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    firstname: &quot;).append(toIndentedString(firstname)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    lastname: &quot;).append(toIndentedString(lastname)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    fallbackUrl: &quot;).append(toIndentedString(fallbackUrl)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    correlationId: &quot;).append(toIndentedString(correlationId)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;}&quot;);</b>
<b class="nc">&nbsp;    return sb.toString();</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Convert the given object to string with each line indented by 4 spaces
&nbsp;   * (except the first line).
&nbsp;   */
&nbsp;  private String toIndentedString(Object o) {
<b class="nc">&nbsp;    if (o == null) {</b>
<b class="nc">&nbsp;      return &quot;null&quot;;</b>
&nbsp;    }
<b class="nc">&nbsp;    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</b>
&nbsp;  }
&nbsp;}
&nbsp;
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
