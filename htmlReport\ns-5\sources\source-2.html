


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenMapperImpl</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.citizen.mapper</a>
</div>

<h1>Coverage Summary for Class: CitizenMapperImpl (be.fgov.onerva.person.backend.citizen.mapper)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">CitizenMapperImpl</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (6/6)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    65.4%
  </span>
  <span class="absValue">
    (17/26)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    94.8%
  </span>
  <span class="absValue">
    (55/58)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.citizen.mapper;
&nbsp;
&nbsp;import backend.rest.model.CitizenCreationRequestDTO;
&nbsp;import backend.rest.model.CitizenDTO;
&nbsp;import backend.rest.model.CitizenPageDTO;
&nbsp;import backend.rest.model.CitizenUpdateRequestDTO;
&nbsp;import be.fgov.onerva.person.backend.citizen.model.BusinessDomain;
&nbsp;import be.fgov.onerva.person.backend.citizen.model.CitizenCreationRequest;
&nbsp;import be.fgov.onerva.person.backend.citizen.model.CitizenEntity;
&nbsp;import be.fgov.onerva.person.backend.citizen.model.CitizenUpdateRequest;
&nbsp;import java.util.ArrayList;
&nbsp;import java.util.List;
&nbsp;import javax.annotation.processing.Generated;
&nbsp;import org.springframework.data.domain.Page;
&nbsp;
&nbsp;@Generated(
&nbsp;    value = &quot;org.mapstruct.ap.MappingProcessor&quot;,
&nbsp;    date = &quot;2025-07-17T13:40:46+0200&quot;,
&nbsp;    comments = &quot;version: 1.6.3, compiler: javac, environment: Java 21.0.6 (Eclipse Adoptium)&quot;
&nbsp;)
<b class="fc">&nbsp;public class CitizenMapperImpl implements CitizenMapper {</b>
&nbsp;
&nbsp;    @Override
&nbsp;    public CitizenDTO map(CitizenEntity entity) {
<b class="fc">&nbsp;        if ( entity == null ) {</b>
<b class="fc">&nbsp;            return null;</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        CitizenDTO citizenDTO = new CitizenDTO();</b>
&nbsp;
<b class="fc">&nbsp;        citizenDTO.setPensionNumber( entity.getId() );</b>
<b class="fc">&nbsp;        citizenDTO.setZipCode( entity.getZipCode() );</b>
<b class="fc">&nbsp;        citizenDTO.setNumbox( entity.getNumBox() );</b>
<b class="fc">&nbsp;        citizenDTO.setAgent( entity.isFlagPersonnel() );</b>
&nbsp;
<b class="fc">&nbsp;        citizenDTO.setNiss( niss(entity) );</b>
<b class="fc">&nbsp;        citizenDTO.setLastname( lastName(entity) );</b>
<b class="fc">&nbsp;        citizenDTO.setFirstname( firstName(entity) );</b>
&nbsp;
<b class="fc">&nbsp;        return citizenDTO;</b>
&nbsp;    }
&nbsp;
&nbsp;    @Override
&nbsp;    public CitizenPageDTO mapPageToDto(Page&lt;CitizenEntity&gt; source) {
<b class="fc">&nbsp;        if ( source == null ) {</b>
<b class="fc">&nbsp;            return null;</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        CitizenPageDTO citizenPageDTO = new CitizenPageDTO();</b>
&nbsp;
<b class="fc">&nbsp;        citizenPageDTO.setPageNumber( source.getNumber() );</b>
<b class="fc">&nbsp;        citizenPageDTO.setPageSize( source.getSize() );</b>
<b class="fc">&nbsp;        citizenPageDTO.setTotalPage( source.getTotalPages() );</b>
<b class="fc">&nbsp;        citizenPageDTO.setTotalElements( (int) source.getTotalElements() );</b>
<b class="fc">&nbsp;        citizenPageDTO.setIsFirst( source.isFirst() );</b>
<b class="fc">&nbsp;        citizenPageDTO.setIsLast( source.isLast() );</b>
<b class="fc">&nbsp;        if ( source.hasContent() ) {</b>
<b class="fc">&nbsp;            citizenPageDTO.setContent( citizenEntityListToCitizenDTOList( source.getContent() ) );</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        return citizenPageDTO;</b>
&nbsp;    }
&nbsp;
&nbsp;    @Override
&nbsp;    public CitizenCreationRequest map(CitizenCreationRequestDTO source, String businessDomain, boolean allowance) {
<b class="pc">&nbsp;        if ( source == null &amp;&amp; businessDomain == null ) {</b>
<b class="nc">&nbsp;            return null;</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        CitizenCreationRequest.CitizenCreationRequestBuilder citizenCreationRequest = CitizenCreationRequest.builder();</b>
&nbsp;
<b class="fc">&nbsp;        if ( source != null ) {</b>
<b class="fc">&nbsp;            citizenCreationRequest.niss( source.getNiss() );</b>
<b class="fc">&nbsp;            citizenCreationRequest.firstname( source.getFirstname() );</b>
<b class="fc">&nbsp;            citizenCreationRequest.lastname( source.getLastname() );</b>
<b class="fc">&nbsp;            citizenCreationRequest.correlationId( source.getCorrelationId() );</b>
&nbsp;        }
<b class="pc">&nbsp;        if ( businessDomain != null ) {</b>
<b class="fc">&nbsp;            citizenCreationRequest.domain( Enum.valueOf( BusinessDomain.class, businessDomain ) );</b>
&nbsp;        }
<b class="fc">&nbsp;        citizenCreationRequest.allowance( allowance );</b>
&nbsp;
<b class="fc">&nbsp;        return citizenCreationRequest.build();</b>
&nbsp;    }
&nbsp;
&nbsp;    @Override
&nbsp;    public CitizenUpdateRequest map(CitizenUpdateRequestDTO source, String niss, String username) {
<b class="pc">&nbsp;        if ( source == null &amp;&amp; niss == null &amp;&amp; username == null ) {</b>
<b class="nc">&nbsp;            return null;</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        CitizenUpdateRequest.CitizenUpdateRequestBuilder citizenUpdateRequest = CitizenUpdateRequest.builder();</b>
&nbsp;
<b class="pc">&nbsp;        if ( source != null ) {</b>
<b class="fc">&nbsp;            citizenUpdateRequest.nationalityCode( source.getNationalityCode() );</b>
<b class="fc">&nbsp;            citizenUpdateRequest.validFrom( source.getValidFrom() );</b>
<b class="fc">&nbsp;            citizenUpdateRequest.correlationId( source.getCorrelationId() );</b>
&nbsp;        }
<b class="fc">&nbsp;        citizenUpdateRequest.niss( niss );</b>
<b class="fc">&nbsp;        citizenUpdateRequest.username( username );</b>
<b class="fc">&nbsp;        citizenUpdateRequest.address( mapAddress(source.getAddress()) );</b>
<b class="fc">&nbsp;        citizenUpdateRequest.bankInfo( mapBankInfo(source) );</b>
<b class="fc">&nbsp;        citizenUpdateRequest.unionDueInfo( mapUnionDueInfo(source) );</b>
<b class="fc">&nbsp;        citizenUpdateRequest.birthDate( mapBirthDate(source) );</b>
<b class="fc">&nbsp;        citizenUpdateRequest.languageCode( mapLanguageCode(source) );</b>
<b class="fc">&nbsp;        citizenUpdateRequest.unemploymentOffice( mapUnemploymentOffice(source) );</b>
&nbsp;
<b class="fc">&nbsp;        return citizenUpdateRequest.build();</b>
&nbsp;    }
&nbsp;
&nbsp;    protected List&lt;CitizenDTO&gt; citizenEntityListToCitizenDTOList(List&lt;CitizenEntity&gt; list) {
<b class="pc">&nbsp;        if ( list == null ) {</b>
<b class="nc">&nbsp;            return null;</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        List&lt;CitizenDTO&gt; list1 = new ArrayList&lt;CitizenDTO&gt;( list.size() );</b>
<b class="fc">&nbsp;        for ( CitizenEntity citizenEntity : list ) {</b>
<b class="fc">&nbsp;            list1.add( map( citizenEntity ) );</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        return list1;</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
