


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > SecurityConfig</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.config</a>
</div>

<h1>Coverage Summary for Class: SecurityConfig (be.fgov.onerva.person.backend.config)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">SecurityConfig</td>
<td class="coverageStat">
  <span class="percent">
    66.7%
  </span>
  <span class="absValue">
    (4/6)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    25%
  </span>
  <span class="absValue">
    (1/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    51.6%
  </span>
  <span class="absValue">
    (16/31)
  </span>
</td>
</tr>
  <tr>
    <td class="name">SecurityConfig$$SpringCGLIB$$0</td>
  </tr>
  <tr>
    <td class="name">SecurityConfig$$SpringCGLIB$$FastClass$$0</td>
  </tr>
  <tr>
    <td class="name">SecurityConfig$$SpringCGLIB$$FastClass$$1</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    66.7%
  </span>
  <span class="absValue">
    (4/6)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    25%
  </span>
  <span class="absValue">
    (1/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    51.6%
  </span>
  <span class="absValue">
    (16/31)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.config;
&nbsp;
&nbsp;import lombok.extern.slf4j.Slf4j;
&nbsp;import org.springframework.beans.factory.annotation.Value;
&nbsp;import org.springframework.boot.autoconfigure.security.oauth2.resource.OAuth2ResourceServerProperties;
&nbsp;import org.springframework.context.annotation.Bean;
&nbsp;import org.springframework.context.annotation.Configuration;
&nbsp;import org.springframework.context.annotation.Profile;
&nbsp;import org.springframework.core.env.Environment;
&nbsp;import org.springframework.core.env.Profiles;
&nbsp;import org.springframework.http.HttpMethod;
&nbsp;import org.springframework.security.config.Customizer;
&nbsp;import org.springframework.security.config.annotation.web.builders.HttpSecurity;
&nbsp;import org.springframework.security.oauth2.jwt.JwtDecoder;
&nbsp;import org.springframework.security.oauth2.jwt.JwtDecoders;
&nbsp;import org.springframework.security.oauth2.jwt.JwtValidators;
&nbsp;import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;
&nbsp;import org.springframework.security.web.SecurityFilterChain;
&nbsp;import org.springframework.web.cors.CorsConfiguration;
&nbsp;import org.springframework.web.cors.CorsConfigurationSource;
&nbsp;import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
&nbsp;
&nbsp;import java.time.Duration;
&nbsp;import java.util.List;
&nbsp;
<b class="fc">&nbsp;@Slf4j</b>
&nbsp;@Configuration
<b class="fc">&nbsp;public class SecurityConfig {</b>
&nbsp;
&nbsp;    @Bean
&nbsp;    SecurityFilterChain securityFilterChain(HttpSecurity http, Environment env) throws Exception {
<b class="fc">&nbsp;        http.csrf(c -&gt; c.disable())</b>
<b class="fc">&nbsp;                .cors(Customizer.withDefaults());</b>
&nbsp;
<b class="pc">&nbsp;        if (env.acceptsProfiles(Profiles.of(&quot;unsecured&quot;))) {</b>
<b class="fc">&nbsp;            log.info(&quot;Unsecured --&gt; permit all&quot;);</b>
<b class="fc">&nbsp;            http.authorizeHttpRequests(authorize -&gt; authorize.anyRequest().permitAll());</b>
&nbsp;        } else {
<b class="nc">&nbsp;            log.info(&quot;Secured --&gt; /api&quot;);</b>
<b class="nc">&nbsp;            http.authorizeHttpRequests(authorize -&gt;</b>
<b class="nc">&nbsp;                authorize.requestMatchers(&quot;/actuator/**&quot;, &quot;/swagger-ui.html&quot;, &quot;/swagger-ui/**&quot;, &quot;/v3/api-docs/**&quot;, &quot;/e2e/**&quot;).permitAll()</b>
<b class="nc">&nbsp;                        .requestMatchers(HttpMethod.OPTIONS).permitAll()</b>
<b class="nc">&nbsp;                        .requestMatchers(&quot;/api/**&quot;).authenticated()</b>
<b class="nc">&nbsp;            ).oauth2ResourceServer(server -&gt; server.jwt(Customizer.withDefaults()));</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        return http.build();</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * This decoder can be skipped during development phase when we want to avoid checking the issuer of the JWT token.
&nbsp;     */
&nbsp;    @Bean @Profile(&quot;!unsecured&quot;)
&nbsp;    JwtDecoder jwtDecoder(OAuth2ResourceServerProperties props, @Value(&quot;${keycloak.checkToken:true}&quot;) boolean checkIssuer) {
<b class="nc">&nbsp;        String issuerUri = props.getJwt().getIssuerUri();</b>
<b class="nc">&nbsp;        var validator = checkIssuer ?</b>
<b class="nc">&nbsp;                JwtValidators.createDefaultWithIssuer(issuerUri) // check timestamp and issuer</b>
<b class="nc">&nbsp;                : JwtValidators.createDefault(); // only check timestamp</b>
<b class="nc">&nbsp;        log.info(&quot;Issuer: {}&quot;, issuerUri);</b>
<b class="nc">&nbsp;        log.info(&quot;check issuer: {}&quot;, checkIssuer);</b>
<b class="nc">&nbsp;        NimbusJwtDecoder jwtDecoder = JwtDecoders.fromOidcIssuerLocation(issuerUri);</b>
<b class="nc">&nbsp;        jwtDecoder.setJwtValidator(validator);</b>
&nbsp;
<b class="nc">&nbsp;        return jwtDecoder;</b>
&nbsp;    }
&nbsp;
&nbsp;    @Bean
&nbsp;    CorsConfigurationSource corsConfigurationSource(@Value(&quot;${cors.allowedOrigins:*}&quot;) List&lt;String&gt; origins) {
<b class="fc">&nbsp;        CorsConfiguration configuration = new CorsConfiguration();</b>
<b class="fc">&nbsp;        configuration.setAllowedOriginPatterns(origins);</b>
<b class="fc">&nbsp;        configuration.setAllowedMethods(List.of(&quot;*&quot;));</b>
<b class="fc">&nbsp;        configuration.setAllowedHeaders(List.of(&quot;*&quot;));</b>
<b class="fc">&nbsp;        configuration.setMaxAge(Duration.ofHours(1));</b>
&nbsp;
<b class="fc">&nbsp;        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();</b>
<b class="fc">&nbsp;        source.registerCorsConfiguration(&quot;/**&quot;, configuration);</b>
&nbsp;
<b class="fc">&nbsp;        return source;</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
