


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenInfoApi</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">backend.api</a>
</div>

<h1>Coverage Summary for Class: CitizenInfoApi (backend.api)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">CitizenInfoApi</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/3)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/7)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;/**
&nbsp; * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.12.0).
&nbsp; * https://openapi-generator.tech
&nbsp; * Do not edit the class manually.
&nbsp; */
&nbsp;package backend.api;
&nbsp;
&nbsp;import backend.rest.model.CitizenInfoPageDTO;
&nbsp;import io.swagger.v3.oas.annotations.ExternalDocumentation;
&nbsp;import io.swagger.v3.oas.annotations.Operation;
&nbsp;import io.swagger.v3.oas.annotations.Parameter;
&nbsp;import io.swagger.v3.oas.annotations.Parameters;
&nbsp;import io.swagger.v3.oas.annotations.media.ArraySchema;
&nbsp;import io.swagger.v3.oas.annotations.media.Content;
&nbsp;import io.swagger.v3.oas.annotations.media.Schema;
&nbsp;import io.swagger.v3.oas.annotations.responses.ApiResponse;
&nbsp;import io.swagger.v3.oas.annotations.security.SecurityRequirement;
&nbsp;import io.swagger.v3.oas.annotations.tags.Tag;
&nbsp;import io.swagger.v3.oas.annotations.enums.ParameterIn;
&nbsp;import org.springframework.http.HttpStatus;
&nbsp;import org.springframework.http.MediaType;
&nbsp;import org.springframework.http.ResponseEntity;
&nbsp;import org.springframework.validation.annotation.Validated;
&nbsp;import org.springframework.web.bind.annotation.*;
&nbsp;import org.springframework.web.context.request.NativeWebRequest;
&nbsp;import org.springframework.web.multipart.MultipartFile;
&nbsp;
&nbsp;import jakarta.validation.Valid;
&nbsp;import jakarta.validation.constraints.*;
&nbsp;import java.util.List;
&nbsp;import java.util.Map;
&nbsp;import java.util.Optional;
&nbsp;import jakarta.annotation.Generated;
&nbsp;
&nbsp;@Generated(value = &quot;org.openapitools.codegen.languages.SpringCodegen&quot;, date = &quot;2025-07-16T14:11:30.627767400+02:00[Europe/Brussels]&quot;, comments = &quot;Generator version: 7.12.0&quot;)
&nbsp;@Validated
&nbsp;@Tag(name = &quot;CitizenInfo&quot;, description = &quot;the CitizenInfo API&quot;)
&nbsp;public interface CitizenInfoApi {
&nbsp;
&nbsp;    default Optional&lt;NativeWebRequest&gt; getRequest() {
<b class="nc">&nbsp;        return Optional.empty();</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * GET /citizen/info
&nbsp;     * Search citizens based on a list of SSIN or ids
&nbsp;     *
&nbsp;     * @param ssins Get citizen info using a list of ssins. Cannot be used with citizenId. (optional)
&nbsp;     * @param citizenId Search citizens by id(s). CitizenId is the MFX numbox. Cannot be used with ssins. (optional)
&nbsp;     * @param dataReturned This parameter is never used! (optional, default to SUMMARY)
&nbsp;     * @param pageNumber Paging doesn&amp;#39;t make sense with a list of ids. (optional, default to 0)
&nbsp;     * @param pageSize Paging doesn&amp;#39;t make sense with a list of ids. (optional, default to 10)
&nbsp;     * @return CitizenInfo (status code 200)
&nbsp;     */
&nbsp;    @Operation(
&nbsp;        operationId = &quot;searchCitizenInfo&quot;,
&nbsp;        description = &quot;Search citizens based on a list of SSIN or ids&quot;,
&nbsp;        tags = { &quot;CitizenInfo&quot; },
&nbsp;        responses = {
&nbsp;            @ApiResponse(responseCode = &quot;200&quot;, description = &quot;CitizenInfo&quot;, content = {
&nbsp;                @Content(mediaType = &quot;application/json&quot;, schema = @Schema(implementation = CitizenInfoPageDTO.class))
&nbsp;            })
&nbsp;        }
&nbsp;    )
&nbsp;    @RequestMapping(
&nbsp;        method = RequestMethod.GET,
&nbsp;        value = &quot;/citizen/info&quot;,
&nbsp;        produces = { &quot;application/json&quot; }
&nbsp;    )
&nbsp;    
&nbsp;    default ResponseEntity&lt;CitizenInfoPageDTO&gt; searchCitizenInfo(
&nbsp;        @Parameter(name = &quot;ssins&quot;, description = &quot;Get citizen info using a list of ssins. Cannot be used with citizenId.&quot;, in = ParameterIn.QUERY) @Valid @RequestParam(value = &quot;ssins&quot;, required = false) List&lt;String&gt; ssins,
&nbsp;        @Parameter(name = &quot;citizenId&quot;, description = &quot;Search citizens by id(s). CitizenId is the MFX numbox. Cannot be used with ssins.&quot;, in = ParameterIn.QUERY) @Valid @RequestParam(value = &quot;citizenId&quot;, required = false) List&lt;@Min(1)Integer&gt; citizenId,
&nbsp;        @Parameter(name = &quot;dataReturned&quot;, deprecated = true, description = &quot;This parameter is never used!&quot;, in = ParameterIn.QUERY) @Valid @RequestParam(value = &quot;dataReturned&quot;, required = false, defaultValue = &quot;SUMMARY&quot;) @Deprecated String dataReturned,
&nbsp;        @Min(0) @Parameter(name = &quot;pageNumber&quot;, deprecated = true, description = &quot;Paging doesn&#39;t make sense with a list of ids.&quot;, in = ParameterIn.QUERY) @Valid @RequestParam(value = &quot;pageNumber&quot;, required = false, defaultValue = &quot;0&quot;) @Deprecated Integer pageNumber,
&nbsp;        @Parameter(name = &quot;pageSize&quot;, deprecated = true, description = &quot;Paging doesn&#39;t make sense with a list of ids.&quot;, in = ParameterIn.QUERY) @Valid @RequestParam(value = &quot;pageSize&quot;, required = false, defaultValue = &quot;10&quot;) @Deprecated Integer pageSize
&nbsp;    ) {
<b class="nc">&nbsp;        getRequest().ifPresent(request -&gt; {</b>
<b class="nc">&nbsp;            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader(&quot;Accept&quot;))) {</b>
<b class="nc">&nbsp;                if (mediaType.isCompatibleWith(MediaType.valueOf(&quot;application/json&quot;))) {</b>
<b class="nc">&nbsp;                    String exampleString = &quot;{ \&quot;isFirst\&quot; : true, \&quot;pageNumber\&quot; : 0, \&quot;isLast\&quot; : true, \&quot;totalPage\&quot; : 1, \&quot;pageSize\&quot; : 6, \&quot;content\&quot; : [ { \&quot;lastName\&quot; : \&quot;lastName\&quot;, \&quot;employmentContract\&quot; : 1, \&quot;emailReg\&quot; : \&quot;emailReg\&quot;, \&quot;postalCode\&quot; : \&quot;postalCode\&quot;, \&quot;language\&quot; : \&quot;language\&quot;, \&quot;addressObj\&quot; : { \&quot;zip\&quot; : \&quot;zip\&quot;, \&quot;number\&quot; : \&quot;number\&quot;, \&quot;city\&quot; : \&quot;city\&quot;, \&quot;street\&quot; : \&quot;street\&quot;, \&quot;countryCode\&quot; : 7, \&quot;box\&quot; : \&quot;box\&quot;, \&quot;validFrom\&quot; : \&quot;2000-01-23\&quot; }, \&quot;bisNumber\&quot; : [ \&quot;bisNumber\&quot;, \&quot;bisNumber\&quot; ], \&quot;flagToPurge\&quot; : \&quot;flagToPurge\&quot;, \&quot;telephoneReg\&quot; : \&quot;telephoneReg\&quot;, \&quot;gsmReg\&quot; : \&quot;gsmReg\&quot;, \&quot;numPens\&quot; : 2.****************, \&quot;id\&quot; : 5.***************, \&quot;lastModifDate\&quot; : 1, \&quot;email\&quot; : \&quot;email\&quot;, \&quot;bankAccount\&quot; : { \&quot;iban\&quot; : \&quot;iban\&quot;, \&quot;holder\&quot; : \&quot;holder\&quot;, \&quot;validFrom\&quot; : \&quot;2000-01-23\&quot;, \&quot;bic\&quot; : \&quot;bic\&quot; }, \&quot;OP\&quot; : 3.***************, \&quot;address\&quot; : \&quot;address\&quot;, \&quot;unemploymentOffice\&quot; : 2.***************, \&quot;paymentMode\&quot; : 4, \&quot;sex\&quot; : \&quot;sex\&quot;, \&quot;gsmOnem\&quot; : \&quot;gsmOnem\&quot;, \&quot;birthDate\&quot; : \&quot;2000-01-23\&quot;, \&quot;flagNation\&quot; : 7.***************, \&quot;firstName\&quot; : \&quot;firstName\&quot;, \&quot;ssin\&quot; : \&quot;ssin\&quot;, \&quot;flagVCpte\&quot; : 1.****************, \&quot;unionDue\&quot; : { \&quot;mandateActive\&quot; : true, \&quot;validFrom\&quot; : \&quot;2000-01-23\&quot; }, \&quot;iban\&quot; : \&quot;iban\&quot;, \&quot;telephoneOnem\&quot; : \&quot;telephoneOnem\&quot;, \&quot;numBox\&quot; : 9.***************, \&quot;deceasedDate\&quot; : \&quot;2000-01-23\&quot; }, { \&quot;lastName\&quot; : \&quot;lastName\&quot;, \&quot;employmentContract\&quot; : 1, \&quot;emailReg\&quot; : \&quot;emailReg\&quot;, \&quot;postalCode\&quot; : \&quot;postalCode\&quot;, \&quot;language\&quot; : \&quot;language\&quot;, \&quot;addressObj\&quot; : { \&quot;zip\&quot; : \&quot;zip\&quot;, \&quot;number\&quot; : \&quot;number\&quot;, \&quot;city\&quot; : \&quot;city\&quot;, \&quot;street\&quot; : \&quot;street\&quot;, \&quot;countryCode\&quot; : 7, \&quot;box\&quot; : \&quot;box\&quot;, \&quot;validFrom\&quot; : \&quot;2000-01-23\&quot; }, \&quot;bisNumber\&quot; : [ \&quot;bisNumber\&quot;, \&quot;bisNumber\&quot; ], \&quot;flagToPurge\&quot; : \&quot;flagToPurge\&quot;, \&quot;telephoneReg\&quot; : \&quot;telephoneReg\&quot;, \&quot;gsmReg\&quot; : \&quot;gsmReg\&quot;, \&quot;numPens\&quot; : 2.****************, \&quot;id\&quot; : 5.***************, \&quot;lastModifDate\&quot; : 1, \&quot;email\&quot; : \&quot;email\&quot;, \&quot;bankAccount\&quot; : { \&quot;iban\&quot; : \&quot;iban\&quot;, \&quot;holder\&quot; : \&quot;holder\&quot;, \&quot;validFrom\&quot; : \&quot;2000-01-23\&quot;, \&quot;bic\&quot; : \&quot;bic\&quot; }, \&quot;OP\&quot; : 3.***************, \&quot;address\&quot; : \&quot;address\&quot;, \&quot;unemploymentOffice\&quot; : 2.***************, \&quot;paymentMode\&quot; : 4, \&quot;sex\&quot; : \&quot;sex\&quot;, \&quot;gsmOnem\&quot; : \&quot;gsmOnem\&quot;, \&quot;birthDate\&quot; : \&quot;2000-01-23\&quot;, \&quot;flagNation\&quot; : 7.***************, \&quot;firstName\&quot; : \&quot;firstName\&quot;, \&quot;ssin\&quot; : \&quot;ssin\&quot;, \&quot;flagVCpte\&quot; : 1.****************, \&quot;unionDue\&quot; : { \&quot;mandateActive\&quot; : true, \&quot;validFrom\&quot; : \&quot;2000-01-23\&quot; }, \&quot;iban\&quot; : \&quot;iban\&quot;, \&quot;telephoneOnem\&quot; : \&quot;telephoneOnem\&quot;, \&quot;numBox\&quot; : 9.***************, \&quot;deceasedDate\&quot; : \&quot;2000-01-23\&quot; } ], \&quot;totalElements\&quot; : 5 }&quot;;</b>
<b class="nc">&nbsp;                    ApiUtil.setExampleResponse(request, &quot;application/json&quot;, exampleString);</b>
&nbsp;                    break;
&nbsp;                }
&nbsp;            }
&nbsp;        });
<b class="nc">&nbsp;        return new ResponseEntity&lt;&gt;(HttpStatus.NOT_IMPLEMENTED);</b>
&nbsp;
&nbsp;    }
&nbsp;
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
