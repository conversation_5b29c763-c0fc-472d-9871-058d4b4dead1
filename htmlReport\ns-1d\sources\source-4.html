


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > PersonUpdated</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.msg.v1</a>
</div>

<h1>Coverage Summary for Class: PersonUpdated (be.fgov.onerva.person.msg.v1)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">PersonUpdated</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/17)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/18)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/29)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.msg.v1;
&nbsp;
&nbsp;import java.util.Objects;
&nbsp;import java.util.Map;
<b class="nc">&nbsp;public class PersonUpdated {</b>
&nbsp;  private int errorCode;
&nbsp;  private long id;
&nbsp;  private String correlationId;
&nbsp;  private String ssin;
&nbsp;  private boolean success;
&nbsp;  private String names;
&nbsp;  private Map&lt;String, Object&gt; additionalProperties;
&nbsp;
<b class="nc">&nbsp;  public int getErrorCode() { return this.errorCode; }</b>
<b class="nc">&nbsp;  public void setErrorCode(int errorCode) { this.errorCode = errorCode; }</b>
&nbsp;
<b class="nc">&nbsp;  public long getId() { return this.id; }</b>
<b class="nc">&nbsp;  public void setId(long id) { this.id = id; }</b>
&nbsp;
<b class="nc">&nbsp;  public String getCorrelationId() { return this.correlationId; }</b>
<b class="nc">&nbsp;  public void setCorrelationId(String correlationId) { this.correlationId = correlationId; }</b>
&nbsp;
<b class="nc">&nbsp;  public String getSsin() { return this.ssin; }</b>
<b class="nc">&nbsp;  public void setSsin(String ssin) { this.ssin = ssin; }</b>
&nbsp;
<b class="nc">&nbsp;  public boolean getSuccess() { return this.success; }</b>
<b class="nc">&nbsp;  public void setSuccess(boolean success) { this.success = success; }</b>
&nbsp;
<b class="nc">&nbsp;  public String getNames() { return this.names; }</b>
<b class="nc">&nbsp;  public void setNames(String names) { this.names = names; }</b>
&nbsp;
<b class="nc">&nbsp;  public Map&lt;String, Object&gt; getAdditionalProperties() { return this.additionalProperties; }</b>
<b class="nc">&nbsp;  public void setAdditionalProperties(Map&lt;String, Object&gt; additionalProperties) { this.additionalProperties = additionalProperties; }</b>
&nbsp;
&nbsp;  @Override
&nbsp;  public boolean equals(Object o) {
<b class="nc">&nbsp;    if (this == o) {</b>
<b class="nc">&nbsp;      return true;</b>
&nbsp;    }
<b class="nc">&nbsp;    if (o == null || getClass() != o.getClass()) {</b>
<b class="nc">&nbsp;      return false;</b>
&nbsp;    }
<b class="nc">&nbsp;    PersonUpdated self = (PersonUpdated) o;</b>
<b class="nc">&nbsp;      return </b>
<b class="nc">&nbsp;        Objects.equals(this.errorCode, self.errorCode) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.id, self.id) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.correlationId, self.correlationId) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.ssin, self.ssin) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.success, self.success) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.names, self.names) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.additionalProperties, self.additionalProperties);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public int hashCode() {
<b class="nc">&nbsp;    return Objects.hash((Object)errorCode, (Object)id, (Object)correlationId, (Object)ssin, (Object)success, (Object)names, (Object)additionalProperties);</b>
&nbsp;  }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
