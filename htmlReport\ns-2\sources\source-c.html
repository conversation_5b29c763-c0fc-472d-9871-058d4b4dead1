


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenRequestDTO</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">backend.rest.model</a>
</div>

<h1>Coverage Summary for Class: CitizenRequestDTO (backend.rest.model)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">CitizenRequestDTO</td>
<td class="coverageStat">
  <span class="percent">
    78.3%
  </span>
  <span class="absValue">
    (47/60)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    47.6%
  </span>
  <span class="absValue">
    (20/42)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    61.3%
  </span>
  <span class="absValue">
    (84/137)
  </span>
</td>
</tr>
  <tr>
    <td class="name">CitizenRequestDTO$TypeEnum</td>
<td class="coverageStat">
  <span class="percent">
    83.3%
  </span>
  <span class="absValue">
    (5/6)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    75%
  </span>
  <span class="absValue">
    (3/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    81.8%
  </span>
  <span class="absValue">
    (9/11)
  </span>
</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    78.8%
  </span>
  <span class="absValue">
    (52/66)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    50%
  </span>
  <span class="absValue">
    (23/46)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    62.8%
  </span>
  <span class="absValue">
    (93/148)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package backend.rest.model;
&nbsp;
&nbsp;import java.net.URI;
&nbsp;import java.util.Objects;
&nbsp;import backend.rest.model.AddressDTO;
&nbsp;import backend.rest.model.PaymentTypeDTO;
&nbsp;import com.fasterxml.jackson.annotation.JsonProperty;
&nbsp;import com.fasterxml.jackson.annotation.JsonCreator;
&nbsp;import com.fasterxml.jackson.annotation.JsonTypeName;
&nbsp;import com.fasterxml.jackson.annotation.JsonValue;
&nbsp;import java.time.LocalDate;
&nbsp;import java.time.OffsetDateTime;
&nbsp;import org.springframework.format.annotation.DateTimeFormat;
&nbsp;import org.springframework.lang.Nullable;
&nbsp;import java.time.OffsetDateTime;
&nbsp;import jakarta.validation.Valid;
&nbsp;import jakarta.validation.constraints.*;
&nbsp;import org.hibernate.validator.constraints.*;
&nbsp;import io.swagger.v3.oas.annotations.media.Schema;
&nbsp;
&nbsp;
&nbsp;import java.util.*;
&nbsp;import jakarta.annotation.Generated;
&nbsp;
&nbsp;/**
&nbsp; * Citizen creation request
&nbsp; */
&nbsp;
&nbsp;@Schema(name = &quot;CitizenRequest&quot;, description = &quot;Citizen creation request&quot;)
&nbsp;@JsonTypeName(&quot;CitizenRequest&quot;)
&nbsp;@Generated(value = &quot;org.openapitools.codegen.languages.SpringCodegen&quot;, date = &quot;2025-07-16T14:11:30.627767400+02:00[Europe/Brussels]&quot;, comments = &quot;Generator version: 7.12.0&quot;)
&nbsp;@com.fasterxml.jackson.annotation.JsonInclude(com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL)
&nbsp;public class CitizenRequestDTO {
&nbsp;
&nbsp;  private Long id;
&nbsp;
&nbsp;  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
&nbsp;  private OffsetDateTime created;
&nbsp;
&nbsp;  private String niss;
&nbsp;
&nbsp;  private @Nullable String firstname;
&nbsp;
&nbsp;  private @Nullable String lastname;
&nbsp;
<b class="fc">&nbsp;  private @Nullable String correlationId = null;</b>
&nbsp;
&nbsp;  private Boolean sent;
&nbsp;
<b class="fc">&nbsp;  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)</b>
&nbsp;  private @Nullable OffsetDateTime updated = null;
&nbsp;
&nbsp;  private Integer retryCount;
&nbsp;
<b class="fc">&nbsp;  private @Nullable Integer returnCode = null;</b>
&nbsp;
<b class="fc">&nbsp;  private @Nullable String error = null;</b>
&nbsp;
&nbsp;  /**
&nbsp;   * Gets or Sets type
&nbsp;   */
<b class="fc">&nbsp;  public enum TypeEnum {</b>
<b class="fc">&nbsp;    CREATE(&quot;CREATE&quot;),</b>
&nbsp;    
<b class="fc">&nbsp;    UPDATE(&quot;UPDATE&quot;);</b>
&nbsp;
&nbsp;    private String value;
&nbsp;
<b class="fc">&nbsp;    TypeEnum(String value) {</b>
<b class="fc">&nbsp;      this.value = value;</b>
&nbsp;    }
&nbsp;
&nbsp;    @JsonValue
&nbsp;    public String getValue() {
<b class="fc">&nbsp;      return value;</b>
&nbsp;    }
&nbsp;
&nbsp;    @Override
&nbsp;    public String toString() {
<b class="nc">&nbsp;      return String.valueOf(value);</b>
&nbsp;    }
&nbsp;
&nbsp;    @JsonCreator
&nbsp;    public static TypeEnum fromValue(String value) {
<b class="pc">&nbsp;      for (TypeEnum b : TypeEnum.values()) {</b>
<b class="fc">&nbsp;        if (b.value.equals(value)) {</b>
<b class="fc">&nbsp;          return b;</b>
&nbsp;        }
&nbsp;      }
<b class="nc">&nbsp;      throw new IllegalArgumentException(&quot;Unexpected value &#39;&quot; + value + &quot;&#39;&quot;);</b>
&nbsp;    }
&nbsp;  }
&nbsp;
&nbsp;  private TypeEnum type;
&nbsp;
<b class="fc">&nbsp;  private @Nullable Integer nationalityCode = null;</b>
&nbsp;
&nbsp;  private @Nullable PaymentTypeDTO paymentType;
&nbsp;
<b class="fc">&nbsp;  private @Nullable Boolean unionDue = null;</b>
&nbsp;
<b class="fc">&nbsp;  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)</b>
&nbsp;  private @Nullable LocalDate valueDate = null;
&nbsp;
&nbsp;  private @Nullable AddressDTO address;
&nbsp;
<b class="fc">&nbsp;  private @Nullable String username = null;</b>
&nbsp;
&nbsp;  public CitizenRequestDTO() {
<b class="fc">&nbsp;    super();</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Constructor with only required parameters
&nbsp;   */
<b class="nc">&nbsp;  public CitizenRequestDTO(Long id, OffsetDateTime created, String niss, Boolean sent, Integer retryCount, TypeEnum type) {</b>
<b class="nc">&nbsp;    this.id = id;</b>
<b class="nc">&nbsp;    this.created = created;</b>
<b class="nc">&nbsp;    this.niss = niss;</b>
<b class="nc">&nbsp;    this.sent = sent;</b>
<b class="nc">&nbsp;    this.retryCount = retryCount;</b>
<b class="nc">&nbsp;    this.type = type;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenRequestDTO id(Long id) {
<b class="fc">&nbsp;    this.id = id;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get id
&nbsp;   * @return id
&nbsp;   */
&nbsp;  @NotNull 
&nbsp;  @Schema(name = &quot;id&quot;, requiredMode = Schema.RequiredMode.REQUIRED)
&nbsp;  @JsonProperty(&quot;id&quot;)
&nbsp;  public Long getId() {
<b class="fc">&nbsp;    return id;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setId(Long id) {
<b class="fc">&nbsp;    this.id = id;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenRequestDTO created(OffsetDateTime created) {
<b class="fc">&nbsp;    this.created = created;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get created
&nbsp;   * @return created
&nbsp;   */
&nbsp;  @NotNull @Valid 
&nbsp;  @Schema(name = &quot;created&quot;, requiredMode = Schema.RequiredMode.REQUIRED)
&nbsp;  @JsonProperty(&quot;created&quot;)
&nbsp;  public OffsetDateTime getCreated() {
<b class="fc">&nbsp;    return created;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setCreated(OffsetDateTime created) {
<b class="fc">&nbsp;    this.created = created;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenRequestDTO niss(String niss) {
<b class="fc">&nbsp;    this.niss = niss;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get niss
&nbsp;   * @return niss
&nbsp;   */
&nbsp;  @NotNull 
&nbsp;  @Schema(name = &quot;niss&quot;, requiredMode = Schema.RequiredMode.REQUIRED)
&nbsp;  @JsonProperty(&quot;niss&quot;)
&nbsp;  public String getNiss() {
<b class="fc">&nbsp;    return niss;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setNiss(String niss) {
<b class="fc">&nbsp;    this.niss = niss;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenRequestDTO firstname(String firstname) {
<b class="fc">&nbsp;    this.firstname = firstname;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get firstname
&nbsp;   * @return firstname
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;firstname&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;firstname&quot;)
&nbsp;  public String getFirstname() {
<b class="fc">&nbsp;    return firstname;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setFirstname(String firstname) {
<b class="fc">&nbsp;    this.firstname = firstname;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenRequestDTO lastname(String lastname) {
<b class="fc">&nbsp;    this.lastname = lastname;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get lastname
&nbsp;   * @return lastname
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;lastname&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;lastname&quot;)
&nbsp;  public String getLastname() {
<b class="fc">&nbsp;    return lastname;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setLastname(String lastname) {
<b class="fc">&nbsp;    this.lastname = lastname;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenRequestDTO correlationId(String correlationId) {
<b class="fc">&nbsp;    this.correlationId = correlationId;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get correlationId
&nbsp;   * @return correlationId
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;correlationId&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;correlationId&quot;)
&nbsp;  public String getCorrelationId() {
<b class="fc">&nbsp;    return correlationId;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setCorrelationId(String correlationId) {
<b class="fc">&nbsp;    this.correlationId = correlationId;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenRequestDTO sent(Boolean sent) {
<b class="fc">&nbsp;    this.sent = sent;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get sent
&nbsp;   * @return sent
&nbsp;   */
&nbsp;  @NotNull 
&nbsp;  @Schema(name = &quot;sent&quot;, requiredMode = Schema.RequiredMode.REQUIRED)
&nbsp;  @JsonProperty(&quot;sent&quot;)
&nbsp;  public Boolean getSent() {
<b class="fc">&nbsp;    return sent;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setSent(Boolean sent) {
<b class="fc">&nbsp;    this.sent = sent;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenRequestDTO updated(OffsetDateTime updated) {
<b class="nc">&nbsp;    this.updated = updated;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get updated
&nbsp;   * @return updated
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;updated&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;updated&quot;)
&nbsp;  public OffsetDateTime getUpdated() {
<b class="fc">&nbsp;    return updated;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setUpdated(OffsetDateTime updated) {
<b class="fc">&nbsp;    this.updated = updated;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenRequestDTO retryCount(Integer retryCount) {
<b class="fc">&nbsp;    this.retryCount = retryCount;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get retryCount
&nbsp;   * @return retryCount
&nbsp;   */
&nbsp;  @NotNull 
&nbsp;  @Schema(name = &quot;retryCount&quot;, requiredMode = Schema.RequiredMode.REQUIRED)
&nbsp;  @JsonProperty(&quot;retryCount&quot;)
&nbsp;  public Integer getRetryCount() {
<b class="fc">&nbsp;    return retryCount;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setRetryCount(Integer retryCount) {
<b class="fc">&nbsp;    this.retryCount = retryCount;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenRequestDTO returnCode(Integer returnCode) {
<b class="nc">&nbsp;    this.returnCode = returnCode;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get returnCode
&nbsp;   * @return returnCode
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;returnCode&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;returnCode&quot;)
&nbsp;  public Integer getReturnCode() {
<b class="fc">&nbsp;    return returnCode;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setReturnCode(Integer returnCode) {
<b class="fc">&nbsp;    this.returnCode = returnCode;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenRequestDTO error(String error) {
<b class="nc">&nbsp;    this.error = error;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get error
&nbsp;   * @return error
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;error&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;error&quot;)
&nbsp;  public String getError() {
<b class="fc">&nbsp;    return error;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setError(String error) {
<b class="fc">&nbsp;    this.error = error;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenRequestDTO type(TypeEnum type) {
<b class="fc">&nbsp;    this.type = type;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get type
&nbsp;   * @return type
&nbsp;   */
&nbsp;  @NotNull 
&nbsp;  @Schema(name = &quot;type&quot;, requiredMode = Schema.RequiredMode.REQUIRED)
&nbsp;  @JsonProperty(&quot;type&quot;)
&nbsp;  public TypeEnum getType() {
<b class="fc">&nbsp;    return type;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setType(TypeEnum type) {
<b class="fc">&nbsp;    this.type = type;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenRequestDTO nationalityCode(Integer nationalityCode) {
<b class="nc">&nbsp;    this.nationalityCode = nationalityCode;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get nationalityCode
&nbsp;   * @return nationalityCode
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;nationalityCode&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;nationalityCode&quot;)
&nbsp;  public Integer getNationalityCode() {
<b class="fc">&nbsp;    return nationalityCode;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setNationalityCode(Integer nationalityCode) {
<b class="fc">&nbsp;    this.nationalityCode = nationalityCode;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenRequestDTO paymentType(PaymentTypeDTO paymentType) {
<b class="nc">&nbsp;    this.paymentType = paymentType;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get paymentType
&nbsp;   * @return paymentType
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;paymentType&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;paymentType&quot;)
&nbsp;  public PaymentTypeDTO getPaymentType() {
<b class="fc">&nbsp;    return paymentType;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setPaymentType(PaymentTypeDTO paymentType) {
<b class="fc">&nbsp;    this.paymentType = paymentType;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenRequestDTO unionDue(Boolean unionDue) {
<b class="nc">&nbsp;    this.unionDue = unionDue;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get unionDue
&nbsp;   * @return unionDue
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;unionDue&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;unionDue&quot;)
&nbsp;  public Boolean getUnionDue() {
<b class="fc">&nbsp;    return unionDue;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setUnionDue(Boolean unionDue) {
<b class="fc">&nbsp;    this.unionDue = unionDue;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenRequestDTO valueDate(LocalDate valueDate) {
<b class="nc">&nbsp;    this.valueDate = valueDate;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get valueDate
&nbsp;   * @return valueDate
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;valueDate&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;valueDate&quot;)
&nbsp;  public LocalDate getValueDate() {
<b class="fc">&nbsp;    return valueDate;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setValueDate(LocalDate valueDate) {
<b class="fc">&nbsp;    this.valueDate = valueDate;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenRequestDTO address(AddressDTO address) {
<b class="nc">&nbsp;    this.address = address;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get address
&nbsp;   * @return address
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;address&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;address&quot;)
&nbsp;  public AddressDTO getAddress() {
<b class="fc">&nbsp;    return address;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setAddress(AddressDTO address) {
<b class="fc">&nbsp;    this.address = address;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenRequestDTO username(String username) {
<b class="nc">&nbsp;    this.username = username;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get username
&nbsp;   * @return username
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;username&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;username&quot;)
&nbsp;  public String getUsername() {
<b class="fc">&nbsp;    return username;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setUsername(String username) {
<b class="fc">&nbsp;    this.username = username;</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public boolean equals(Object o) {
<b class="pc">&nbsp;    if (this == o) {</b>
<b class="nc">&nbsp;      return true;</b>
&nbsp;    }
<b class="pc">&nbsp;    if (o == null || getClass() != o.getClass()) {</b>
<b class="nc">&nbsp;      return false;</b>
&nbsp;    }
<b class="fc">&nbsp;    CitizenRequestDTO citizenRequest = (CitizenRequestDTO) o;</b>
<b class="pc">&nbsp;    return Objects.equals(this.id, citizenRequest.id) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.created, citizenRequest.created) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.niss, citizenRequest.niss) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.firstname, citizenRequest.firstname) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.lastname, citizenRequest.lastname) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.correlationId, citizenRequest.correlationId) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.sent, citizenRequest.sent) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.updated, citizenRequest.updated) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.retryCount, citizenRequest.retryCount) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.returnCode, citizenRequest.returnCode) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.error, citizenRequest.error) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.type, citizenRequest.type) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.nationalityCode, citizenRequest.nationalityCode) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.paymentType, citizenRequest.paymentType) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.unionDue, citizenRequest.unionDue) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.valueDate, citizenRequest.valueDate) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.address, citizenRequest.address) &amp;&amp;</b>
<b class="fc">&nbsp;        Objects.equals(this.username, citizenRequest.username);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public int hashCode() {
<b class="nc">&nbsp;    return Objects.hash(id, created, niss, firstname, lastname, correlationId, sent, updated, retryCount, returnCode, error, type, nationalityCode, paymentType, unionDue, valueDate, address, username);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public String toString() {
<b class="nc">&nbsp;    StringBuilder sb = new StringBuilder();</b>
<b class="nc">&nbsp;    sb.append(&quot;class CitizenRequestDTO {\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    id: &quot;).append(toIndentedString(id)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    created: &quot;).append(toIndentedString(created)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    niss: &quot;).append(toIndentedString(niss)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    firstname: &quot;).append(toIndentedString(firstname)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    lastname: &quot;).append(toIndentedString(lastname)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    correlationId: &quot;).append(toIndentedString(correlationId)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    sent: &quot;).append(toIndentedString(sent)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    updated: &quot;).append(toIndentedString(updated)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    retryCount: &quot;).append(toIndentedString(retryCount)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    returnCode: &quot;).append(toIndentedString(returnCode)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    error: &quot;).append(toIndentedString(error)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    type: &quot;).append(toIndentedString(type)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    nationalityCode: &quot;).append(toIndentedString(nationalityCode)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    paymentType: &quot;).append(toIndentedString(paymentType)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    unionDue: &quot;).append(toIndentedString(unionDue)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    valueDate: &quot;).append(toIndentedString(valueDate)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    address: &quot;).append(toIndentedString(address)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    username: &quot;).append(toIndentedString(username)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;}&quot;);</b>
<b class="nc">&nbsp;    return sb.toString();</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Convert the given object to string with each line indented by 4 spaces
&nbsp;   * (except the first line).
&nbsp;   */
&nbsp;  private String toIndentedString(Object o) {
<b class="nc">&nbsp;    if (o == null) {</b>
<b class="nc">&nbsp;      return &quot;null&quot;;</b>
&nbsp;    }
<b class="nc">&nbsp;    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</b>
&nbsp;  }
&nbsp;}
&nbsp;
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
