


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenInfoSectopDs</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.citizeninfo.model</a>
</div>

<h1>Coverage Summary for Class: CitizenInfoSectopDs (be.fgov.onerva.person.backend.citizeninfo.model)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">CitizenInfoSectopDs</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
</tr>
  <tr>
    <td class="name">CitizenInfoSectopDs$HibernateInstantiator$wCQnXw95</td>
  </tr>
  <tr>
    <td class="name">CitizenInfoSectopDs$HibernateProxy$aHB4ZwW5</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.citizeninfo.model;
&nbsp;
&nbsp;
&nbsp;import jakarta.persistence.Column;
&nbsp;import lombok.*;
&nbsp;
&nbsp;import jakarta.persistence.Entity;
&nbsp;import jakarta.persistence.Id;
&nbsp;import jakarta.persistence.Table;
&nbsp;
&nbsp;@Getter
&nbsp;@Builder
&nbsp;@AllArgsConstructor
&nbsp;@NoArgsConstructor
&nbsp;@Entity
&nbsp;@Table(name = &quot;sectop_ds&quot;, schema = &quot;dbo&quot;)
&nbsp;public class CitizenInfoSectopDs {
&nbsp;
&nbsp;    @Id
&nbsp;    private long id;
&nbsp;
&nbsp;    private int numBox;
&nbsp;
&nbsp;    private int flagValid;
&nbsp;
&nbsp;    private int dateValid;
&nbsp;
&nbsp;    private Integer sectOp;
&nbsp;
&nbsp;    @Column(name= &quot;mandat_syndic&quot;)
&nbsp;    private String unionDue;
&nbsp;
&nbsp;    public boolean hasUnionDue() {
<b class="fc">&nbsp;        return &quot;1&quot;.equals(unionDue);</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
