


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenInfoV2DTO</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">backend.rest.model</a>
</div>

<h1>Coverage Summary for Class: CitizenInfoV2DTO (backend.rest.model)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">CitizenInfoV2DTO</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    53.3%
  </span>
  <span class="absValue">
    (48/90)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/64)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    29.9%
  </span>
  <span class="absValue">
    (59/197)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package backend.rest.model;
&nbsp;
&nbsp;import java.net.URI;
&nbsp;import java.util.Objects;
&nbsp;import com.fasterxml.jackson.annotation.JsonProperty;
&nbsp;import com.fasterxml.jackson.annotation.JsonCreator;
&nbsp;import com.fasterxml.jackson.annotation.JsonTypeName;
&nbsp;import java.math.BigDecimal;
&nbsp;import java.time.LocalDate;
&nbsp;import java.util.ArrayList;
&nbsp;import java.util.Arrays;
&nbsp;import java.util.List;
&nbsp;import org.springframework.format.annotation.DateTimeFormat;
&nbsp;import org.springframework.lang.Nullable;
&nbsp;import java.time.OffsetDateTime;
&nbsp;import jakarta.validation.Valid;
&nbsp;import jakarta.validation.constraints.*;
&nbsp;import org.hibernate.validator.constraints.*;
&nbsp;import io.swagger.v3.oas.annotations.media.Schema;
&nbsp;
&nbsp;
&nbsp;import java.util.*;
&nbsp;import jakarta.annotation.Generated;
&nbsp;
&nbsp;/**
&nbsp; * CitizenInfoV2DTO
&nbsp; * @deprecated
&nbsp; */
&nbsp;
&nbsp;@JsonTypeName(&quot;CitizenInfoV2&quot;)
&nbsp;@Generated(value = &quot;org.openapitools.codegen.languages.SpringCodegen&quot;, date = &quot;2025-07-16T14:11:30.627767400+02:00[Europe/Brussels]&quot;, comments = &quot;Generator version: 7.12.0&quot;)
<b class="fc">&nbsp;public class CitizenInfoV2DTO {</b>
&nbsp;
<b class="fc">&nbsp;  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)</b>
&nbsp;  private @Nullable LocalDate dateMcpte = null;
&nbsp;
<b class="fc">&nbsp;  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)</b>
&nbsp;  private @Nullable LocalDate communeDateValid = null;
&nbsp;
<b class="fc">&nbsp;  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)</b>
&nbsp;  private @Nullable LocalDate nationDateValid = null;
&nbsp;
&nbsp;  private @Nullable BigDecimal nationBcss;
&nbsp;
<b class="fc">&nbsp;  private @Nullable Long id = null;</b>
&nbsp;
&nbsp;  private @Nullable String ssin;
&nbsp;
&nbsp;  private @Nullable BigDecimal numPens;
&nbsp;
&nbsp;  private @Nullable String lastName;
&nbsp;
&nbsp;  private @Nullable String firstName;
&nbsp;
&nbsp;  private @Nullable String address;
&nbsp;
&nbsp;  private @Nullable String postalCode;
&nbsp;
&nbsp;  private @Nullable Integer rvaCountryCode;
&nbsp;
&nbsp;  private @Nullable BigDecimal numBox;
&nbsp;
&nbsp;  private @Nullable BigDecimal OP;
&nbsp;
&nbsp;  private @Nullable BigDecimal unemploymentOffice;
&nbsp;
&nbsp;  private @Nullable String iban;
&nbsp;
<b class="fc">&nbsp;  private @Nullable String language = null;</b>
&nbsp;
&nbsp;  private @Nullable String sex;
&nbsp;
&nbsp;  private @Nullable String flagPurge;
&nbsp;
&nbsp;  private @Nullable BigDecimal flagNation;
&nbsp;
&nbsp;  private @Nullable BigDecimal flagVCpte;
&nbsp;
<b class="fc">&nbsp;  private @Nullable String email = null;</b>
&nbsp;
<b class="fc">&nbsp;  private @Nullable String telephoneOnem = null;</b>
&nbsp;
<b class="fc">&nbsp;  private @Nullable String gsmOnem = null;</b>
&nbsp;
<b class="fc">&nbsp;  private @Nullable String telephoneReg = null;</b>
&nbsp;
<b class="fc">&nbsp;  private @Nullable String gsmReg = null;</b>
&nbsp;
<b class="fc">&nbsp;  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)</b>
&nbsp;  private @Nullable LocalDate deceasedDate = null;
&nbsp;
&nbsp;  @Valid
&nbsp;  private @Nullable List&lt;String&gt; bisNumber;
&nbsp;
&nbsp;  public CitizenInfoV2DTO dateMcpte(LocalDate dateMcpte) {
<b class="nc">&nbsp;    this.dateMcpte = dateMcpte;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get dateMcpte
&nbsp;   * @return dateMcpte
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;dateMcpte&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;dateMcpte&quot;)
&nbsp;  public LocalDate getDateMcpte() {
<b class="fc">&nbsp;    return dateMcpte;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setDateMcpte(LocalDate dateMcpte) {
<b class="fc">&nbsp;    this.dateMcpte = dateMcpte;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoV2DTO communeDateValid(LocalDate communeDateValid) {
<b class="nc">&nbsp;    this.communeDateValid = communeDateValid;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get communeDateValid
&nbsp;   * @return communeDateValid
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;communeDateValid&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;communeDateValid&quot;)
&nbsp;  public LocalDate getCommuneDateValid() {
<b class="fc">&nbsp;    return communeDateValid;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setCommuneDateValid(LocalDate communeDateValid) {
<b class="fc">&nbsp;    this.communeDateValid = communeDateValid;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoV2DTO nationDateValid(LocalDate nationDateValid) {
<b class="nc">&nbsp;    this.nationDateValid = nationDateValid;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get nationDateValid
&nbsp;   * @return nationDateValid
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;nationDateValid&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;nationDateValid&quot;)
&nbsp;  public LocalDate getNationDateValid() {
<b class="fc">&nbsp;    return nationDateValid;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setNationDateValid(LocalDate nationDateValid) {
<b class="fc">&nbsp;    this.nationDateValid = nationDateValid;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoV2DTO nationBcss(BigDecimal nationBcss) {
<b class="nc">&nbsp;    this.nationBcss = nationBcss;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get nationBcss
&nbsp;   * @return nationBcss
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;nationBcss&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;nationBcss&quot;)
&nbsp;  public BigDecimal getNationBcss() {
<b class="fc">&nbsp;    return nationBcss;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setNationBcss(BigDecimal nationBcss) {
<b class="fc">&nbsp;    this.nationBcss = nationBcss;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoV2DTO id(Long id) {
<b class="nc">&nbsp;    this.id = id;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get id
&nbsp;   * @return id
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;id&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;id&quot;)
&nbsp;  public Long getId() {
<b class="nc">&nbsp;    return id;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setId(Long id) {
<b class="fc">&nbsp;    this.id = id;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoV2DTO ssin(String ssin) {
<b class="nc">&nbsp;    this.ssin = ssin;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get ssin
&nbsp;   * @return ssin
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;ssin&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;ssin&quot;)
&nbsp;  public String getSsin() {
<b class="nc">&nbsp;    return ssin;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setSsin(String ssin) {
<b class="fc">&nbsp;    this.ssin = ssin;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoV2DTO numPens(BigDecimal numPens) {
<b class="nc">&nbsp;    this.numPens = numPens;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get numPens
&nbsp;   * @return numPens
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;numPens&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;numPens&quot;)
&nbsp;  public BigDecimal getNumPens() {
<b class="fc">&nbsp;    return numPens;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setNumPens(BigDecimal numPens) {
<b class="fc">&nbsp;    this.numPens = numPens;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoV2DTO lastName(String lastName) {
<b class="nc">&nbsp;    this.lastName = lastName;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get lastName
&nbsp;   * @return lastName
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;lastName&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;lastName&quot;)
&nbsp;  public String getLastName() {
<b class="fc">&nbsp;    return lastName;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setLastName(String lastName) {
<b class="fc">&nbsp;    this.lastName = lastName;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoV2DTO firstName(String firstName) {
<b class="nc">&nbsp;    this.firstName = firstName;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get firstName
&nbsp;   * @return firstName
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;firstName&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;firstName&quot;)
&nbsp;  public String getFirstName() {
<b class="nc">&nbsp;    return firstName;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setFirstName(String firstName) {
<b class="fc">&nbsp;    this.firstName = firstName;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoV2DTO address(String address) {
<b class="nc">&nbsp;    this.address = address;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get address
&nbsp;   * @return address
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;address&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;address&quot;)
&nbsp;  public String getAddress() {
<b class="fc">&nbsp;    return address;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setAddress(String address) {
<b class="fc">&nbsp;    this.address = address;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoV2DTO postalCode(String postalCode) {
<b class="nc">&nbsp;    this.postalCode = postalCode;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get postalCode
&nbsp;   * @return postalCode
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;postalCode&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;postalCode&quot;)
&nbsp;  public String getPostalCode() {
<b class="nc">&nbsp;    return postalCode;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setPostalCode(String postalCode) {
<b class="fc">&nbsp;    this.postalCode = postalCode;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoV2DTO rvaCountryCode(Integer rvaCountryCode) {
<b class="nc">&nbsp;    this.rvaCountryCode = rvaCountryCode;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get rvaCountryCode
&nbsp;   * @return rvaCountryCode
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;rvaCountryCode&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;rvaCountryCode&quot;)
&nbsp;  public Integer getRvaCountryCode() {
<b class="nc">&nbsp;    return rvaCountryCode;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setRvaCountryCode(Integer rvaCountryCode) {
<b class="fc">&nbsp;    this.rvaCountryCode = rvaCountryCode;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoV2DTO numBox(BigDecimal numBox) {
<b class="nc">&nbsp;    this.numBox = numBox;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get numBox
&nbsp;   * @return numBox
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;numBox&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;numBox&quot;)
&nbsp;  public BigDecimal getNumBox() {
<b class="fc">&nbsp;    return numBox;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setNumBox(BigDecimal numBox) {
<b class="fc">&nbsp;    this.numBox = numBox;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoV2DTO OP(BigDecimal OP) {
<b class="nc">&nbsp;    this.OP = OP;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get OP
&nbsp;   * @return OP
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;OP&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;OP&quot;)
&nbsp;  public BigDecimal getOP() {
<b class="fc">&nbsp;    return OP;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setOP(BigDecimal OP) {
<b class="fc">&nbsp;    this.OP = OP;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoV2DTO unemploymentOffice(BigDecimal unemploymentOffice) {
<b class="nc">&nbsp;    this.unemploymentOffice = unemploymentOffice;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get unemploymentOffice
&nbsp;   * @return unemploymentOffice
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;unemploymentOffice&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;unemploymentOffice&quot;)
&nbsp;  public BigDecimal getUnemploymentOffice() {
<b class="nc">&nbsp;    return unemploymentOffice;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setUnemploymentOffice(BigDecimal unemploymentOffice) {
<b class="fc">&nbsp;    this.unemploymentOffice = unemploymentOffice;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoV2DTO iban(String iban) {
<b class="nc">&nbsp;    this.iban = iban;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get iban
&nbsp;   * @return iban
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;iban&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;iban&quot;)
&nbsp;  public String getIban() {
<b class="fc">&nbsp;    return iban;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setIban(String iban) {
<b class="fc">&nbsp;    this.iban = iban;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoV2DTO language(String language) {
<b class="nc">&nbsp;    this.language = language;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get language
&nbsp;   * @return language
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;language&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;language&quot;)
&nbsp;  public String getLanguage() {
<b class="fc">&nbsp;    return language;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setLanguage(String language) {
<b class="fc">&nbsp;    this.language = language;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoV2DTO sex(String sex) {
<b class="nc">&nbsp;    this.sex = sex;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get sex
&nbsp;   * @return sex
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;sex&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;sex&quot;)
&nbsp;  public String getSex() {
<b class="fc">&nbsp;    return sex;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setSex(String sex) {
<b class="fc">&nbsp;    this.sex = sex;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoV2DTO flagPurge(String flagPurge) {
<b class="nc">&nbsp;    this.flagPurge = flagPurge;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get flagPurge
&nbsp;   * @return flagPurge
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;flagPurge&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;flagPurge&quot;)
&nbsp;  public String getFlagPurge() {
<b class="fc">&nbsp;    return flagPurge;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setFlagPurge(String flagPurge) {
<b class="fc">&nbsp;    this.flagPurge = flagPurge;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoV2DTO flagNation(BigDecimal flagNation) {
<b class="nc">&nbsp;    this.flagNation = flagNation;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get flagNation
&nbsp;   * @return flagNation
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;flagNation&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;flagNation&quot;)
&nbsp;  public BigDecimal getFlagNation() {
<b class="fc">&nbsp;    return flagNation;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setFlagNation(BigDecimal flagNation) {
<b class="fc">&nbsp;    this.flagNation = flagNation;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoV2DTO flagVCpte(BigDecimal flagVCpte) {
<b class="nc">&nbsp;    this.flagVCpte = flagVCpte;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get flagVCpte
&nbsp;   * @return flagVCpte
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;flagVCpte&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;flagVCpte&quot;)
&nbsp;  public BigDecimal getFlagVCpte() {
<b class="fc">&nbsp;    return flagVCpte;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setFlagVCpte(BigDecimal flagVCpte) {
<b class="fc">&nbsp;    this.flagVCpte = flagVCpte;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoV2DTO email(String email) {
<b class="nc">&nbsp;    this.email = email;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get email
&nbsp;   * @return email
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;email&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;email&quot;)
&nbsp;  public String getEmail() {
<b class="fc">&nbsp;    return email;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setEmail(String email) {
<b class="fc">&nbsp;    this.email = email;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoV2DTO telephoneOnem(String telephoneOnem) {
<b class="nc">&nbsp;    this.telephoneOnem = telephoneOnem;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get telephoneOnem
&nbsp;   * @return telephoneOnem
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;telephoneOnem&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;telephoneOnem&quot;)
&nbsp;  public String getTelephoneOnem() {
<b class="fc">&nbsp;    return telephoneOnem;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setTelephoneOnem(String telephoneOnem) {
<b class="fc">&nbsp;    this.telephoneOnem = telephoneOnem;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoV2DTO gsmOnem(String gsmOnem) {
<b class="nc">&nbsp;    this.gsmOnem = gsmOnem;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get gsmOnem
&nbsp;   * @return gsmOnem
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;gsmOnem&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;gsmOnem&quot;)
&nbsp;  public String getGsmOnem() {
<b class="fc">&nbsp;    return gsmOnem;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setGsmOnem(String gsmOnem) {
<b class="fc">&nbsp;    this.gsmOnem = gsmOnem;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoV2DTO telephoneReg(String telephoneReg) {
<b class="nc">&nbsp;    this.telephoneReg = telephoneReg;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get telephoneReg
&nbsp;   * @return telephoneReg
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;telephoneReg&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;telephoneReg&quot;)
&nbsp;  public String getTelephoneReg() {
<b class="fc">&nbsp;    return telephoneReg;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setTelephoneReg(String telephoneReg) {
<b class="fc">&nbsp;    this.telephoneReg = telephoneReg;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoV2DTO gsmReg(String gsmReg) {
<b class="nc">&nbsp;    this.gsmReg = gsmReg;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get gsmReg
&nbsp;   * @return gsmReg
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;gsmReg&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;gsmReg&quot;)
&nbsp;  public String getGsmReg() {
<b class="fc">&nbsp;    return gsmReg;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setGsmReg(String gsmReg) {
<b class="fc">&nbsp;    this.gsmReg = gsmReg;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoV2DTO deceasedDate(LocalDate deceasedDate) {
<b class="nc">&nbsp;    this.deceasedDate = deceasedDate;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get deceasedDate
&nbsp;   * @return deceasedDate
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;deceasedDate&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;deceasedDate&quot;)
&nbsp;  public LocalDate getDeceasedDate() {
<b class="nc">&nbsp;    return deceasedDate;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setDeceasedDate(LocalDate deceasedDate) {
<b class="fc">&nbsp;    this.deceasedDate = deceasedDate;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoV2DTO bisNumber(List&lt;String&gt; bisNumber) {
<b class="nc">&nbsp;    this.bisNumber = bisNumber;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoV2DTO addBisNumberItem(String bisNumberItem) {
<b class="nc">&nbsp;    if (this.bisNumber == null) {</b>
<b class="nc">&nbsp;      this.bisNumber = new ArrayList&lt;&gt;();</b>
&nbsp;    }
<b class="nc">&nbsp;    this.bisNumber.add(bisNumberItem);</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get bisNumber
&nbsp;   * @return bisNumber
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;bisNumber&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;bisNumber&quot;)
&nbsp;  public List&lt;String&gt; getBisNumber() {
<b class="nc">&nbsp;    return bisNumber;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setBisNumber(List&lt;String&gt; bisNumber) {
<b class="nc">&nbsp;    this.bisNumber = bisNumber;</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public boolean equals(Object o) {
<b class="nc">&nbsp;    if (this == o) {</b>
<b class="nc">&nbsp;      return true;</b>
&nbsp;    }
<b class="nc">&nbsp;    if (o == null || getClass() != o.getClass()) {</b>
<b class="nc">&nbsp;      return false;</b>
&nbsp;    }
<b class="nc">&nbsp;    CitizenInfoV2DTO citizenInfoV2 = (CitizenInfoV2DTO) o;</b>
<b class="nc">&nbsp;    return Objects.equals(this.dateMcpte, citizenInfoV2.dateMcpte) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.communeDateValid, citizenInfoV2.communeDateValid) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.nationDateValid, citizenInfoV2.nationDateValid) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.nationBcss, citizenInfoV2.nationBcss) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.id, citizenInfoV2.id) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.ssin, citizenInfoV2.ssin) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.numPens, citizenInfoV2.numPens) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.lastName, citizenInfoV2.lastName) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.firstName, citizenInfoV2.firstName) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.address, citizenInfoV2.address) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.postalCode, citizenInfoV2.postalCode) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.rvaCountryCode, citizenInfoV2.rvaCountryCode) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.numBox, citizenInfoV2.numBox) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.OP, citizenInfoV2.OP) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.unemploymentOffice, citizenInfoV2.unemploymentOffice) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.iban, citizenInfoV2.iban) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.language, citizenInfoV2.language) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.sex, citizenInfoV2.sex) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.flagPurge, citizenInfoV2.flagPurge) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.flagNation, citizenInfoV2.flagNation) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.flagVCpte, citizenInfoV2.flagVCpte) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.email, citizenInfoV2.email) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.telephoneOnem, citizenInfoV2.telephoneOnem) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.gsmOnem, citizenInfoV2.gsmOnem) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.telephoneReg, citizenInfoV2.telephoneReg) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.gsmReg, citizenInfoV2.gsmReg) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.deceasedDate, citizenInfoV2.deceasedDate) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.bisNumber, citizenInfoV2.bisNumber);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public int hashCode() {
<b class="nc">&nbsp;    return Objects.hash(dateMcpte, communeDateValid, nationDateValid, nationBcss, id, ssin, numPens, lastName, firstName, address, postalCode, rvaCountryCode, numBox, OP, unemploymentOffice, iban, language, sex, flagPurge, flagNation, flagVCpte, email, telephoneOnem, gsmOnem, telephoneReg, gsmReg, deceasedDate, bisNumber);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public String toString() {
<b class="nc">&nbsp;    StringBuilder sb = new StringBuilder();</b>
<b class="nc">&nbsp;    sb.append(&quot;class CitizenInfoV2DTO {\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    dateMcpte: &quot;).append(toIndentedString(dateMcpte)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    communeDateValid: &quot;).append(toIndentedString(communeDateValid)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    nationDateValid: &quot;).append(toIndentedString(nationDateValid)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    nationBcss: &quot;).append(toIndentedString(nationBcss)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    id: &quot;).append(toIndentedString(id)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    ssin: &quot;).append(toIndentedString(ssin)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    numPens: &quot;).append(toIndentedString(numPens)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    lastName: &quot;).append(toIndentedString(lastName)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    firstName: &quot;).append(toIndentedString(firstName)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    address: &quot;).append(toIndentedString(address)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    postalCode: &quot;).append(toIndentedString(postalCode)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    rvaCountryCode: &quot;).append(toIndentedString(rvaCountryCode)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    numBox: &quot;).append(toIndentedString(numBox)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    OP: &quot;).append(toIndentedString(OP)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    unemploymentOffice: &quot;).append(toIndentedString(unemploymentOffice)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    iban: &quot;).append(toIndentedString(iban)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    language: &quot;).append(toIndentedString(language)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    sex: &quot;).append(toIndentedString(sex)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    flagPurge: &quot;).append(toIndentedString(flagPurge)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    flagNation: &quot;).append(toIndentedString(flagNation)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    flagVCpte: &quot;).append(toIndentedString(flagVCpte)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    email: &quot;).append(toIndentedString(email)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    telephoneOnem: &quot;).append(toIndentedString(telephoneOnem)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    gsmOnem: &quot;).append(toIndentedString(gsmOnem)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    telephoneReg: &quot;).append(toIndentedString(telephoneReg)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    gsmReg: &quot;).append(toIndentedString(gsmReg)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    deceasedDate: &quot;).append(toIndentedString(deceasedDate)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    bisNumber: &quot;).append(toIndentedString(bisNumber)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;}&quot;);</b>
<b class="nc">&nbsp;    return sb.toString();</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Convert the given object to string with each line indented by 4 spaces
&nbsp;   * (except the first line).
&nbsp;   */
&nbsp;  private String toIndentedString(Object o) {
<b class="nc">&nbsp;    if (o == null) {</b>
<b class="nc">&nbsp;      return &quot;null&quot;;</b>
&nbsp;    }
<b class="nc">&nbsp;    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</b>
&nbsp;  }
&nbsp;}
&nbsp;
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
