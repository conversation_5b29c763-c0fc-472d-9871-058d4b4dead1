


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > ServerConfiguration</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.wave</a>
</div>

<h1>Coverage Summary for Class: ServerConfiguration (be.fgov.onerva.wave)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">ServerConfiguration</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/3)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/10)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/16)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;/*
&nbsp; * WO facade API
&nbsp; * API to manage tasks (and processes) in a simplified manner
&nbsp; *
&nbsp; * The version of the OpenAPI document: v1
&nbsp; * 
&nbsp; *
&nbsp; * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
&nbsp; * https://openapi-generator.tech
&nbsp; * Do not edit the class manually.
&nbsp; */
&nbsp;
&nbsp;
&nbsp;package be.fgov.onerva.wave;
&nbsp;
&nbsp;import java.util.Map;
&nbsp;
&nbsp;/**
&nbsp; * Representing a Server configuration.
&nbsp; */
&nbsp;@jakarta.annotation.Generated(value = &quot;org.openapitools.codegen.languages.JavaClientCodegen&quot;, date = &quot;2025-07-16T14:11:32.899250600+02:00[Europe/Brussels]&quot;, comments = &quot;Generator version: 7.12.0&quot;)
&nbsp;public class ServerConfiguration {
&nbsp;    public String URL;
&nbsp;    public String description;
&nbsp;    public Map&lt;String, ServerVariable&gt; variables;
&nbsp;
&nbsp;    /**
&nbsp;     * @param URL A URL to the target host.
&nbsp;     * @param description A description of the host designated by the URL.
&nbsp;     * @param variables A map between a variable name and its value. The value is used for substitution in the server&#39;s URL template.
&nbsp;     */
<b class="nc">&nbsp;    public ServerConfiguration(String URL, String description, Map&lt;String, ServerVariable&gt; variables) {</b>
<b class="nc">&nbsp;        this.URL = URL;</b>
<b class="nc">&nbsp;        this.description = description;</b>
<b class="nc">&nbsp;        this.variables = variables;</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Format URL template using given variables.
&nbsp;     *
&nbsp;     * @param variables A map between a variable name and its value.
&nbsp;     * @return Formatted URL.
&nbsp;     */
&nbsp;    public String URL(Map&lt;String, String&gt; variables) {
<b class="nc">&nbsp;        String url = this.URL;</b>
&nbsp;
&nbsp;        // go through variables and replace placeholders
<b class="nc">&nbsp;        for (Map.Entry&lt;String, ServerVariable&gt; variable: this.variables.entrySet()) {</b>
<b class="nc">&nbsp;            String name = variable.getKey();</b>
<b class="nc">&nbsp;            ServerVariable serverVariable = variable.getValue();</b>
<b class="nc">&nbsp;            String value = serverVariable.defaultValue;</b>
&nbsp;
<b class="nc">&nbsp;            if (variables != null &amp;&amp; variables.containsKey(name)) {</b>
<b class="nc">&nbsp;                value = variables.get(name);</b>
<b class="nc">&nbsp;                if (serverVariable.enumValues.size() &gt; 0 &amp;&amp; !serverVariable.enumValues.contains(value)) {</b>
<b class="nc">&nbsp;                    throw new IllegalArgumentException(&quot;The variable &quot; + name + &quot; in the server URL has invalid value &quot; + value + &quot;.&quot;);</b>
&nbsp;                }
&nbsp;            }
<b class="nc">&nbsp;            url = url.replace(&quot;{&quot; + name + &quot;}&quot;, value);</b>
&nbsp;        }
<b class="nc">&nbsp;        return url;</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Format URL template using default server variables.
&nbsp;     *
&nbsp;     * @return Formatted URL.
&nbsp;     */
&nbsp;    public String URL() {
<b class="nc">&nbsp;        return URL(null);</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
