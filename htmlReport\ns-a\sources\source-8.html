


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenInfoCompte</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.citizeninfo.model</a>
</div>

<h1>Coverage Summary for Class: CitizenInfoCompte (be.fgov.onerva.person.backend.citizeninfo.model)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
</tr>
  <tr>
    <td class="name">CitizenInfoCompte$HibernateInstantiator$bj2qFzsi</td>
  </tr>
  <tr>
    <td class="name">CitizenInfoCompte$HibernateProxy$rgEpyaiz</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.citizeninfo.model;
&nbsp;
&nbsp;import lombok.*;
&nbsp;
&nbsp;import jakarta.persistence.Column;
&nbsp;import jakarta.persistence.Entity;
&nbsp;import jakarta.persistence.Id;
&nbsp;import jakarta.persistence.Table;
&nbsp;
&nbsp;@Getter
&nbsp;@Builder
&nbsp;@AllArgsConstructor
&nbsp;@NoArgsConstructor
&nbsp;@Entity
&nbsp;@Table(name = &quot;cpte_ds&quot;, schema = &quot;dbo&quot;)
&nbsp;public class CitizenInfoCompte {
&nbsp;    @Id
&nbsp;    private long id;
&nbsp;
&nbsp;    private long parentId;
&nbsp;
&nbsp;    private int flagValid;
&nbsp;
&nbsp;    private int dateValid;
&nbsp;
&nbsp;    @Column(name = &quot;cpte_iban&quot;)
&nbsp;    private String iban;
&nbsp;
&nbsp;    @Column(name = &quot;cpte_bic&quot;)
&nbsp;    private String bic;
&nbsp;
&nbsp;    @Column(name = &quot;cpte_tit&quot;)
&nbsp;    private String bankAccountHolder;
&nbsp;
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
