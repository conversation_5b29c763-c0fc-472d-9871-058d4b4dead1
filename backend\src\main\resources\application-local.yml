spring:
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.security.oauth2.client.servlet.OAuth2ClientAutoConfiguration
  security:
    oauth2:
      resource-server:
        jwt:
          issuer-uri: http://localhost:8082/realms/onemrva-agents
  jpa:
    show-sql: true

application:
  national-citizen-default: 150

flagsmith:
  environment:
    id: QFA37efDPrx4J6YsP5mHJc

datasource:
  mfx:
    url: ******************************************************;
    username: sa
    password: D3vD3vD3v$
  person:
    url: ***********************************************************
    username: sa
    password: p3rs0n-pwd

ibm:
  mq:
    autoConfigure: false

queue:
  out: QL_UB_UNKNOWN_CITIZEN_OUT_LOCAL

management:
  health:
    rabbit:
      enabled: false

onerva:
  observability:
    otel:
      enabled: false
    prometheus:
      enabled: false

logging:
  level:
    be:
      fgov:
        onerva:
          person:
            backend: DEBUG

local:
  simulated-error-code: 0