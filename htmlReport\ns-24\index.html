
<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > be.fgov.onerva.person.backend.request.event</title>
  <style type="text/css">
    @import "../css/coverage.css";
    @import "../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../js/highlight.min.js"></script>
  <script type="text/javascript" src="../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">

<div class="breadCrumbs">
Current scope:     <a href="../index.html">all classes</a>
    <span class="separator">|</span>
be.fgov.onerva.person.backend.request.event</div>

<h1>Coverage Summary for Package: be.fgov.onerva.person.backend.request.event</h1>
<table class="coverageStats">
  <tr>
    <th class="name">Package</th>
  </tr>
  <tr>
    <td class="name">be.fgov.onerva.person.backend.request.event</td>
  </tr>
</table>

<br/>
<br/>

<table class="coverageStats">
<tr>
  <th class="name  sortedAsc
">
<a href="index_SORT_BY_NAME_DESC.html">Class</a>  </th>
</tr>
</table>

</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
