


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > User</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.wave.model</a>
</div>

<h1>Coverage Summary for Class: User (be.fgov.onerva.wave.model)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">User</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    37.5%
  </span>
  <span class="absValue">
    (9/24)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    15%
  </span>
  <span class="absValue">
    (3/20)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    47.3%
  </span>
  <span class="absValue">
    (26/55)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;/*
&nbsp; * WO facade API
&nbsp; * API to manage tasks (and processes) in a simplified manner
&nbsp; *
&nbsp; * The version of the OpenAPI document: v1
&nbsp; * 
&nbsp; *
&nbsp; * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
&nbsp; * https://openapi-generator.tech
&nbsp; * Do not edit the class manually.
&nbsp; */
&nbsp;
&nbsp;
&nbsp;package be.fgov.onerva.wave.model;
&nbsp;
&nbsp;import java.util.Objects;
&nbsp;import java.util.Arrays;
&nbsp;import com.fasterxml.jackson.annotation.JsonInclude;
&nbsp;import com.fasterxml.jackson.annotation.JsonProperty;
&nbsp;import com.fasterxml.jackson.annotation.JsonCreator;
&nbsp;import com.fasterxml.jackson.annotation.JsonTypeName;
&nbsp;import com.fasterxml.jackson.annotation.JsonValue;
&nbsp;import java.util.ArrayList;
&nbsp;import java.util.Arrays;
&nbsp;import java.util.List;
&nbsp;import com.fasterxml.jackson.annotation.JsonPropertyOrder;
&nbsp;import com.fasterxml.jackson.annotation.JsonTypeName;
&nbsp;
&nbsp;/**
&nbsp; * User
&nbsp; */
&nbsp;@JsonPropertyOrder({
&nbsp;  User.JSON_PROPERTY_USERNAME,
&nbsp;  User.JSON_PROPERTY_FIRSTNAME,
&nbsp;  User.JSON_PROPERTY_LASTNAME,
&nbsp;  User.JSON_PROPERTY_OPERATOR_CODE,
&nbsp;  User.JSON_PROPERTY_OPERATOR_CODES,
&nbsp;  User.JSON_PROPERTY_INSS
&nbsp;})
&nbsp;@jakarta.annotation.Generated(value = &quot;org.openapitools.codegen.languages.JavaClientCodegen&quot;, date = &quot;2025-07-16T14:11:32.899250600+02:00[Europe/Brussels]&quot;, comments = &quot;Generator version: 7.12.0&quot;)
&nbsp;public class User {
&nbsp;  public static final String JSON_PROPERTY_USERNAME = &quot;username&quot;;
&nbsp;  @jakarta.annotation.Nonnull
&nbsp;  private String username;
&nbsp;
&nbsp;  public static final String JSON_PROPERTY_FIRSTNAME = &quot;firstname&quot;;
&nbsp;  @jakarta.annotation.Nonnull
&nbsp;  private String firstname;
&nbsp;
&nbsp;  public static final String JSON_PROPERTY_LASTNAME = &quot;lastname&quot;;
&nbsp;  @jakarta.annotation.Nonnull
&nbsp;  private String lastname;
&nbsp;
&nbsp;  public static final String JSON_PROPERTY_OPERATOR_CODE = &quot;operatorCode&quot;;
&nbsp;  @jakarta.annotation.Nullable
&nbsp;  private String operatorCode;
&nbsp;
&nbsp;  public static final String JSON_PROPERTY_OPERATOR_CODES = &quot;operatorCodes&quot;;
<b class="fc">&nbsp;  @jakarta.annotation.Nonnull</b>
&nbsp;  private List&lt;String&gt; operatorCodes = new ArrayList&lt;&gt;();
&nbsp;
&nbsp;  public static final String JSON_PROPERTY_INSS = &quot;inss&quot;;
&nbsp;  @jakarta.annotation.Nonnull
&nbsp;  private String inss;
&nbsp;
<b class="fc">&nbsp;  public User() {</b>
&nbsp;  }
&nbsp;
&nbsp;  public User username(@jakarta.annotation.Nonnull String username) {
&nbsp;    
<b class="fc">&nbsp;    this.username = username;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get username
&nbsp;   * @return username
&nbsp;   */
&nbsp;  @jakarta.annotation.Nonnull
&nbsp;  @JsonProperty(JSON_PROPERTY_USERNAME)
&nbsp;  @JsonInclude(value = JsonInclude.Include.ALWAYS)
&nbsp;
&nbsp;  public String getUsername() {
<b class="fc">&nbsp;    return username;</b>
&nbsp;  }
&nbsp;
&nbsp;
&nbsp;  @JsonProperty(JSON_PROPERTY_USERNAME)
&nbsp;  @JsonInclude(value = JsonInclude.Include.ALWAYS)
&nbsp;  public void setUsername(@jakarta.annotation.Nonnull String username) {
<b class="nc">&nbsp;    this.username = username;</b>
&nbsp;  }
&nbsp;
&nbsp;  public User firstname(@jakarta.annotation.Nonnull String firstname) {
&nbsp;    
<b class="fc">&nbsp;    this.firstname = firstname;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get firstname
&nbsp;   * @return firstname
&nbsp;   */
&nbsp;  @jakarta.annotation.Nonnull
&nbsp;  @JsonProperty(JSON_PROPERTY_FIRSTNAME)
&nbsp;  @JsonInclude(value = JsonInclude.Include.ALWAYS)
&nbsp;
&nbsp;  public String getFirstname() {
<b class="nc">&nbsp;    return firstname;</b>
&nbsp;  }
&nbsp;
&nbsp;
&nbsp;  @JsonProperty(JSON_PROPERTY_FIRSTNAME)
&nbsp;  @JsonInclude(value = JsonInclude.Include.ALWAYS)
&nbsp;  public void setFirstname(@jakarta.annotation.Nonnull String firstname) {
<b class="nc">&nbsp;    this.firstname = firstname;</b>
&nbsp;  }
&nbsp;
&nbsp;  public User lastname(@jakarta.annotation.Nonnull String lastname) {
&nbsp;    
<b class="fc">&nbsp;    this.lastname = lastname;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get lastname
&nbsp;   * @return lastname
&nbsp;   */
&nbsp;  @jakarta.annotation.Nonnull
&nbsp;  @JsonProperty(JSON_PROPERTY_LASTNAME)
&nbsp;  @JsonInclude(value = JsonInclude.Include.ALWAYS)
&nbsp;
&nbsp;  public String getLastname() {
<b class="nc">&nbsp;    return lastname;</b>
&nbsp;  }
&nbsp;
&nbsp;
&nbsp;  @JsonProperty(JSON_PROPERTY_LASTNAME)
&nbsp;  @JsonInclude(value = JsonInclude.Include.ALWAYS)
&nbsp;  public void setLastname(@jakarta.annotation.Nonnull String lastname) {
<b class="nc">&nbsp;    this.lastname = lastname;</b>
&nbsp;  }
&nbsp;
&nbsp;  public User operatorCode(@jakarta.annotation.Nullable String operatorCode) {
&nbsp;    
<b class="nc">&nbsp;    this.operatorCode = operatorCode;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get operatorCode
&nbsp;   * @return operatorCode
&nbsp;   * @deprecated
&nbsp;   */
&nbsp;  @Deprecated
&nbsp;  @jakarta.annotation.Nullable
&nbsp;  @JsonProperty(JSON_PROPERTY_OPERATOR_CODE)
&nbsp;  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
&nbsp;
&nbsp;  public String getOperatorCode() {
<b class="nc">&nbsp;    return operatorCode;</b>
&nbsp;  }
&nbsp;
&nbsp;
&nbsp;  @JsonProperty(JSON_PROPERTY_OPERATOR_CODE)
&nbsp;  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
&nbsp;  public void setOperatorCode(@jakarta.annotation.Nullable String operatorCode) {
<b class="nc">&nbsp;    this.operatorCode = operatorCode;</b>
&nbsp;  }
&nbsp;
&nbsp;  public User operatorCodes(@jakarta.annotation.Nonnull List&lt;String&gt; operatorCodes) {
&nbsp;    
<b class="nc">&nbsp;    this.operatorCodes = operatorCodes;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  public User addOperatorCodesItem(String operatorCodesItem) {
<b class="pc">&nbsp;    if (this.operatorCodes == null) {</b>
<b class="nc">&nbsp;      this.operatorCodes = new ArrayList&lt;&gt;();</b>
&nbsp;    }
<b class="fc">&nbsp;    this.operatorCodes.add(operatorCodesItem);</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get operatorCodes
&nbsp;   * @return operatorCodes
&nbsp;   */
&nbsp;  @jakarta.annotation.Nonnull
&nbsp;  @JsonProperty(JSON_PROPERTY_OPERATOR_CODES)
&nbsp;  @JsonInclude(value = JsonInclude.Include.ALWAYS)
&nbsp;
&nbsp;  public List&lt;String&gt; getOperatorCodes() {
<b class="fc">&nbsp;    return operatorCodes;</b>
&nbsp;  }
&nbsp;
&nbsp;
&nbsp;  @JsonProperty(JSON_PROPERTY_OPERATOR_CODES)
&nbsp;  @JsonInclude(value = JsonInclude.Include.ALWAYS)
&nbsp;  public void setOperatorCodes(@jakarta.annotation.Nonnull List&lt;String&gt; operatorCodes) {
<b class="nc">&nbsp;    this.operatorCodes = operatorCodes;</b>
&nbsp;  }
&nbsp;
&nbsp;  public User inss(@jakarta.annotation.Nonnull String inss) {
&nbsp;    
<b class="nc">&nbsp;    this.inss = inss;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get inss
&nbsp;   * @return inss
&nbsp;   */
&nbsp;  @jakarta.annotation.Nonnull
&nbsp;  @JsonProperty(JSON_PROPERTY_INSS)
&nbsp;  @JsonInclude(value = JsonInclude.Include.ALWAYS)
&nbsp;
&nbsp;  public String getInss() {
<b class="nc">&nbsp;    return inss;</b>
&nbsp;  }
&nbsp;
&nbsp;
&nbsp;  @JsonProperty(JSON_PROPERTY_INSS)
&nbsp;  @JsonInclude(value = JsonInclude.Include.ALWAYS)
&nbsp;  public void setInss(@jakarta.annotation.Nonnull String inss) {
<b class="nc">&nbsp;    this.inss = inss;</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public boolean equals(Object o) {
<b class="nc">&nbsp;    if (this == o) {</b>
<b class="nc">&nbsp;      return true;</b>
&nbsp;    }
<b class="nc">&nbsp;    if (o == null || getClass() != o.getClass()) {</b>
<b class="nc">&nbsp;      return false;</b>
&nbsp;    }
<b class="nc">&nbsp;    User user = (User) o;</b>
<b class="nc">&nbsp;    return Objects.equals(this.username, user.username) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.firstname, user.firstname) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.lastname, user.lastname) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.operatorCode, user.operatorCode) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.operatorCodes, user.operatorCodes) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.inss, user.inss);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public int hashCode() {
<b class="nc">&nbsp;    return Objects.hash(username, firstname, lastname, operatorCode, operatorCodes, inss);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public String toString() {
<b class="fc">&nbsp;    StringBuilder sb = new StringBuilder();</b>
<b class="fc">&nbsp;    sb.append(&quot;class User {\n&quot;);</b>
<b class="fc">&nbsp;    sb.append(&quot;    username: &quot;).append(toIndentedString(username)).append(&quot;\n&quot;);</b>
<b class="fc">&nbsp;    sb.append(&quot;    firstname: &quot;).append(toIndentedString(firstname)).append(&quot;\n&quot;);</b>
<b class="fc">&nbsp;    sb.append(&quot;    lastname: &quot;).append(toIndentedString(lastname)).append(&quot;\n&quot;);</b>
<b class="fc">&nbsp;    sb.append(&quot;    operatorCode: &quot;).append(toIndentedString(operatorCode)).append(&quot;\n&quot;);</b>
<b class="fc">&nbsp;    sb.append(&quot;    operatorCodes: &quot;).append(toIndentedString(operatorCodes)).append(&quot;\n&quot;);</b>
<b class="fc">&nbsp;    sb.append(&quot;    inss: &quot;).append(toIndentedString(inss)).append(&quot;\n&quot;);</b>
<b class="fc">&nbsp;    sb.append(&quot;}&quot;);</b>
<b class="fc">&nbsp;    return sb.toString();</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Convert the given object to string with each line indented by 4 spaces
&nbsp;   * (except the first line).
&nbsp;   */
&nbsp;  private String toIndentedString(Object o) {
<b class="fc">&nbsp;    if (o == null) {</b>
<b class="fc">&nbsp;      return &quot;null&quot;;</b>
&nbsp;    }
<b class="fc">&nbsp;    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</b>
&nbsp;  }
&nbsp;
&nbsp;}
&nbsp;
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
