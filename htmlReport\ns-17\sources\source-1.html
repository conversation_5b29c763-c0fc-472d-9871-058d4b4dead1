


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > PersonEventMapper</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.request.mapper</a>
</div>

<h1>Coverage Summary for Class: PersonEventMapper (be.fgov.onerva.person.backend.request.mapper)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">PersonEventMapper</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (4/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (5/5)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.request.mapper;
&nbsp;
&nbsp;import be.fgov.onerva.person.backend.request.model.PersonMfxResponse;
&nbsp;import be.fgov.onerva.person.backend.request.model.PersonRequest;
&nbsp;import be.fgov.onerva.person.msg.v1.*;
&nbsp;import org.mapstruct.Mapper;
&nbsp;import org.mapstruct.Mapping;
&nbsp;
&nbsp;import static be.fgov.onerva.person.msg.v1.PersonCreatedStatus.*;
&nbsp;
&nbsp;@Mapper
&nbsp;public interface PersonEventMapper {
&nbsp;
&nbsp;    @CloudEventMapping
&nbsp;    @Mapping(target = &quot;type&quot;, constant = &quot;be.fgov.onerva.person.msg.v1.PersonCreated&quot;)
&nbsp;    @Mapping(target = &quot;data&quot;, expression = &quot;java(mapToCreate(request, response))&quot;)
&nbsp;    PersonCreatedPayload mapToCloudEventCreate(PersonRequest request, PersonMfxResponse response);
&nbsp;
&nbsp;    @PersonEventMapping
&nbsp;    @Mapping(target = &quot;status&quot;, expression = &quot;java(personCreatedStatus(response.getErrorCode()))&quot;)
&nbsp;    PersonCreated mapToCreate(PersonRequest request, PersonMfxResponse response);
&nbsp;
&nbsp;    default PersonCreatedStatus personCreatedStatus(int errorCode) {
<b class="pc">&nbsp;        return switch (errorCode) {</b>
<b class="fc">&nbsp;            case 1 -&gt; CREATED;</b>
<b class="fc">&nbsp;            case -1 -&gt; INVALID_SSIN;</b>
<b class="fc">&nbsp;            case -2 -&gt; EXISTS;</b>
<b class="fc">&nbsp;            default -&gt; ERROR;</b>
&nbsp;        };
&nbsp;    };
&nbsp;
&nbsp;    @CloudEventMapping
&nbsp;    @Mapping(target = &quot;type&quot;, constant = &quot;be.fgov.onerva.person.msg.v1.PersonUpdated&quot;)
&nbsp;    @Mapping(target = &quot;data&quot;, expression = &quot;java(mapToUpdate(request, response))&quot;)
&nbsp;    PersonUpdatedPayload mapToCloudEventUpdate(PersonRequest request, PersonMfxResponse response);
&nbsp;
&nbsp;    @PersonEventMapping
&nbsp;    PersonUpdated mapToUpdate(PersonRequest request, PersonMfxResponse response);
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
