


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > UserApi</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.wave.api</a>
</div>

<h1>Coverage Summary for Class: UserApi (be.fgov.onerva.wave.api)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">UserApi</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/8)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/26)
  </span>
</td>
</tr>
  <tr>
    <td class="name">UserApi$1</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
  </tr>
  <tr>
    <td class="name">UserApi$2</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
  </tr>
  <tr>
    <td class="name">UserApi$3</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/11)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/29)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.wave.api;
&nbsp;
&nbsp;import be.fgov.onerva.wave.ApiClient;
&nbsp;
&nbsp;import be.fgov.onerva.wave.model.User;
&nbsp;import be.fgov.onerva.wave.model.UserCriteria;
&nbsp;
&nbsp;import java.util.HashMap;
&nbsp;import java.util.List;
&nbsp;import java.util.Locale;
&nbsp;import java.util.Map;
&nbsp;import java.util.stream.Collectors;
&nbsp;
&nbsp;import org.springframework.beans.factory.annotation.Autowired;
&nbsp;import org.springframework.util.LinkedMultiValueMap;
&nbsp;import org.springframework.util.MultiValueMap;
&nbsp;import org.springframework.core.ParameterizedTypeReference;
&nbsp;import org.springframework.web.client.RestClient.ResponseSpec;
&nbsp;import org.springframework.web.client.RestClientResponseException;
&nbsp;import org.springframework.core.io.FileSystemResource;
&nbsp;import org.springframework.http.HttpHeaders;
&nbsp;import org.springframework.http.HttpMethod;
&nbsp;import org.springframework.http.HttpStatus;
&nbsp;import org.springframework.http.MediaType;
&nbsp;import org.springframework.http.ResponseEntity;
&nbsp;
&nbsp;@jakarta.annotation.Generated(value = &quot;org.openapitools.codegen.languages.JavaClientCodegen&quot;, date = &quot;2025-07-16T14:11:32.899250600+02:00[Europe/Brussels]&quot;, comments = &quot;Generator version: 7.12.0&quot;)
&nbsp;public class UserApi {
&nbsp;    private ApiClient apiClient;
&nbsp;
&nbsp;    public UserApi() {
<b class="nc">&nbsp;        this(new ApiClient());</b>
&nbsp;    }
&nbsp;
&nbsp;    @Autowired
<b class="nc">&nbsp;    public UserApi(ApiClient apiClient) {</b>
<b class="nc">&nbsp;        this.apiClient = apiClient;</b>
&nbsp;    }
&nbsp;
&nbsp;    public ApiClient getApiClient() {
<b class="nc">&nbsp;        return apiClient;</b>
&nbsp;    }
&nbsp;
&nbsp;    public void setApiClient(ApiClient apiClient) {
<b class="nc">&nbsp;        this.apiClient = apiClient;</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * 
&nbsp;     * 
&nbsp;     * &lt;p&gt;&lt;b&gt;200&lt;/b&gt; - user fetched successfully
&nbsp;     * @param criteria The criteria parameter
&nbsp;     * @return User
&nbsp;     * @throws RestClientResponseException if an error occurs while attempting to invoke the API
&nbsp;     */
&nbsp;    private ResponseSpec searchUsersRequestCreation(UserCriteria criteria) throws RestClientResponseException {
<b class="nc">&nbsp;        Object postBody = null;</b>
&nbsp;        // create path and map variables
<b class="nc">&nbsp;        final Map&lt;String, Object&gt; pathParams = new HashMap&lt;&gt;();</b>
&nbsp;
<b class="nc">&nbsp;        final MultiValueMap&lt;String, String&gt; queryParams = new LinkedMultiValueMap&lt;&gt;();</b>
<b class="nc">&nbsp;        final HttpHeaders headerParams = new HttpHeaders();</b>
<b class="nc">&nbsp;        final MultiValueMap&lt;String, String&gt; cookieParams = new LinkedMultiValueMap&lt;&gt;();</b>
<b class="nc">&nbsp;        final MultiValueMap&lt;String, Object&gt; formParams = new LinkedMultiValueMap&lt;&gt;();</b>
&nbsp;
<b class="nc">&nbsp;        queryParams.putAll(apiClient.parameterToMultiValueMap(null, &quot;operatorCode&quot;, criteria.getOperatorCode()));</b>
<b class="nc">&nbsp;        queryParams.putAll(apiClient.parameterToMultiValueMap(null, &quot;username&quot;, criteria.getUsername()));</b>
<b class="nc">&nbsp;        queryParams.putAll(apiClient.parameterToMultiValueMap(null, &quot;inss&quot;, criteria.getInss()));</b>
&nbsp;        
<b class="nc">&nbsp;        final String[] localVarAccepts = { </b>
&nbsp;            &quot;application/json&quot;
&nbsp;        };
<b class="nc">&nbsp;        final List&lt;MediaType&gt; localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);</b>
<b class="nc">&nbsp;        final String[] localVarContentTypes = { };</b>
<b class="nc">&nbsp;        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);</b>
&nbsp;
<b class="nc">&nbsp;        String[] localVarAuthNames = new String[] {  };</b>
&nbsp;
<b class="nc">&nbsp;        ParameterizedTypeReference&lt;User&gt; localVarReturnType = new ParameterizedTypeReference&lt;&gt;() {};</b>
<b class="nc">&nbsp;        return apiClient.invokeAPI(&quot;/users&quot;, HttpMethod.GET, pathParams, queryParams, postBody, headerParams, cookieParams, formParams, localVarAccept, localVarContentType, localVarAuthNames, localVarReturnType);</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * 
&nbsp;     * 
&nbsp;     * &lt;p&gt;&lt;b&gt;200&lt;/b&gt; - user fetched successfully
&nbsp;     * @param criteria The criteria parameter
&nbsp;     * @return User
&nbsp;     * @throws RestClientResponseException if an error occurs while attempting to invoke the API
&nbsp;     */
&nbsp;    public User searchUsers(UserCriteria criteria) throws RestClientResponseException {
<b class="nc">&nbsp;        ParameterizedTypeReference&lt;User&gt; localVarReturnType = new ParameterizedTypeReference&lt;&gt;() {};</b>
<b class="nc">&nbsp;        return searchUsersRequestCreation(criteria).body(localVarReturnType);</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * 
&nbsp;     * 
&nbsp;     * &lt;p&gt;&lt;b&gt;200&lt;/b&gt; - user fetched successfully
&nbsp;     * @param criteria The criteria parameter
&nbsp;     * @return ResponseEntity&amp;lt;User&amp;gt;
&nbsp;     * @throws RestClientResponseException if an error occurs while attempting to invoke the API
&nbsp;     */
&nbsp;    public ResponseEntity&lt;User&gt; searchUsersWithHttpInfo(UserCriteria criteria) throws RestClientResponseException {
<b class="nc">&nbsp;        ParameterizedTypeReference&lt;User&gt; localVarReturnType = new ParameterizedTypeReference&lt;&gt;() {};</b>
<b class="nc">&nbsp;        return searchUsersRequestCreation(criteria).toEntity(localVarReturnType);</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * 
&nbsp;     * 
&nbsp;     * &lt;p&gt;&lt;b&gt;200&lt;/b&gt; - user fetched successfully
&nbsp;     * @param criteria The criteria parameter
&nbsp;     * @return ResponseSpec
&nbsp;     * @throws RestClientResponseException if an error occurs while attempting to invoke the API
&nbsp;     */
&nbsp;    public ResponseSpec searchUsersWithResponseSpec(UserCriteria criteria) throws RestClientResponseException {
<b class="nc">&nbsp;        return searchUsersRequestCreation(criteria);</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
