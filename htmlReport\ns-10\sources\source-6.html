


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > RabbitConfig</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.config</a>
</div>

<h1>Coverage Summary for Class: RabbitConfig (be.fgov.onerva.person.backend.config)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">RabbitConfig</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (4/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (4/4)
  </span>
</td>
</tr>
  <tr>
    <td class="name">RabbitConfig$OauthRabbitConfig</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/3)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/4)
  </span>
</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    57.1%
  </span>
  <span class="absValue">
    (4/7)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    50%
  </span>
  <span class="absValue">
    (4/8)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.config;
&nbsp;
&nbsp;import be.fgov.onerva.person.backend.config.props.RabbitmqOauthProperties;
&nbsp;import com.fasterxml.jackson.databind.ObjectMapper;
&nbsp;import com.rabbitmq.client.impl.CredentialsProvider;
&nbsp;import com.rabbitmq.client.impl.CredentialsRefreshService;
&nbsp;import com.rabbitmq.client.impl.DefaultCredentialsRefreshService;
&nbsp;import lombok.extern.slf4j.Slf4j;
&nbsp;import org.springframework.amqp.core.FanoutExchange;
&nbsp;import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
&nbsp;import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
&nbsp;import org.springframework.boot.context.properties.EnableConfigurationProperties;
&nbsp;import org.springframework.context.annotation.Bean;
&nbsp;import org.springframework.context.annotation.Configuration;
&nbsp;
<b class="fc">&nbsp;@Slf4j</b>
&nbsp;@Configuration(proxyBeanMethods = false)
<b class="fc">&nbsp;public class RabbitConfig {</b>
&nbsp;
&nbsp;
&nbsp;    @ConditionalOnProperty(prefix = RabbitmqOauthProperties.PREFIX, name = &quot;enabled&quot;)
&nbsp;    @EnableConfigurationProperties(RabbitmqOauthProperties.class)
&nbsp;    @Configuration
<b class="nc">&nbsp;    static class OauthRabbitConfig {</b>
&nbsp;
&nbsp;        @Bean
&nbsp;        public CredentialsProvider oauthCredentialsProvider(RabbitmqOauthProperties properties) {
<b class="nc">&nbsp;            return properties.toCredentialsProvider();</b>
&nbsp;        }
&nbsp;
&nbsp;        @Bean
&nbsp;        public CredentialsRefreshService credentialsRefreshService() {
<b class="nc">&nbsp;            return new DefaultCredentialsRefreshService.DefaultCredentialsRefreshServiceBuilder().</b>
<b class="nc">&nbsp;                    build();</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    @Bean
&nbsp;    public Jackson2JsonMessageConverter messageConverter(ObjectMapper objectMapper) {
<b class="fc">&nbsp;        return new Jackson2JsonMessageConverter(objectMapper);</b>
&nbsp;    }
&nbsp;
&nbsp;    @Bean
&nbsp;    public FanoutExchange personExchange() {
<b class="fc">&nbsp;        return new FanoutExchange(&quot;person.exchange&quot;, true, false);</b>
&nbsp;    }
&nbsp;
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
