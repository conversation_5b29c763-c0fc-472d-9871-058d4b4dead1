


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenInfoMapperV2</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.citizeninfo.v2.mapper</a>
</div>

<h1>Coverage Summary for Class: CitizenInfoMapperV2 (be.fgov.onerva.person.backend.citizeninfo.v2.mapper)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">CitizenInfoMapperV2</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
</tr>
  <tr>
    <td class="name">CitizenInfoMapperV2$MockitoMock$KWgXDptO</td>
  </tr>
  <tr>
    <td class="name">CitizenInfoMapperV2$MockitoMock$KWgXDptO$auxiliary$f8kG9hwc</td>
  </tr>
  <tr>
    <td class="name">CitizenInfoMapperV2$MockitoMock$KWgXDptO$auxiliary$ULacrlOc</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.citizeninfo.v2.mapper;
&nbsp;
&nbsp;import backend.rest.model.CitizenInfoPageV2DTO;
&nbsp;import backend.rest.model.CitizenInfoV2DTO;
&nbsp;import backend.rest.model.FlagDTO;
&nbsp;import be.fgov.onerva.person.backend.citizeninfo.model.CitizenInfoEntity;
&nbsp;import org.mapstruct.Mapper;
&nbsp;import org.mapstruct.Mapping;
&nbsp;import org.mapstruct.Named;
&nbsp;import org.springframework.data.domain.Page;
&nbsp;
&nbsp;@Mapper(componentModel = &quot;spring&quot;)
&nbsp;public interface CitizenInfoMapperV2 {
&nbsp;    @Mapping(target = &quot;numPens&quot;, source = &quot;numPens&quot;)
&nbsp;    @Mapping(target = &quot;numBox&quot;, source = &quot;numBox&quot;)
&nbsp;    @Mapping(target = &quot;flagPurge&quot;, source = &quot;flagPurge&quot;, qualifiedByName = &quot;convertFlag&quot;)
&nbsp;    @Mapping(target = &quot;ssin&quot;, ignore = true)
&nbsp;    @Mapping(target = &quot;firstName&quot;, ignore = true)
&nbsp;    @Mapping(target = &quot;lastName&quot;, ignore = true)
&nbsp;    @Mapping(target = &quot;address&quot;, ignore = true)
&nbsp;    @Mapping(target = &quot;postalCode&quot;, ignore = true)
&nbsp;    @Mapping(target = &quot;rvaCountryCode&quot;, ignore = true)
&nbsp;    @Mapping(target = &quot;OP&quot;, ignore = true)
&nbsp;    @Mapping(target = &quot;unemploymentOffice&quot;, ignore = true)
&nbsp;    @Mapping(target = &quot;deceasedDate&quot;, ignore = true)
&nbsp;    @Mapping(target = &quot;iban&quot;, ignore = true)
&nbsp;    @Mapping(target = &quot;language&quot;, ignore = true)
&nbsp;    @Mapping(target = &quot;sex&quot;, ignore = true)
&nbsp;    @Mapping(target = &quot;bisNumber&quot;, ignore = true)
&nbsp;    @Mapping(target = &quot;flagNation&quot;, ignore = true)
&nbsp;    @Mapping(target = &quot;telephoneOnem&quot;, ignore = true)
&nbsp;    @Mapping(target = &quot;gsmOnem&quot;, ignore = true)
&nbsp;    @Mapping(target = &quot;telephoneReg&quot;, ignore = true)
&nbsp;    @Mapping(target = &quot;gsmReg&quot;, ignore = true)
&nbsp;    @Mapping(target = &quot;email&quot;, ignore = true)
&nbsp;    @Mapping(target = &quot;id&quot;, ignore = true)
&nbsp;    @Mapping(target = &quot;flagVCpte&quot;, ignore = true)
&nbsp;    @Mapping(target = &quot;nationBcss&quot;, ignore = true)
&nbsp;    @Mapping(target = &quot;communeDateValid&quot;, ignore = true)
&nbsp;    @Mapping(target = &quot;dateMcpte&quot;, ignore = true)
&nbsp;    @Mapping(target = &quot;nationDateValid&quot;, ignore = true)
&nbsp;    CitizenInfoV2DTO map(CitizenInfoEntity entity);
&nbsp;
&nbsp;    @Named(&quot;convertFlag&quot;)
&nbsp;    static String convertFlag(boolean flag) {
<b class="nc">&nbsp;        return flag ? FlagDTO.n.name() : FlagDTO.y.name();</b>
&nbsp;    }
&nbsp;    
&nbsp;    @Mapping(source = &quot;number&quot;, target = &quot;pageNumber&quot;)
&nbsp;    @Mapping(source = &quot;size&quot;, target = &quot;pageSize&quot;)
&nbsp;    @Mapping(source = &quot;totalPages&quot;, target = &quot;totalPage&quot;)
&nbsp;    @Mapping(source = &quot;totalElements&quot;, target = &quot;totalElements&quot;)
&nbsp;    @Mapping(source = &quot;first&quot;, target = &quot;isFirst&quot;)
&nbsp;    @Mapping(source = &quot;last&quot;, target = &quot;isLast&quot;)
&nbsp;    CitizenInfoPageV2DTO mapPageToDto(Page&lt;CitizenInfoEntity&gt; source);
&nbsp;
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
