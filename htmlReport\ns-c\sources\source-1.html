


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenInfoV2Controller</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.citizeninfo.v2.controller</a>
</div>

<h1>Coverage Summary for Class: CitizenInfoV2Controller (be.fgov.onerva.person.backend.citizeninfo.v2.controller)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">CitizenInfoV2Controller</td>
<td class="coverageStat">
  <span class="percent">
    50%
  </span>
  <span class="absValue">
    (1/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    20%
  </span>
  <span class="absValue">
    (1/5)
  </span>
</td>
</tr>
  <tr>
    <td class="name">CitizenInfoV2Controller$$SpringCGLIB$$0</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    50%
  </span>
  <span class="absValue">
    (1/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    20%
  </span>
  <span class="absValue">
    (1/5)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.citizeninfo.v2.controller;
&nbsp;
&nbsp;import backend.api.CitizenInfoV2Api;
&nbsp;import backend.rest.model.CitizenInfoPageV2DTO;
&nbsp;import be.fgov.onerva.person.backend.citizeninfo.v2.service.CitizenInfoServiceV2;
&nbsp;import lombok.RequiredArgsConstructor;
&nbsp;import lombok.extern.slf4j.Slf4j;
&nbsp;import org.springframework.data.domain.PageRequest;
&nbsp;import org.springframework.data.domain.Sort;
&nbsp;import org.springframework.http.ResponseEntity;
&nbsp;import org.springframework.web.bind.annotation.CrossOrigin;
&nbsp;import org.springframework.web.bind.annotation.RequestMapping;
&nbsp;import org.springframework.web.bind.annotation.RestController;
&nbsp;
&nbsp;import java.util.List;
&nbsp;
&nbsp;import static org.springframework.http.ResponseEntity.ok;
&nbsp;
&nbsp;@RequiredArgsConstructor
&nbsp;@RestController
<b class="fc">&nbsp;@Slf4j</b>
&nbsp;@CrossOrigin(origins = &quot;*&quot;, maxAge = 3600)
&nbsp;@RequestMapping(&quot;/api&quot;)
&nbsp;public class CitizenInfoV2Controller implements CitizenInfoV2Api {
&nbsp;
&nbsp;    private final CitizenInfoServiceV2 citizenInfoServiceV2;
&nbsp;
&nbsp;    @Override
&nbsp;    public ResponseEntity&lt;CitizenInfoPageV2DTO&gt; searchCitizenInfov2(List&lt;String&gt; ssins,
&nbsp;                                                                    String dataReturned,
&nbsp;                                                                    Integer pageNumber,
&nbsp;                                                                    Integer pageSize) {
<b class="nc">&nbsp;        log.debug(&quot;searchCitizenInfov2: ssins={}, dataReturned={}, pageNumber={}, pageSize={}&quot;, ssins, dataReturned, pageNumber, pageSize);</b>
<b class="nc">&nbsp;        var citizenInfo = citizenInfoServiceV2.getCitizenInfoBySsinListV2(ssins, PageRequest.of(pageNumber, pageSize, Sort.by(Sort.Direction.ASC, &quot;numPens&quot;)));</b>
<b class="nc">&nbsp;        log.debug(&quot;searchCitizenInfov2: found {} citizenInfo&quot;, (citizenInfo!=null &amp;&amp; citizenInfo.getContent()!=null) ? citizenInfo.getContent().size() : 0);</b>
<b class="nc">&nbsp;        return ok(citizenInfo);</b>
&nbsp;    }
&nbsp;
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
