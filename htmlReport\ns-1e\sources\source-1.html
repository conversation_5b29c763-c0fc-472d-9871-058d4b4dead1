


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > ApiClient</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.wave</a>
</div>

<h1>Coverage Summary for Class: ApiClient (be.fgov.onerva.wave)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">ApiClient</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/48)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/126)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/200)
  </span>
</td>
</tr>
  <tr>
    <td class="name">ApiClient$CollectionFormat</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/5)
  </span>
</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/52)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/126)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/205)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;/*
&nbsp; * WO facade API
&nbsp; * API to manage tasks (and processes) in a simplified manner
&nbsp; *
&nbsp; * The version of the OpenAPI document: v1
&nbsp; * 
&nbsp; *
&nbsp; * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
&nbsp; * https://openapi-generator.tech
&nbsp; * Do not edit the class manually.
&nbsp; */
&nbsp;
&nbsp;
&nbsp;package be.fgov.onerva.wave;
&nbsp;
&nbsp;import com.fasterxml.jackson.databind.DeserializationFeature;
&nbsp;import com.fasterxml.jackson.databind.ObjectMapper;
&nbsp;import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
&nbsp;import java.util.function.Consumer;
&nbsp;import org.springframework.core.ParameterizedTypeReference;
&nbsp;import org.springframework.http.HttpHeaders;
&nbsp;import org.springframework.http.HttpMethod;
&nbsp;import org.springframework.http.InvalidMediaTypeException;
&nbsp;import org.springframework.http.MediaType;
&nbsp;import org.springframework.http.converter.HttpMessageConverter;
&nbsp;import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
&nbsp;import org.springframework.util.CollectionUtils;
&nbsp;import org.springframework.util.LinkedMultiValueMap;
&nbsp;import org.springframework.util.MultiValueMap;
&nbsp;import org.springframework.util.StringUtils;
&nbsp;import org.springframework.web.client.RestClientException;
&nbsp;import org.springframework.web.util.UriComponentsBuilder;
&nbsp;import org.springframework.web.client.RestClient;
&nbsp;import org.springframework.web.client.RestClient.ResponseSpec;
&nbsp;import java.util.Optional;
&nbsp;
&nbsp;import java.text.DateFormat;
&nbsp;import java.text.ParseException;
&nbsp;import java.util.ArrayList;
&nbsp;import java.util.Collection;
&nbsp;import java.util.Collections;
&nbsp;import java.util.Date;
&nbsp;import java.util.HashMap;
&nbsp;import java.util.List;
&nbsp;import java.util.Map;
&nbsp;import java.util.Map.Entry;
&nbsp;import java.util.TimeZone;
&nbsp;import java.util.function.Supplier;
&nbsp;
&nbsp;import jakarta.annotation.Nullable;
&nbsp;
&nbsp;import java.time.OffsetDateTime;
&nbsp;
&nbsp;import be.fgov.onerva.wave.auth.Authentication;
&nbsp;import be.fgov.onerva.wave.auth.HttpBasicAuth;
&nbsp;import be.fgov.onerva.wave.auth.HttpBearerAuth;
&nbsp;import be.fgov.onerva.wave.auth.ApiKeyAuth;
&nbsp;
&nbsp;@jakarta.annotation.Generated(value = &quot;org.openapitools.codegen.languages.JavaClientCodegen&quot;, date = &quot;2025-07-16T14:11:32.899250600+02:00[Europe/Brussels]&quot;, comments = &quot;Generator version: 7.12.0&quot;)
&nbsp;public class ApiClient extends JavaTimeFormatter {
<b class="nc">&nbsp;    public enum CollectionFormat {</b>
<b class="nc">&nbsp;        CSV(&quot;,&quot;), TSV(&quot;\t&quot;), SSV(&quot; &quot;), PIPES(&quot;|&quot;), MULTI(null);</b>
&nbsp;
&nbsp;        private final String separator;
<b class="nc">&nbsp;        CollectionFormat(String separator) {</b>
<b class="nc">&nbsp;            this.separator = separator;</b>
&nbsp;        }
&nbsp;
&nbsp;        private String collectionToString(Collection&lt;?&gt; collection) {
<b class="nc">&nbsp;            return StringUtils.collectionToDelimitedString(collection, separator);</b>
&nbsp;        }
&nbsp;    }
&nbsp;
<b class="nc">&nbsp;    private final HttpHeaders defaultHeaders = new HttpHeaders();</b>
<b class="nc">&nbsp;    private final MultiValueMap&lt;String, String&gt; defaultCookies = new LinkedMultiValueMap&lt;&gt;();</b>
&nbsp;
<b class="nc">&nbsp;    private String basePath = &quot;http://localhost&quot;;</b>
&nbsp;
&nbsp;    private final RestClient restClient;
&nbsp;    private final DateFormat dateFormat;
&nbsp;    private final ObjectMapper objectMapper;
&nbsp;
&nbsp;    private Map&lt;String, Authentication&gt; authentications;
&nbsp;
&nbsp;
&nbsp;    public ApiClient() {
<b class="nc">&nbsp;        this(null);</b>
&nbsp;    }
&nbsp;
&nbsp;    public ApiClient(RestClient restClient) {
<b class="nc">&nbsp;        this(restClient, createDefaultDateFormat());</b>
&nbsp;    }
&nbsp;
&nbsp;    public ApiClient(ObjectMapper mapper, DateFormat format) {
<b class="nc">&nbsp;        this(null, mapper, format);</b>
&nbsp;    }
&nbsp;
<b class="nc">&nbsp;    public ApiClient(RestClient restClient, ObjectMapper mapper, DateFormat format) {</b>
<b class="nc">&nbsp;        this.objectMapper = mapper.copy();</b>
<b class="nc">&nbsp;        this.restClient = Optional.ofNullable(restClient).orElseGet(() -&gt; buildRestClient(this.objectMapper));</b>
<b class="nc">&nbsp;        this.dateFormat = format;</b>
<b class="nc">&nbsp;        this.init();</b>
&nbsp;    }
&nbsp;
&nbsp;    private ApiClient(RestClient restClient, DateFormat format) {
<b class="nc">&nbsp;        this(restClient, createDefaultObjectMapper(format), format);</b>
&nbsp;    }
&nbsp;
&nbsp;    public static DateFormat createDefaultDateFormat() {
<b class="nc">&nbsp;        DateFormat dateFormat = new RFC3339DateFormat();</b>
<b class="nc">&nbsp;        dateFormat.setTimeZone(TimeZone.getTimeZone(&quot;UTC&quot;));</b>
<b class="nc">&nbsp;        return dateFormat;</b>
&nbsp;    }
&nbsp;
&nbsp;    public static ObjectMapper createDefaultObjectMapper(@Nullable DateFormat dateFormat) {
<b class="nc">&nbsp;        if (null == dateFormat) {</b>
<b class="nc">&nbsp;            dateFormat = createDefaultDateFormat();</b>
&nbsp;        }
<b class="nc">&nbsp;        ObjectMapper mapper = new ObjectMapper();</b>
<b class="nc">&nbsp;        mapper.setDateFormat(dateFormat);</b>
<b class="nc">&nbsp;        mapper.registerModule(new JavaTimeModule());</b>
<b class="nc">&nbsp;        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);</b>
<b class="nc">&nbsp;        return mapper;</b>
&nbsp;    }
&nbsp;
&nbsp;    protected void init() {
&nbsp;        // Setup authentications (key: authentication name, value: authentication).
<b class="nc">&nbsp;        authentications = new HashMap&lt;&gt;();</b>
&nbsp;        // Prevent the authentications from being modified.
<b class="nc">&nbsp;        authentications = Collections.unmodifiableMap(authentications);</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;    * Build the RestClientBuilder used to make RestClient.
&nbsp;    * @param mapper ObjectMapper used for serialize/deserialize
&nbsp;    * @return RestClient
&nbsp;    */
&nbsp;    public static RestClient.Builder buildRestClientBuilder(ObjectMapper mapper) {
<b class="nc">&nbsp;        Consumer&lt;List&lt;HttpMessageConverter&lt;?&gt;&gt;&gt; messageConverters = converters -&gt; {</b>
<b class="nc">&nbsp;            converters.add(0, new MappingJackson2HttpMessageConverter(mapper));</b>
&nbsp;        };
&nbsp;
<b class="nc">&nbsp;        return RestClient.builder().messageConverters(messageConverters);</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Build the RestClientBuilder used to make RestClient.
&nbsp;     * @return RestClient
&nbsp;     */
&nbsp;    public static RestClient.Builder buildRestClientBuilder() {
<b class="nc">&nbsp;        return buildRestClientBuilder(createDefaultObjectMapper(null));</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Build the RestClient used to make HTTP requests.
&nbsp;     * @param mapper ObjectMapper used for serialize/deserialize
&nbsp;     * @return RestClient
&nbsp;     */
&nbsp;    public static RestClient buildRestClient(ObjectMapper mapper) {
<b class="nc">&nbsp;        return buildRestClientBuilder(mapper).build();</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Build the RestClient used to make HTTP requests.
&nbsp;     * @return RestClient
&nbsp;     */
&nbsp;    public static RestClient buildRestClient() {
<b class="nc">&nbsp;        return buildRestClientBuilder(createDefaultObjectMapper(null)).build();</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Get the current base path
&nbsp;     * @return String the base path
&nbsp;     */
&nbsp;    public String getBasePath() {
<b class="nc">&nbsp;        return basePath;</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Set the base path, which should include the host
&nbsp;     * @param basePath the base path
&nbsp;     * @return ApiClient this client
&nbsp;     */
&nbsp;    public ApiClient setBasePath(String basePath) {
<b class="nc">&nbsp;        this.basePath = basePath;</b>
<b class="nc">&nbsp;        return this;</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Get authentications (key: authentication name, value: authentication).
&nbsp;     * @return Map the currently configured authentication types
&nbsp;     */
&nbsp;    public Map&lt;String, Authentication&gt; getAuthentications() {
<b class="nc">&nbsp;        return authentications;</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Get authentication for the given name.
&nbsp;     *
&nbsp;     * @param authName The authentication name
&nbsp;     * @return The authentication, null if not found
&nbsp;     */
&nbsp;    public Authentication getAuthentication(String authName) {
<b class="nc">&nbsp;        return authentications.get(authName);</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Helper method to set access token for the first Bearer authentication.
&nbsp;     * @param bearerToken Bearer token
&nbsp;     */
&nbsp;    public void setBearerToken(String bearerToken) {
<b class="nc">&nbsp;        for (Authentication auth : authentications.values()) {</b>
<b class="nc">&nbsp;            if (auth instanceof HttpBearerAuth) {</b>
<b class="nc">&nbsp;                ((HttpBearerAuth) auth).setBearerToken(bearerToken);</b>
&nbsp;                return;
&nbsp;            }
&nbsp;        }
<b class="nc">&nbsp;        throw new RuntimeException(&quot;No Bearer authentication configured!&quot;);</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;    * Helper method to set the supplier of access tokens for Bearer authentication.
&nbsp;    *
&nbsp;    * @param tokenSupplier the token supplier function
&nbsp;    */
&nbsp;    public void setBearerToken(Supplier&lt;String&gt; tokenSupplier) {
<b class="nc">&nbsp;        for (Authentication auth : authentications.values()) {</b>
<b class="nc">&nbsp;            if (auth instanceof HttpBearerAuth) {</b>
<b class="nc">&nbsp;                ((HttpBearerAuth) auth).setBearerToken(tokenSupplier);</b>
&nbsp;            return;
&nbsp;            }
&nbsp;        }
<b class="nc">&nbsp;        throw new RuntimeException(&quot;No Bearer authentication configured!&quot;);</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Helper method to set username for the first HTTP basic authentication.
&nbsp;     * @param username the username
&nbsp;     */
&nbsp;    public void setUsername(String username) {
<b class="nc">&nbsp;        for (Authentication auth : authentications.values()) {</b>
<b class="nc">&nbsp;            if (auth instanceof HttpBasicAuth) {</b>
<b class="nc">&nbsp;                ((HttpBasicAuth) auth).setUsername(username);</b>
&nbsp;                return;
&nbsp;            }
&nbsp;        }
<b class="nc">&nbsp;        throw new RuntimeException(&quot;No HTTP basic authentication configured!&quot;);</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Helper method to set password for the first HTTP basic authentication.
&nbsp;     * @param password the password
&nbsp;     */
&nbsp;    public void setPassword(String password) {
<b class="nc">&nbsp;        for (Authentication auth : authentications.values()) {</b>
<b class="nc">&nbsp;            if (auth instanceof HttpBasicAuth) {</b>
<b class="nc">&nbsp;                ((HttpBasicAuth) auth).setPassword(password);</b>
&nbsp;                return;
&nbsp;            }
&nbsp;        }
<b class="nc">&nbsp;        throw new RuntimeException(&quot;No HTTP basic authentication configured!&quot;);</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Helper method to set API key value for the first API key authentication.
&nbsp;     * @param apiKey the API key
&nbsp;     */
&nbsp;    public void setApiKey(String apiKey) {
<b class="nc">&nbsp;        for (Authentication auth : authentications.values()) {</b>
<b class="nc">&nbsp;            if (auth instanceof ApiKeyAuth) {</b>
<b class="nc">&nbsp;                ((ApiKeyAuth) auth).setApiKey(apiKey);</b>
&nbsp;                return;
&nbsp;            }
&nbsp;        }
<b class="nc">&nbsp;        throw new RuntimeException(&quot;No API key authentication configured!&quot;);</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Helper method to set API key prefix for the first API key authentication.
&nbsp;     * @param apiKeyPrefix the API key prefix
&nbsp;     */
&nbsp;    public void setApiKeyPrefix(String apiKeyPrefix) {
<b class="nc">&nbsp;        for (Authentication auth : authentications.values()) {</b>
<b class="nc">&nbsp;            if (auth instanceof ApiKeyAuth) {</b>
<b class="nc">&nbsp;                ((ApiKeyAuth) auth).setApiKeyPrefix(apiKeyPrefix);</b>
&nbsp;                return;
&nbsp;            }
&nbsp;        }
<b class="nc">&nbsp;        throw new RuntimeException(&quot;No API key authentication configured!&quot;);</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Set the User-Agent header&#39;s value (by adding to the default header map).
&nbsp;     * @param userAgent the user agent string
&nbsp;     * @return ApiClient this client
&nbsp;     */
&nbsp;    public ApiClient setUserAgent(String userAgent) {
<b class="nc">&nbsp;        addDefaultHeader(&quot;User-Agent&quot;, userAgent);</b>
<b class="nc">&nbsp;        return this;</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Add a default header.
&nbsp;     *
&nbsp;     * @param name The header&#39;s name
&nbsp;     * @param value The header&#39;s value
&nbsp;     * @return ApiClient this client
&nbsp;     */
&nbsp;    public ApiClient addDefaultHeader(String name, String value) {
<b class="nc">&nbsp;        if (defaultHeaders.containsKey(name)) {</b>
<b class="nc">&nbsp;            defaultHeaders.remove(name);</b>
&nbsp;        }
<b class="nc">&nbsp;        defaultHeaders.add(name, value);</b>
<b class="nc">&nbsp;        return this;</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Add a default cookie.
&nbsp;     *
&nbsp;     * @param name The cookie&#39;s name
&nbsp;     * @param value The cookie&#39;s value
&nbsp;     * @return ApiClient this client
&nbsp;     */
&nbsp;    public ApiClient addDefaultCookie(String name, String value) {
<b class="nc">&nbsp;        if (defaultCookies.containsKey(name)) {</b>
<b class="nc">&nbsp;            defaultCookies.remove(name);</b>
&nbsp;        }
<b class="nc">&nbsp;        defaultCookies.add(name, value);</b>
<b class="nc">&nbsp;        return this;</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Get the date format used to parse/format date parameters.
&nbsp;     * @return DateFormat format
&nbsp;     */
&nbsp;    public DateFormat getDateFormat() {
<b class="nc">&nbsp;        return dateFormat;</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Parse the given string into Date object.
&nbsp;     */
&nbsp;    public Date parseDate(String str) {
&nbsp;        try {
<b class="nc">&nbsp;            return dateFormat.parse(str);</b>
&nbsp;        } catch (ParseException e) {
<b class="nc">&nbsp;            throw new RuntimeException(e);</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Format the given Date object into string.
&nbsp;     */
&nbsp;    public String formatDate(Date date) {
<b class="nc">&nbsp;        return dateFormat.format(date);</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Get the ObjectMapper used to make HTTP requests.
&nbsp;     * @return ObjectMapper objectMapper
&nbsp;     */
&nbsp;    public ObjectMapper getObjectMapper() {
<b class="nc">&nbsp;        return objectMapper;</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Get the RestClient used to make HTTP requests.
&nbsp;     * @return RestClient restClient
&nbsp;     */
&nbsp;    public RestClient getRestClient() {
<b class="nc">&nbsp;        return restClient;</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Format the given parameter object into string.
&nbsp;     * @param param the object to convert
&nbsp;     * @return String the parameter represented as a String
&nbsp;     */
&nbsp;    public String parameterToString(Object param) {
<b class="nc">&nbsp;        if (param == null) {</b>
<b class="nc">&nbsp;            return &quot;&quot;;</b>
<b class="nc">&nbsp;        } else if (param instanceof Date) {</b>
<b class="nc">&nbsp;            return formatDate( (Date) param);</b>
<b class="nc">&nbsp;        } else if (param instanceof OffsetDateTime) {</b>
<b class="nc">&nbsp;            return formatOffsetDateTime((OffsetDateTime) param);</b>
<b class="nc">&nbsp;        } else if (param instanceof Collection) {</b>
<b class="nc">&nbsp;            StringBuilder b = new StringBuilder();</b>
<b class="nc">&nbsp;            for(Object o : (Collection&lt;?&gt;) param) {</b>
<b class="nc">&nbsp;                if(b.length() &gt; 0) {</b>
<b class="nc">&nbsp;                    b.append(&quot;,&quot;);</b>
&nbsp;                }
<b class="nc">&nbsp;                b.append(String.valueOf(o));</b>
&nbsp;            }
<b class="nc">&nbsp;            return b.toString();</b>
&nbsp;        } else {
<b class="nc">&nbsp;            return String.valueOf(param);</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Converts a parameter to a {@link MultiValueMap} for use in REST requests
&nbsp;     * @param collectionFormat The format to convert to
&nbsp;     * @param name The name of the parameter
&nbsp;     * @param value The parameter&#39;s value
&nbsp;     * @return a Map containing the String value(s) of the input parameter
&nbsp;     */
&nbsp;    public MultiValueMap&lt;String, String&gt; parameterToMultiValueMap(CollectionFormat collectionFormat, String name, Object value) {
<b class="nc">&nbsp;        final MultiValueMap&lt;String, String&gt; params = new LinkedMultiValueMap&lt;&gt;();</b>
&nbsp;
<b class="nc">&nbsp;        if (name == null || name.isEmpty() || value == null) {</b>
<b class="nc">&nbsp;            return params;</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        if(collectionFormat == null) {</b>
<b class="nc">&nbsp;            collectionFormat = CollectionFormat.CSV;</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        if (value instanceof Map) {</b>
&nbsp;            @SuppressWarnings(&quot;unchecked&quot;)
<b class="nc">&nbsp;            final Map&lt;String, Object&gt; valuesMap = (Map&lt;String, Object&gt;) value;</b>
<b class="nc">&nbsp;            for (final Entry&lt;String, Object&gt; entry : valuesMap.entrySet()) {</b>
<b class="nc">&nbsp;                params.add(entry.getKey(), parameterToString(entry.getValue()));</b>
&nbsp;            }
<b class="nc">&nbsp;            return params;</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        Collection&lt;?&gt; valueCollection = null;</b>
<b class="nc">&nbsp;        if (value instanceof Collection) {</b>
<b class="nc">&nbsp;            valueCollection = (Collection&lt;?&gt;) value;</b>
&nbsp;        } else {
<b class="nc">&nbsp;            params.add(name, parameterToString(value));</b>
<b class="nc">&nbsp;            return params;</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        if (valueCollection.isEmpty()){</b>
<b class="nc">&nbsp;            return params;</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        if (collectionFormat.equals(CollectionFormat.MULTI)) {</b>
<b class="nc">&nbsp;            for (Object item : valueCollection) {</b>
<b class="nc">&nbsp;                params.add(name, parameterToString(item));</b>
&nbsp;            }
<b class="nc">&nbsp;            return params;</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        List&lt;String&gt; values = new ArrayList&lt;&gt;();</b>
<b class="nc">&nbsp;        for(Object o : valueCollection) {</b>
<b class="nc">&nbsp;            values.add(parameterToString(o));</b>
&nbsp;        }
<b class="nc">&nbsp;        params.add(name, collectionFormat.collectionToString(values));</b>
&nbsp;
<b class="nc">&nbsp;        return params;</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;    * Check if the given {@code String} is a JSON MIME.
&nbsp;    * @param mediaType the input MediaType
&nbsp;    * @return boolean true if the MediaType represents JSON, false otherwise
&nbsp;    */
&nbsp;    public boolean isJsonMime(String mediaType) {
&nbsp;        // &quot;* / *&quot; is default to JSON
<b class="nc">&nbsp;        if (&quot;*/*&quot;.equals(mediaType)) {</b>
<b class="nc">&nbsp;            return true;</b>
&nbsp;        }
&nbsp;
&nbsp;        try {
<b class="nc">&nbsp;            return isJsonMime(MediaType.parseMediaType(mediaType));</b>
&nbsp;        } catch (InvalidMediaTypeException e) {
&nbsp;        }
<b class="nc">&nbsp;        return false;</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Check if the given MIME is a JSON MIME.
&nbsp;     * JSON MIME examples:
&nbsp;     *     application/json
&nbsp;     *     application/json; charset=UTF8
&nbsp;     *     APPLICATION/JSON
&nbsp;     * @param mediaType the input MediaType
&nbsp;     * @return boolean true if the MediaType represents JSON, false otherwise
&nbsp;     */
&nbsp;    public boolean isJsonMime(MediaType mediaType) {
<b class="nc">&nbsp;        return mediaType != null &amp;&amp; (MediaType.APPLICATION_JSON.isCompatibleWith(mediaType) || mediaType.getSubtype().matches(&quot;^.*(\\+json|ndjson)[;]?\\s*$&quot;));</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;    * Check if the given {@code String} is a Problem JSON MIME (RFC-7807).
&nbsp;    * @param mediaType the input MediaType
&nbsp;    * @return boolean true if the MediaType represents Problem JSON, false otherwise
&nbsp;    */
&nbsp;    public boolean isProblemJsonMime(String mediaType) {
<b class="nc">&nbsp;        return &quot;application/problem+json&quot;.equalsIgnoreCase(mediaType);</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Select the Accept header&#39;s value from the given accepts array:
&nbsp;     *     if JSON exists in the given array, use it;
&nbsp;     *     otherwise use all of them (joining into a string)
&nbsp;     *
&nbsp;     * @param accepts The accepts array to select from
&nbsp;     * @return List The list of MediaTypes to use for the Accept header
&nbsp;     */
&nbsp;    public List&lt;MediaType&gt; selectHeaderAccept(String[] accepts) {
<b class="nc">&nbsp;        if (accepts.length == 0) {</b>
<b class="nc">&nbsp;            return null;</b>
&nbsp;        }
<b class="nc">&nbsp;        for (String accept : accepts) {</b>
<b class="nc">&nbsp;            MediaType mediaType = MediaType.parseMediaType(accept);</b>
<b class="nc">&nbsp;            if (isJsonMime(mediaType) &amp;&amp; !isProblemJsonMime(accept)) {</b>
<b class="nc">&nbsp;                return Collections.singletonList(mediaType);</b>
&nbsp;            }
&nbsp;        }
<b class="nc">&nbsp;        return MediaType.parseMediaTypes(StringUtils.arrayToCommaDelimitedString(accepts));</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Select the Content-Type header&#39;s value from the given array:
&nbsp;     *     if JSON exists in the given array, use it;
&nbsp;     *     otherwise use the first one of the array.
&nbsp;     *
&nbsp;     * @param contentTypes The Content-Type array to select from
&nbsp;     * @return MediaType The Content-Type header to use. If the given array is empty, null will be returned.
&nbsp;     */
&nbsp;    public MediaType selectHeaderContentType(String[] contentTypes) {
<b class="nc">&nbsp;        if (contentTypes.length == 0) {</b>
<b class="nc">&nbsp;            return null;</b>
&nbsp;        }
<b class="nc">&nbsp;        for (String contentType : contentTypes) {</b>
<b class="nc">&nbsp;            MediaType mediaType = MediaType.parseMediaType(contentType);</b>
<b class="nc">&nbsp;            if (isJsonMime(mediaType)) {</b>
<b class="nc">&nbsp;                return mediaType;</b>
&nbsp;            }
&nbsp;        }
<b class="nc">&nbsp;        return MediaType.parseMediaType(contentTypes[0]);</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;    * Select the body to use for the request
&nbsp;    *
&nbsp;    * @param obj the body object
&nbsp;    * @param formParams the form parameters
&nbsp;    * @param contentType the content type of the request
&nbsp;    * @return Object the selected body
&nbsp;    */
&nbsp;    protected Object selectBody(Object obj, MultiValueMap&lt;String, Object&gt; formParams, MediaType contentType) {
<b class="nc">&nbsp;        boolean isForm = MediaType.MULTIPART_FORM_DATA.isCompatibleWith(contentType) || MediaType.APPLICATION_FORM_URLENCODED.isCompatibleWith(contentType);</b>
<b class="nc">&nbsp;        return isForm ? formParams : obj;</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Invoke API by sending HTTP request with the given options.
&nbsp;     *
&nbsp;     * @param &lt;T&gt; the return type to use
&nbsp;     * @param path The sub-path of the HTTP URL
&nbsp;     * @param method The request method
&nbsp;     * @param pathParams The path parameters
&nbsp;     * @param queryParams The query parameters
&nbsp;     * @param body The request body object
&nbsp;     * @param headerParams The header parameters
&nbsp;     * @param formParams The form parameters
&nbsp;     * @param accept The request&#39;s Accept header
&nbsp;     * @param contentType The request&#39;s Content-Type header
&nbsp;     * @param authNames The authentications to apply
&nbsp;     * @param returnType The return type into which to deserialize the response
&nbsp;     * @return The response body in chosen type
&nbsp;     */
&nbsp;    public &lt;T&gt; ResponseSpec invokeAPI(String path, HttpMethod method, Map&lt;String, Object&gt; pathParams, MultiValueMap&lt;String, String&gt; queryParams, Object body, HttpHeaders headerParams, MultiValueMap&lt;String, String&gt; cookieParams, MultiValueMap&lt;String, Object&gt; formParams, List&lt;MediaType&gt; accept, MediaType contentType, String[] authNames, ParameterizedTypeReference&lt;T&gt; returnType) throws RestClientException {
<b class="nc">&nbsp;        final RestClient.RequestBodySpec requestBuilder = prepareRequest(path, method, pathParams, queryParams, body, headerParams, cookieParams, formParams, accept, contentType, authNames);</b>
<b class="nc">&nbsp;        return requestBuilder.retrieve();</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Include queryParams in uriParams taking into account the paramName
&nbsp;     * @param queryParams The query parameters
&nbsp;     * @param uriParams The path parameters
&nbsp;     * return templatized query string
&nbsp;     */
&nbsp;    private String generateQueryUri(MultiValueMap&lt;String, String&gt; queryParams, Map&lt;String, Object&gt; uriParams) {
<b class="nc">&nbsp;        StringBuilder queryBuilder = new StringBuilder();</b>
<b class="nc">&nbsp;        queryParams.forEach((name, values) -&gt; {</b>
<b class="nc">&nbsp;            if (CollectionUtils.isEmpty(values)) {</b>
<b class="nc">&nbsp;                if (queryBuilder.length() != 0) {</b>
<b class="nc">&nbsp;                    queryBuilder.append(&#39;&amp;&#39;);</b>
&nbsp;                }
<b class="nc">&nbsp;                queryBuilder.append(name);</b>
&nbsp;            } else {
<b class="nc">&nbsp;                int valueItemCounter = 0;</b>
<b class="nc">&nbsp;                for (Object value : values) {</b>
<b class="nc">&nbsp;                    if (queryBuilder.length() != 0) {</b>
<b class="nc">&nbsp;                        queryBuilder.append(&#39;&amp;&#39;);</b>
&nbsp;                    }
<b class="nc">&nbsp;                    queryBuilder.append(name);</b>
<b class="nc">&nbsp;                    if (value != null) {</b>
<b class="nc">&nbsp;                        String templatizedKey = name + valueItemCounter++;</b>
<b class="nc">&nbsp;                        uriParams.put(templatizedKey, value.toString());</b>
<b class="nc">&nbsp;                        queryBuilder.append(&#39;=&#39;).append(&quot;{&quot;).append(templatizedKey).append(&quot;}&quot;);</b>
&nbsp;                    }
&nbsp;                }
&nbsp;            }
&nbsp;        });
<b class="nc">&nbsp;        return queryBuilder.toString();</b>
&nbsp;    }
&nbsp;
&nbsp;    private RestClient.RequestBodySpec prepareRequest(String path, HttpMethod method, Map&lt;String, Object&gt; pathParams,
&nbsp;        MultiValueMap&lt;String, String&gt; queryParams, Object body, HttpHeaders headerParams,
&nbsp;        MultiValueMap&lt;String, String&gt; cookieParams, MultiValueMap&lt;String, Object&gt; formParams, List&lt;MediaType&gt; accept,
&nbsp;        MediaType contentType, String[] authNames) {
<b class="nc">&nbsp;        updateParamsForAuth(authNames, queryParams, headerParams, cookieParams);</b>
&nbsp;
<b class="nc">&nbsp;        final UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(basePath).path(path);</b>
&nbsp;
<b class="nc">&nbsp;        String finalUri = builder.build(false).toUriString();</b>
<b class="nc">&nbsp;        Map&lt;String, Object&gt; uriParams = new HashMap&lt;&gt;();</b>
<b class="nc">&nbsp;        uriParams.putAll(pathParams);</b>
&nbsp;
<b class="nc">&nbsp;        if (queryParams != null &amp;&amp; !queryParams.isEmpty()) {</b>
&nbsp;            //Include queryParams in uriParams taking into account the paramName
<b class="nc">&nbsp;            String queryUri = generateQueryUri(queryParams, uriParams);</b>
&nbsp;            //Append to finalUri the templatized query string like &quot;?param1={param1Value}&amp;.......
<b class="nc">&nbsp;            finalUri += &quot;?&quot; + queryUri;</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        final RestClient.RequestBodySpec requestBuilder = restClient.method(method).uri(finalUri, uriParams);</b>
&nbsp;
<b class="nc">&nbsp;        if (accept != null) {</b>
<b class="nc">&nbsp;            requestBuilder.accept(accept.toArray(new MediaType[accept.size()]));</b>
&nbsp;        }
<b class="nc">&nbsp;        if(contentType != null) {</b>
<b class="nc">&nbsp;            requestBuilder.contentType(contentType);</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        addHeadersToRequest(headerParams, requestBuilder);</b>
<b class="nc">&nbsp;        addHeadersToRequest(defaultHeaders, requestBuilder);</b>
<b class="nc">&nbsp;        addCookiesToRequest(cookieParams, requestBuilder);</b>
<b class="nc">&nbsp;        addCookiesToRequest(defaultCookies, requestBuilder);</b>
&nbsp;
<b class="nc">&nbsp;        var selectedBody = selectBody(body, formParams, contentType);</b>
<b class="nc">&nbsp;        if (selectedBody != null) {</b>
<b class="nc">&nbsp;          requestBuilder.body(selectedBody);</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        return requestBuilder;</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Add headers to the request that is being built
&nbsp;     * @param headers The headers to add
&nbsp;     * @param requestBuilder The current request
&nbsp;     */
&nbsp;    protected void addHeadersToRequest(HttpHeaders headers, RestClient.RequestBodySpec requestBuilder) {
<b class="nc">&nbsp;        for (Entry&lt;String, List&lt;String&gt;&gt; entry : headers.entrySet()) {</b>
<b class="nc">&nbsp;            List&lt;String&gt; values = entry.getValue();</b>
<b class="nc">&nbsp;            for(String value : values) {</b>
<b class="nc">&nbsp;                if (value != null) {</b>
<b class="nc">&nbsp;                    requestBuilder.header(entry.getKey(), value);</b>
&nbsp;                }
&nbsp;            }
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;     /**
&nbsp;     * Add cookies to the request that is being built
&nbsp;     *
&nbsp;     * @param cookies        The cookies to add
&nbsp;     * @param requestBuilder The current request
&nbsp;     */
&nbsp;    protected void addCookiesToRequest(MultiValueMap&lt;String, String&gt; cookies, RestClient.RequestBodySpec requestBuilder) {
<b class="nc">&nbsp;        if (!cookies.isEmpty()) {</b>
<b class="nc">&nbsp;            requestBuilder.header(&quot;Cookie&quot;, buildCookieHeader(cookies));</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Build cookie header. Keeps a single value per cookie (as per &lt;a href=&quot;https://tools.ietf.org/html/rfc6265#section-5.3&quot;&gt;
&nbsp;     * RFC6265 section 5.3&lt;/a&gt;).
&nbsp;     *
&nbsp;     * @param cookies map all cookies
&nbsp;     * @return header string for cookies.
&nbsp;     */
&nbsp;    private String buildCookieHeader(MultiValueMap&lt;String, String&gt; cookies) {
<b class="nc">&nbsp;        final StringBuilder cookieValue = new StringBuilder();</b>
<b class="nc">&nbsp;        String delimiter = &quot;&quot;;</b>
<b class="nc">&nbsp;        for (final Map.Entry&lt;String, List&lt;String&gt;&gt; entry : cookies.entrySet()) {</b>
<b class="nc">&nbsp;            final String value = entry.getValue().get(entry.getValue().size() - 1);</b>
<b class="nc">&nbsp;            cookieValue.append(String.format(&quot;%s%s=%s&quot;, delimiter, entry.getKey(), value));</b>
<b class="nc">&nbsp;            delimiter = &quot;; &quot;;</b>
&nbsp;        }
<b class="nc">&nbsp;        return cookieValue.toString();</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Update query and header parameters based on authentication settings.
&nbsp;     *
&nbsp;     * @param authNames The authentications to apply
&nbsp;     * @param queryParams The query parameters
&nbsp;     * @param headerParams The header parameters
&nbsp;     * @param cookieParams the cookie parameters
&nbsp;     */
&nbsp;    protected void updateParamsForAuth(String[] authNames, MultiValueMap&lt;String, String&gt; queryParams, HttpHeaders headerParams, MultiValueMap&lt;String, String&gt; cookieParams) {
<b class="nc">&nbsp;        for (String authName : authNames) {</b>
<b class="nc">&nbsp;            Authentication auth = authentications.get(authName);</b>
<b class="nc">&nbsp;            if (auth == null) {</b>
<b class="nc">&nbsp;                throw new RestClientException(&quot;Authentication undefined: &quot; + authName);</b>
&nbsp;            }
<b class="nc">&nbsp;            auth.applyToParams(queryParams, headerParams, cookieParams);</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;    * Formats the specified collection path parameter to a string value.
&nbsp;    *
&nbsp;    * @param collectionFormat The collection format of the parameter.
&nbsp;    * @param values The values of the parameter.
&nbsp;    * @return String representation of the parameter
&nbsp;    */
&nbsp;    public String collectionPathParameterToString(CollectionFormat collectionFormat, Collection&lt;?&gt; values) {
&nbsp;        // create the value based on the collection format
<b class="nc">&nbsp;        if (CollectionFormat.MULTI.equals(collectionFormat)) {</b>
&nbsp;            // not valid for path params
<b class="nc">&nbsp;            return parameterToString(values);</b>
&nbsp;        }
&nbsp;
&nbsp;         // collectionFormat is assumed to be &quot;csv&quot; by default
<b class="nc">&nbsp;        if(collectionFormat == null) {</b>
<b class="nc">&nbsp;            collectionFormat = CollectionFormat.CSV;</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        return collectionFormat.collectionToString(values);</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
