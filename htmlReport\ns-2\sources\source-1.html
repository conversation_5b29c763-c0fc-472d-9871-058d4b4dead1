


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > AddressDTO</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">backend.rest.model</a>
</div>

<h1>Coverage Summary for Class: AddressDTO (backend.rest.model)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">AddressDTO</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    54.2%
  </span>
  <span class="absValue">
    (13/24)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/18)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    25.5%
  </span>
  <span class="absValue">
    (14/55)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package backend.rest.model;
&nbsp;
&nbsp;import java.net.URI;
&nbsp;import java.util.Objects;
&nbsp;import com.fasterxml.jackson.annotation.JsonProperty;
&nbsp;import com.fasterxml.jackson.annotation.JsonCreator;
&nbsp;import com.fasterxml.jackson.annotation.JsonTypeName;
&nbsp;import org.springframework.lang.Nullable;
&nbsp;import java.time.OffsetDateTime;
&nbsp;import jakarta.validation.Valid;
&nbsp;import jakarta.validation.constraints.*;
&nbsp;import org.hibernate.validator.constraints.*;
&nbsp;import io.swagger.v3.oas.annotations.media.Schema;
&nbsp;
&nbsp;
&nbsp;import java.util.*;
&nbsp;import jakarta.annotation.Generated;
&nbsp;
&nbsp;/**
&nbsp; * AddressDTO
&nbsp; */
&nbsp;
&nbsp;@JsonTypeName(&quot;Address&quot;)
&nbsp;@Generated(value = &quot;org.openapitools.codegen.languages.SpringCodegen&quot;, date = &quot;2025-07-16T14:11:30.627767400+02:00[Europe/Brussels]&quot;, comments = &quot;Generator version: 7.12.0&quot;)
&nbsp;public class AddressDTO {
&nbsp;
&nbsp;  private String street;
&nbsp;
&nbsp;  private @Nullable String number;
&nbsp;
&nbsp;  private @Nullable String box;
&nbsp;
&nbsp;  private String zip;
&nbsp;
&nbsp;  private String city;
&nbsp;
<b class="fc">&nbsp;  private Integer countryCode = 1;</b>
&nbsp;
&nbsp;  public AddressDTO() {
<b class="fc">&nbsp;    super();</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Constructor with only required parameters
&nbsp;   */
<b class="nc">&nbsp;  public AddressDTO(String street, String zip, String city) {</b>
<b class="nc">&nbsp;    this.street = street;</b>
<b class="nc">&nbsp;    this.zip = zip;</b>
<b class="nc">&nbsp;    this.city = city;</b>
&nbsp;  }
&nbsp;
&nbsp;  public AddressDTO street(String street) {
<b class="nc">&nbsp;    this.street = street;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Street name and number
&nbsp;   * @return street
&nbsp;   */
&nbsp;  @NotNull @Size(max = 30) 
&nbsp;  @Schema(name = &quot;street&quot;, description = &quot;Street name and number&quot;, requiredMode = Schema.RequiredMode.REQUIRED)
&nbsp;  @JsonProperty(&quot;street&quot;)
&nbsp;  public String getStreet() {
<b class="fc">&nbsp;    return street;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setStreet(String street) {
<b class="fc">&nbsp;    this.street = street;</b>
&nbsp;  }
&nbsp;
&nbsp;  public AddressDTO number(String number) {
<b class="nc">&nbsp;    this.number = number;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * House number
&nbsp;   * @return number
&nbsp;   */
&nbsp;  @Size(max = 10) 
&nbsp;  @Schema(name = &quot;number&quot;, description = &quot;House number&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;number&quot;)
&nbsp;  public String getNumber() {
<b class="fc">&nbsp;    return number;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setNumber(String number) {
<b class="fc">&nbsp;    this.number = number;</b>
&nbsp;  }
&nbsp;
&nbsp;  public AddressDTO box(String box) {
<b class="nc">&nbsp;    this.box = box;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Postal box
&nbsp;   * @return box
&nbsp;   */
&nbsp;  @Size(max = 10) 
&nbsp;  @Schema(name = &quot;box&quot;, description = &quot;Postal box&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;box&quot;)
&nbsp;  public String getBox() {
<b class="fc">&nbsp;    return box;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setBox(String box) {
<b class="fc">&nbsp;    this.box = box;</b>
&nbsp;  }
&nbsp;
&nbsp;  public AddressDTO zip(String zip) {
<b class="nc">&nbsp;    this.zip = zip;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Postal code (4 digits for Belgian addresses, up to 10 characters for foreign addresses)
&nbsp;   * @return zip
&nbsp;   */
&nbsp;  @NotNull @Size(max = 10) 
&nbsp;  @Schema(name = &quot;zip&quot;, example = &quot;1000&quot;, description = &quot;Postal code (4 digits for Belgian addresses, up to 10 characters for foreign addresses)&quot;, requiredMode = Schema.RequiredMode.REQUIRED)
&nbsp;  @JsonProperty(&quot;zip&quot;)
&nbsp;  public String getZip() {
<b class="fc">&nbsp;    return zip;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setZip(String zip) {
<b class="fc">&nbsp;    this.zip = zip;</b>
&nbsp;  }
&nbsp;
&nbsp;  public AddressDTO city(String city) {
<b class="nc">&nbsp;    this.city = city;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * City name
&nbsp;   * @return city
&nbsp;   */
&nbsp;  @NotNull @Size(max = 35) 
&nbsp;  @Schema(name = &quot;city&quot;, description = &quot;City name&quot;, requiredMode = Schema.RequiredMode.REQUIRED)
&nbsp;  @JsonProperty(&quot;city&quot;)
&nbsp;  public String getCity() {
<b class="fc">&nbsp;    return city;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setCity(String city) {
<b class="fc">&nbsp;    this.city = city;</b>
&nbsp;  }
&nbsp;
&nbsp;  public AddressDTO countryCode(Integer countryCode) {
<b class="nc">&nbsp;    this.countryCode = countryCode;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Country code (1 for Belgium, see country lookup service for others)
&nbsp;   * minimum: 1
&nbsp;   * maximum: 999
&nbsp;   * @return countryCode
&nbsp;   */
&nbsp;  @Min(1) @Max(999) 
&nbsp;  @Schema(name = &quot;countryCode&quot;, example = &quot;1&quot;, description = &quot;Country code (1 for Belgium, see country lookup service for others)&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;countryCode&quot;)
&nbsp;  public Integer getCountryCode() {
<b class="fc">&nbsp;    return countryCode;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setCountryCode(Integer countryCode) {
<b class="fc">&nbsp;    this.countryCode = countryCode;</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public boolean equals(Object o) {
<b class="nc">&nbsp;    if (this == o) {</b>
<b class="nc">&nbsp;      return true;</b>
&nbsp;    }
<b class="nc">&nbsp;    if (o == null || getClass() != o.getClass()) {</b>
<b class="nc">&nbsp;      return false;</b>
&nbsp;    }
<b class="nc">&nbsp;    AddressDTO address = (AddressDTO) o;</b>
<b class="nc">&nbsp;    return Objects.equals(this.street, address.street) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.number, address.number) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.box, address.box) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.zip, address.zip) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.city, address.city) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.countryCode, address.countryCode);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public int hashCode() {
<b class="nc">&nbsp;    return Objects.hash(street, number, box, zip, city, countryCode);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public String toString() {
<b class="nc">&nbsp;    StringBuilder sb = new StringBuilder();</b>
<b class="nc">&nbsp;    sb.append(&quot;class AddressDTO {\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    street: &quot;).append(toIndentedString(street)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    number: &quot;).append(toIndentedString(number)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    box: &quot;).append(toIndentedString(box)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    zip: &quot;).append(toIndentedString(zip)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    city: &quot;).append(toIndentedString(city)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    countryCode: &quot;).append(toIndentedString(countryCode)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;}&quot;);</b>
<b class="nc">&nbsp;    return sb.toString();</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Convert the given object to string with each line indented by 4 spaces
&nbsp;   * (except the first line).
&nbsp;   */
&nbsp;  private String toIndentedString(Object o) {
<b class="nc">&nbsp;    if (o == null) {</b>
<b class="nc">&nbsp;      return &quot;null&quot;;</b>
&nbsp;    }
<b class="nc">&nbsp;    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</b>
&nbsp;  }
&nbsp;}
&nbsp;
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
