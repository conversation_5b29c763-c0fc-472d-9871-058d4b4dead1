


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > AddressUtils</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.citizeninfo.mapper</a>
</div>

<h1>Coverage Summary for Class: AddressUtils (be.fgov.onerva.person.backend.citizeninfo.mapper)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">AddressUtils</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (4/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    92.5%
  </span>
  <span class="absValue">
    (37/40)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    94.9%
  </span>
  <span class="absValue">
    (37/39)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.citizeninfo.mapper;
&nbsp;
&nbsp;import lombok.experimental.UtilityClass;
&nbsp;
&nbsp;import java.util.List;
&nbsp;import java.util.regex.Matcher;
&nbsp;import java.util.regex.Pattern;
&nbsp;
&nbsp;@UtilityClass
&nbsp;public final class AddressUtils {
&nbsp;
<b class="fc">&nbsp;    private static final Pattern NBR_BOX = Pattern.compile(&quot;^(\\d+\\s*[A-Za-z]?)\\s*(/|\\*|x|bte|bus|bt|b)\\s*(\\S+)$&quot;, Pattern.CASE_INSENSITIVE);</b>
&nbsp;
<b class="fc">&nbsp;    private static final List&lt;String&gt; BOX_SEPARATORS = List.of(&quot;bte&quot;, &quot;bus&quot;, &quot;bt&quot;, &quot;b&quot;);</b>
<b class="fc">&nbsp;    private static final List&lt;String&gt; SPECIALS = List.of(&quot;app&quot;, &quot;ap&quot;);</b>
&nbsp;
&nbsp;
&nbsp;    public static String[] splitStreet(String line) {
<b class="fc">&nbsp;        int start = indexOfFirstChar(line, 0, false);</b>
<b class="fc">&nbsp;        int index = indexOfFirstChar(line, start, true);</b>
<b class="fc">&nbsp;        if (index &lt;= 0) {</b>
<b class="fc">&nbsp;            return new String[]{line, null};</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        String street = line.substring(0, index - 1).trim();</b>
<b class="fc">&nbsp;        int endIndex = street.charAt(street.length() - 1) == &#39;,&#39; ? street.length() - 1 : street.length();</b>
<b class="fc">&nbsp;        return new String[]{street.substring(0, endIndex), line.substring(index).trim()};</b>
&nbsp;    }
&nbsp;
&nbsp;    public static String[] splitHouseNumberAndBox(String line) {
<b class="fc">&nbsp;        if (line == null) {</b>
<b class="fc">&nbsp;            return new String[]{null, null};</b>
&nbsp;        }
<b class="fc">&nbsp;        var trimmed = line.trim();</b>
<b class="fc">&nbsp;        boolean hasSpecials = SPECIALS.stream().anyMatch(s -&gt; trimmed.toLowerCase().contains(s));</b>
&nbsp;
<b class="fc">&nbsp;        Matcher matcher = NBR_BOX.matcher(trimmed);</b>
<b class="fc">&nbsp;        if (!matcher.matches() || hasSpecials) {</b>
<b class="fc">&nbsp;            return new String[]{trimmed, null};</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        String separator = matcher.group(2);</b>
<b class="fc">&nbsp;        String box = matcher.group(3);</b>
&nbsp;
<b class="pc">&nbsp;        if (separator == null ||  box == null) {</b>
<b class="nc">&nbsp;            return new String[]{trimmed, null};</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        String nbr = matcher.group(1).trim();</b>
<b class="fc">&nbsp;        separator = separator.trim();</b>
<b class="fc">&nbsp;        box = box.trim();</b>
&nbsp;
<b class="fc">&nbsp;        if (&quot;b&quot;.equalsIgnoreCase(separator) &amp;&amp; !Character.isDigit(box.charAt(0))) {</b>
<b class="fc">&nbsp;            return new String[]{trimmed, null};</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        if (&quot;/&quot;.equals(separator) &amp;&amp; box.length() &gt; 1) {</b>
<b class="fc">&nbsp;            for(String sep: BOX_SEPARATORS) {</b>
<b class="fc">&nbsp;                int index = box.toLowerCase().indexOf(sep);</b>
<b class="fc">&nbsp;                if (index != -1) {</b>
<b class="fc">&nbsp;                    return new String[]{nbr, box.substring(index + sep.length()).trim()};</b>
&nbsp;                }
&nbsp;            }
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        return new String[]{nbr, box};</b>
&nbsp;    }
&nbsp;
&nbsp;    static int indexOfFirstChar(String line, int index, boolean digit) {
<b class="fc">&nbsp;        if (line == null || index &lt; 0 || index &gt;= line.length()) {</b>
<b class="fc">&nbsp;            return -1;</b>
&nbsp;        }
<b class="pc">&nbsp;        for (int i = index; i &lt; line.length(); i++) {</b>
<b class="fc">&nbsp;            boolean isDigit = Character.isDigit(line.charAt(i));</b>
<b class="fc">&nbsp;            if (digit? isDigit : !isDigit) {</b>
<b class="fc">&nbsp;                return i;</b>
&nbsp;            }
&nbsp;        }
<b class="nc">&nbsp;        return -1;</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
