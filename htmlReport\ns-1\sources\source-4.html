


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenInfoV2Api</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">backend.api</a>
</div>

<h1>Coverage Summary for Class: CitizenInfoV2Api (backend.api)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">CitizenInfoV2Api</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/3)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/7)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;/**
&nbsp; * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.12.0).
&nbsp; * https://openapi-generator.tech
&nbsp; * Do not edit the class manually.
&nbsp; */
&nbsp;package backend.api;
&nbsp;
&nbsp;import backend.rest.model.CitizenInfoPageV2DTO;
&nbsp;import io.swagger.v3.oas.annotations.ExternalDocumentation;
&nbsp;import io.swagger.v3.oas.annotations.Operation;
&nbsp;import io.swagger.v3.oas.annotations.Parameter;
&nbsp;import io.swagger.v3.oas.annotations.Parameters;
&nbsp;import io.swagger.v3.oas.annotations.media.ArraySchema;
&nbsp;import io.swagger.v3.oas.annotations.media.Content;
&nbsp;import io.swagger.v3.oas.annotations.media.Schema;
&nbsp;import io.swagger.v3.oas.annotations.responses.ApiResponse;
&nbsp;import io.swagger.v3.oas.annotations.security.SecurityRequirement;
&nbsp;import io.swagger.v3.oas.annotations.tags.Tag;
&nbsp;import io.swagger.v3.oas.annotations.enums.ParameterIn;
&nbsp;import org.springframework.http.HttpStatus;
&nbsp;import org.springframework.http.MediaType;
&nbsp;import org.springframework.http.ResponseEntity;
&nbsp;import org.springframework.validation.annotation.Validated;
&nbsp;import org.springframework.web.bind.annotation.*;
&nbsp;import org.springframework.web.context.request.NativeWebRequest;
&nbsp;import org.springframework.web.multipart.MultipartFile;
&nbsp;
&nbsp;import jakarta.validation.Valid;
&nbsp;import jakarta.validation.constraints.*;
&nbsp;import java.util.List;
&nbsp;import java.util.Map;
&nbsp;import java.util.Optional;
&nbsp;import jakarta.annotation.Generated;
&nbsp;
&nbsp;@Generated(value = &quot;org.openapitools.codegen.languages.SpringCodegen&quot;, date = &quot;2025-07-16T14:11:30.627767400+02:00[Europe/Brussels]&quot;, comments = &quot;Generator version: 7.12.0&quot;)
&nbsp;@Validated
&nbsp;@Tag(name = &quot;CitizenInfoV2&quot;, description = &quot;the CitizenInfoV2 API&quot;)
&nbsp;public interface CitizenInfoV2Api {
&nbsp;
&nbsp;    default Optional&lt;NativeWebRequest&gt; getRequest() {
<b class="nc">&nbsp;        return Optional.empty();</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * GET /v2/citizen/info
&nbsp;     * Use the searchCitizenInfo.
&nbsp;     *
&nbsp;     * @param ssins Get citizen info using a list of ssins (required)
&nbsp;     * @param dataReturned  (optional, default to SUMMARY)
&nbsp;     * @param pageNumber  (optional, default to 0)
&nbsp;     * @param pageSize  (optional, default to 10)
&nbsp;     * @return CitizenInfo (status code 200)
&nbsp;     * @deprecated
&nbsp;     */
&nbsp;    @Deprecated
&nbsp;    @Operation(
&nbsp;        operationId = &quot;searchCitizenInfov2&quot;,
&nbsp;        description = &quot;Use the searchCitizenInfo.&quot;,
&nbsp;        deprecated = true,
&nbsp;        tags = { &quot;CitizenInfoV2&quot; },
&nbsp;        responses = {
&nbsp;            @ApiResponse(responseCode = &quot;200&quot;, description = &quot;CitizenInfo&quot;, content = {
&nbsp;                @Content(mediaType = &quot;application/json&quot;, schema = @Schema(implementation = CitizenInfoPageV2DTO.class))
&nbsp;            })
&nbsp;        }
&nbsp;    )
&nbsp;    @RequestMapping(
&nbsp;        method = RequestMethod.GET,
&nbsp;        value = &quot;/v2/citizen/info&quot;,
&nbsp;        produces = { &quot;application/json&quot; }
&nbsp;    )
&nbsp;    
&nbsp;    default ResponseEntity&lt;CitizenInfoPageV2DTO&gt; searchCitizenInfov2(
&nbsp;        @NotNull @Parameter(name = &quot;ssins&quot;, description = &quot;Get citizen info using a list of ssins&quot;, required = true, in = ParameterIn.QUERY) @Valid @RequestParam(value = &quot;ssins&quot;, required = true) List&lt;String&gt; ssins,
&nbsp;        @Parameter(name = &quot;dataReturned&quot;, description = &quot;&quot;, in = ParameterIn.QUERY) @Valid @RequestParam(value = &quot;dataReturned&quot;, required = false, defaultValue = &quot;SUMMARY&quot;) String dataReturned,
&nbsp;        @Min(0) @Parameter(name = &quot;pageNumber&quot;, description = &quot;&quot;, in = ParameterIn.QUERY) @Valid @RequestParam(value = &quot;pageNumber&quot;, required = false, defaultValue = &quot;0&quot;) Integer pageNumber,
&nbsp;        @Parameter(name = &quot;pageSize&quot;, description = &quot;&quot;, in = ParameterIn.QUERY) @Valid @RequestParam(value = &quot;pageSize&quot;, required = false, defaultValue = &quot;10&quot;) Integer pageSize
&nbsp;    ) {
<b class="nc">&nbsp;        getRequest().ifPresent(request -&gt; {</b>
<b class="nc">&nbsp;            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader(&quot;Accept&quot;))) {</b>
<b class="nc">&nbsp;                if (mediaType.isCompatibleWith(MediaType.valueOf(&quot;application/json&quot;))) {</b>
<b class="nc">&nbsp;                    String exampleString = &quot;{ \&quot;isFirst\&quot; : true, \&quot;pageNumber\&quot; : 0, \&quot;isLast\&quot; : true, \&quot;totalPage\&quot; : 1, \&quot;pageSize\&quot; : 6, \&quot;content\&quot; : [ { \&quot;lastName\&quot; : \&quot;lastName\&quot;, \&quot;nationBcss\&quot; : 5.637376656633329, \&quot;postalCode\&quot; : \&quot;postalCode\&quot;, \&quot;language\&quot; : \&quot;language\&quot;, \&quot;bisNumber\&quot; : [ \&quot;bisNumber\&quot;, \&quot;bisNumber\&quot; ], \&quot;rvaCountryCode\&quot; : 9, \&quot;telephoneReg\&quot; : \&quot;telephoneReg\&quot;, \&quot;dateMcpte\&quot; : \&quot;2000-01-23\&quot;, \&quot;gsmReg\&quot; : \&quot;gsmReg\&quot;, \&quot;numPens\&quot; : 7.061401241503109, \&quot;id\&quot; : 2.3021358869347655, \&quot;email\&quot; : \&quot;email\&quot;, \&quot;OP\&quot; : 2.027123023002322, \&quot;address\&quot; : \&quot;address\&quot;, \&quot;unemploymentOffice\&quot; : 4.145608029883936, \&quot;communeDateValid\&quot; : \&quot;2000-01-23\&quot;, \&quot;sex\&quot; : \&quot;sex\&quot;, \&quot;gsmOnem\&quot; : \&quot;gsmOnem\&quot;, \&quot;flagNation\&quot; : 7.386281948385884, \&quot;firstName\&quot; : \&quot;firstName\&quot;, \&quot;ssin\&quot; : \&quot;ssin\&quot;, \&quot;flagVCpte\&quot; : 1.2315135367772556, \&quot;iban\&quot; : \&quot;iban\&quot;, \&quot;telephoneOnem\&quot; : \&quot;telephoneOnem\&quot;, \&quot;nationDateValid\&quot; : \&quot;2000-01-23\&quot;, \&quot;numBox\&quot; : 3.616076749251911, \&quot;deceasedDate\&quot; : \&quot;2000-01-23\&quot;, \&quot;flagPurge\&quot; : \&quot;flagPurge\&quot; }, { \&quot;lastName\&quot; : \&quot;lastName\&quot;, \&quot;nationBcss\&quot; : 5.637376656633329, \&quot;postalCode\&quot; : \&quot;postalCode\&quot;, \&quot;language\&quot; : \&quot;language\&quot;, \&quot;bisNumber\&quot; : [ \&quot;bisNumber\&quot;, \&quot;bisNumber\&quot; ], \&quot;rvaCountryCode\&quot; : 9, \&quot;telephoneReg\&quot; : \&quot;telephoneReg\&quot;, \&quot;dateMcpte\&quot; : \&quot;2000-01-23\&quot;, \&quot;gsmReg\&quot; : \&quot;gsmReg\&quot;, \&quot;numPens\&quot; : 7.061401241503109, \&quot;id\&quot; : 2.3021358869347655, \&quot;email\&quot; : \&quot;email\&quot;, \&quot;OP\&quot; : 2.027123023002322, \&quot;address\&quot; : \&quot;address\&quot;, \&quot;unemploymentOffice\&quot; : 4.145608029883936, \&quot;communeDateValid\&quot; : \&quot;2000-01-23\&quot;, \&quot;sex\&quot; : \&quot;sex\&quot;, \&quot;gsmOnem\&quot; : \&quot;gsmOnem\&quot;, \&quot;flagNation\&quot; : 7.386281948385884, \&quot;firstName\&quot; : \&quot;firstName\&quot;, \&quot;ssin\&quot; : \&quot;ssin\&quot;, \&quot;flagVCpte\&quot; : 1.2315135367772556, \&quot;iban\&quot; : \&quot;iban\&quot;, \&quot;telephoneOnem\&quot; : \&quot;telephoneOnem\&quot;, \&quot;nationDateValid\&quot; : \&quot;2000-01-23\&quot;, \&quot;numBox\&quot; : 3.616076749251911, \&quot;deceasedDate\&quot; : \&quot;2000-01-23\&quot;, \&quot;flagPurge\&quot; : \&quot;flagPurge\&quot; } ], \&quot;totalElements\&quot; : 5 }&quot;;</b>
<b class="nc">&nbsp;                    ApiUtil.setExampleResponse(request, &quot;application/json&quot;, exampleString);</b>
&nbsp;                    break;
&nbsp;                }
&nbsp;            }
&nbsp;        });
<b class="nc">&nbsp;        return new ResponseEntity&lt;&gt;(HttpStatus.NOT_IMPLEMENTED);</b>
&nbsp;
&nbsp;    }
&nbsp;
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
