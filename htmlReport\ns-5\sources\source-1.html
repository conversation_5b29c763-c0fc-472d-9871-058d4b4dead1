


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenMapper</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.citizen.mapper</a>
</div>

<h1>Coverage Summary for Class: CitizenMapper (be.fgov.onerva.person.backend.citizen.mapper)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">CitizenMapper</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (10/10)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    63.2%
  </span>
  <span class="absValue">
    (12/19)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    86%
  </span>
  <span class="absValue">
    (37/43)
  </span>
</td>
</tr>
  <tr>
    <td class="name">CitizenMapper$1</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (11/11)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    63.2%
  </span>
  <span class="absValue">
    (12/19)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    86.4%
  </span>
  <span class="absValue">
    (38/44)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.citizen.mapper;
&nbsp;
&nbsp;import backend.rest.model.CitizenCreationRequestDTO;
&nbsp;import backend.rest.model.CitizenDTO;
&nbsp;import backend.rest.model.CitizenPageDTO;
&nbsp;import backend.rest.model.CitizenUpdateRequestDTO;
&nbsp;import be.fgov.onerva.common.utils.PensionNumberUtils;
&nbsp;import be.fgov.onerva.person.backend.citizen.model.AddressUpdateRequest;
&nbsp;import be.fgov.onerva.person.backend.citizen.model.CitizenCreationRequest;
&nbsp;import be.fgov.onerva.person.backend.citizen.model.CitizenEntity;
&nbsp;import be.fgov.onerva.person.backend.citizen.model.CitizenUpdateRequest;
&nbsp;import be.fgov.onerva.person.backend.request.model.Address;
&nbsp;import be.fgov.onerva.person.backend.request.model.BankUpdateInfo;
&nbsp;import be.fgov.onerva.person.backend.request.model.PaymentType;
&nbsp;import be.fgov.onerva.person.backend.citizen.model.UnionDueUpdateInfo;
&nbsp;import org.apache.commons.lang3.StringUtils;
&nbsp;import org.mapstruct.Mapper;
&nbsp;import org.mapstruct.Mapping;
&nbsp;import org.springframework.data.domain.Page;
&nbsp;
&nbsp;import java.time.LocalDate;
&nbsp;
&nbsp;@Mapper
&nbsp;public interface CitizenMapper {
&nbsp;
&nbsp;    @Mapping(target = &quot;pensionNumber&quot;, source = &quot;entity.id&quot;)
&nbsp;    @Mapping(target = &quot;zipCode&quot;, source = &quot;entity.zipCode&quot;)
&nbsp;    @Mapping(target = &quot;numbox&quot;, source = &quot;entity.numBox&quot;)
&nbsp;    @Mapping(target = &quot;niss&quot;, expression = &quot;java(niss(entity))&quot;)
&nbsp;    @Mapping(target = &quot;lastname&quot;, expression = &quot;java(lastName(entity))&quot;)
&nbsp;    @Mapping(target = &quot;firstname&quot;, expression = &quot;java(firstName(entity))&quot;)
&nbsp;    @Mapping(target = &quot;agent&quot;, source = &quot;entity.flagPersonnel&quot;)
&nbsp;    CitizenDTO map(CitizenEntity entity);
&nbsp;
&nbsp;    @Mapping(source = &quot;number&quot;, target = &quot;pageNumber&quot;)
&nbsp;    @Mapping(source = &quot;size&quot;, target = &quot;pageSize&quot;)
&nbsp;    @Mapping(source = &quot;totalPages&quot;, target = &quot;totalPage&quot;)
&nbsp;    @Mapping(source = &quot;totalElements&quot;, target = &quot;totalElements&quot;)
&nbsp;    @Mapping(source = &quot;first&quot;, target = &quot;isFirst&quot;)
&nbsp;    @Mapping(source = &quot;last&quot;, target = &quot;isLast&quot;)
&nbsp;    CitizenPageDTO mapPageToDto(Page&lt;CitizenEntity&gt; source);
&nbsp;
&nbsp;    @Mapping(source = &quot;businessDomain&quot;, target = &quot;domain&quot;)
&nbsp;    CitizenCreationRequest map(CitizenCreationRequestDTO source, String businessDomain, boolean allowance);
&nbsp;
&nbsp;    @Mapping(target = &quot;address&quot;, expression = &quot;java(mapAddress(source.getAddress()))&quot;)
&nbsp;    @Mapping(target = &quot;bankInfo&quot;, expression = &quot;java(mapBankInfo(source))&quot;)
&nbsp;    @Mapping(target = &quot;unionDueInfo&quot;, expression = &quot;java(mapUnionDueInfo(source))&quot;)
&nbsp;    @Mapping(target = &quot;birthDate&quot;, expression = &quot;java(mapBirthDate(source))&quot;)
&nbsp;    @Mapping(target = &quot;languageCode&quot;, expression = &quot;java(mapLanguageCode(source))&quot;)
&nbsp;    @Mapping(target = &quot;unemploymentOffice&quot;, expression = &quot;java(mapUnemploymentOffice(source))&quot;)
&nbsp;    CitizenUpdateRequest map(CitizenUpdateRequestDTO source, String niss, String username);
&nbsp;
&nbsp;    default AddressUpdateRequest mapAddress(backend.rest.model.ForeignAddressDTO addressDTO) {
<b class="pc">&nbsp;        if (addressDTO == null) {</b>
<b class="nc">&nbsp;            return null;</b>
&nbsp;        }
<b class="fc">&nbsp;        return AddressUpdateRequest.builder()</b>
<b class="fc">&nbsp;                .street(addressDTO.getStreet())</b>
<b class="fc">&nbsp;                .number(addressDTO.getNumber())</b>
<b class="fc">&nbsp;                .box(addressDTO.getBox())</b>
<b class="pc">&nbsp;                .zip(addressDTO.getZip() != null ? addressDTO.getZip().toString() : null)</b>
<b class="fc">&nbsp;                .city(addressDTO.getCity())</b>
<b class="fc">&nbsp;                .countryCode(addressDTO.getCountryCode())</b>
<b class="fc">&nbsp;                .build();</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Maps bank information from DTO to internal model.
&nbsp;     * Handles both new nested structure and backward compatibility with deprecated
&nbsp;     * fields.
&nbsp;     */
&nbsp;    default BankUpdateInfo mapBankInfo(CitizenUpdateRequestDTO source) {
<b class="pc">&nbsp;        if (source.getBankInfo() != null) {</b>
<b class="fc">&nbsp;            return BankUpdateInfo.builder()</b>
<b class="fc">&nbsp;                    .paymentType(mapPaymentType(source.getBankInfo().getPaymentType()))</b>
<b class="fc">&nbsp;                    .validFrom(source.getBankInfo().getValidFrom())</b>
<b class="fc">&nbsp;                    .iban(source.getBankInfo().getIban())</b>
<b class="fc">&nbsp;                    .bic(source.getBankInfo().getBic())</b>
<b class="fc">&nbsp;                    .accountHolder(source.getBankInfo().getAccountHolder())</b>
<b class="fc">&nbsp;                    .build();</b>
&nbsp;        }
<b class="nc">&nbsp;        return null;</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Maps union due information from DTO to internal model.
&nbsp;     * Handles both new nested structure and backward compatibility with deprecated
&nbsp;     * fields.
&nbsp;     */
&nbsp;    default UnionDueUpdateInfo mapUnionDueInfo(CitizenUpdateRequestDTO source) {
&nbsp;        // For now, handle backward compatibility with deprecated unionDue field
&nbsp;        // When API is updated with nested unionDueInfo, this will be updated
&nbsp;        // accordingly
<b class="pc">&nbsp;        if (source.getUnionDueInfo() != null) {</b>
<b class="fc">&nbsp;            return UnionDueUpdateInfo.builder()</b>
<b class="fc">&nbsp;                    .unionDue(source.getUnionDueInfo().getUnionDue())</b>
<b class="fc">&nbsp;                    .validFrom(source.getUnionDueInfo().getValidFrom())</b>
<b class="fc">&nbsp;                    .build();</b>
&nbsp;        }
<b class="nc">&nbsp;        return null;</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Maps birth date from DTO. Will be updated when API includes birthDate field.
&nbsp;     */
&nbsp;    default LocalDate mapBirthDate(CitizenUpdateRequestDTO source) {
<b class="fc">&nbsp;        return source.getBirthDate();</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Maps language code from DTO. Will be updated when API includes languageCode
&nbsp;     * field.
&nbsp;     */
&nbsp;    default Integer mapLanguageCode(CitizenUpdateRequestDTO source) {
<b class="fc">&nbsp;        return source.getLanguageCode();</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Maps unemployment office from DTO. Will be updated when API includes
&nbsp;     * unemploymentOffice field.
&nbsp;     */
&nbsp;    default Integer mapUnemploymentOffice(CitizenUpdateRequestDTO source) {
<b class="fc">&nbsp;        return source.getUnemploymentOffice();</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Maps PaymentTypeDTO to internal PaymentType enum
&nbsp;     */
&nbsp;    default PaymentType mapPaymentType(backend.rest.model.PaymentTypeDTO paymentTypeDTO) {
<b class="pc">&nbsp;        if (paymentTypeDTO == null) {</b>
<b class="nc">&nbsp;            return null;</b>
&nbsp;        }
<b class="pc">&nbsp;        return switch (paymentTypeDTO) {</b>
<b class="nc">&nbsp;            case BANK_TRANSFER -&gt; PaymentType.BANK_TRANSFER;</b>
<b class="fc">&nbsp;            case CIRCULAR_CHEQUE -&gt; PaymentType.CIRCULAR_CHEQUE;</b>
<b class="nc">&nbsp;            case OTHER_BANK_TRANSFER -&gt; PaymentType.OTHER_BANK_TRANSFER;</b>
&nbsp;        };
&nbsp;    }
&nbsp;
&nbsp;    default String firstName(CitizenEntity entity) {
<b class="fc">&nbsp;        if (entity == null) {</b>
<b class="fc">&nbsp;            return null;</b>
&nbsp;        }
<b class="fc">&nbsp;        return entity.getFullName().split(&quot;,&quot;)[1].trim();</b>
&nbsp;    }
&nbsp;
&nbsp;    default String lastName(CitizenEntity entity) {
<b class="fc">&nbsp;        if (entity == null) {</b>
<b class="fc">&nbsp;            return null;</b>
&nbsp;        }
<b class="fc">&nbsp;        return entity.getFullName().split(&quot;,&quot;)[0].trim();</b>
&nbsp;    }
&nbsp;
&nbsp;    default String niss(CitizenEntity entity) {
<b class="fc">&nbsp;        if (entity == null) {</b>
<b class="fc">&nbsp;            return null;</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        return StringUtils.leftPad(PensionNumberUtils.convertToInssWithDefault(entity.getId()).toString(), 11, &#39;0&#39;);</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
