


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > FeatureFlagsServiceImpl</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.featureflags</a>
</div>

<h1>Coverage Summary for Class: FeatureFlagsServiceImpl (be.fgov.onerva.person.backend.featureflags)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">FeatureFlagsServiceImpl</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (3/3)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (13/13)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.featureflags;
&nbsp;
&nbsp;import com.flagsmith.FlagsmithClient;
&nbsp;import com.flagsmith.exceptions.FlagsmithClientError;
&nbsp;import com.flagsmith.models.BaseFlag;
&nbsp;import lombok.RequiredArgsConstructor;
&nbsp;import lombok.extern.slf4j.Slf4j;
&nbsp;import org.springframework.stereotype.Service;
&nbsp;
&nbsp;@Service
&nbsp;@RequiredArgsConstructor
<b class="fc">&nbsp;@Slf4j</b>
&nbsp;public class FeatureFlagsServiceImpl implements FeatureFlagsService {
&nbsp;
&nbsp;    private final FlagsmithClient flagsmithClient;
&nbsp;
&nbsp;    @Override
&nbsp;    public boolean isFeatureEnabled(String featureFlag) {
<b class="fc">&nbsp;        log.debug(String.format(&quot;Check if feature %s is enabled&quot;, featureFlag));</b>
&nbsp;        try {
<b class="fc">&nbsp;            var isEnabled = flagsmithClient.getEnvironmentFlags().isFeatureEnabled(featureFlag);</b>
<b class="fc">&nbsp;            log.debug(String.format(&quot;Feature %s is enabled ? %s&quot;, featureFlag, isEnabled));</b>
<b class="fc">&nbsp;            return isEnabled;</b>
&nbsp;        } catch (FlagsmithClientError e) {
<b class="fc">&nbsp;            log.error(&quot;Error while getting the feature flag &#39;{}&#39;. It will default to false.&quot;, featureFlag,  e);</b>
<b class="fc">&nbsp;            return false;</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    @Override
&nbsp;    public BaseFlag getFlag(String feature) {
<b class="fc">&nbsp;        log.debug(String.format(&quot;Get the flag for feature %s&quot;, feature));</b>
&nbsp;        try {
<b class="fc">&nbsp;            var flag = flagsmithClient.getEnvironmentFlags().getFlag(feature);</b>
<b class="fc">&nbsp;            log.debug(String.format(&quot;Feature %s has flag %s&quot;, feature, flag));</b>
<b class="fc">&nbsp;            return flag;</b>
&nbsp;        } catch (FlagsmithClientError e) {
<b class="fc">&nbsp;            log.error(e.getMessage(), e);</b>
<b class="fc">&nbsp;            return null;</b>
&nbsp;        }
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
