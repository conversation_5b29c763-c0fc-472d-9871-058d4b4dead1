


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > RFC3339DateFormat</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.wave</a>
</div>

<h1>Coverage Summary for Class: RFC3339DateFormat (be.fgov.onerva.wave)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">RFC3339DateFormat</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/6)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/11)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;/*
&nbsp; * WO facade API
&nbsp; * API to manage tasks (and processes) in a simplified manner
&nbsp; *
&nbsp; * The version of the OpenAPI document: v1
&nbsp; * 
&nbsp; *
&nbsp; * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
&nbsp; * https://openapi-generator.tech
&nbsp; * Do not edit the class manually.
&nbsp; */
&nbsp;
&nbsp;package be.fgov.onerva.wave;
&nbsp;
&nbsp;import com.fasterxml.jackson.databind.util.StdDateFormat;
&nbsp;
&nbsp;import java.text.DateFormat;
&nbsp;import java.text.FieldPosition;
&nbsp;import java.text.ParsePosition;
&nbsp;import java.util.Date;
&nbsp;import java.text.DecimalFormat;
&nbsp;import java.util.GregorianCalendar;
&nbsp;import java.util.TimeZone;
&nbsp;
&nbsp;@jakarta.annotation.Generated(value = &quot;org.openapitools.codegen.languages.JavaClientCodegen&quot;, date = &quot;2025-07-16T14:11:32.899250600+02:00[Europe/Brussels]&quot;, comments = &quot;Generator version: 7.12.0&quot;)
&nbsp;public class RFC3339DateFormat extends DateFormat {
&nbsp;  private static final long serialVersionUID = 1L;
<b class="nc">&nbsp;  private static final TimeZone TIMEZONE_Z = TimeZone.getTimeZone(&quot;UTC&quot;);</b>
&nbsp;
<b class="nc">&nbsp;  private final StdDateFormat fmt = new StdDateFormat()</b>
<b class="nc">&nbsp;          .withTimeZone(TIMEZONE_Z)</b>
<b class="nc">&nbsp;          .withColonInTimeZone(true);</b>
&nbsp;
<b class="nc">&nbsp;  public RFC3339DateFormat() {</b>
<b class="nc">&nbsp;    this.calendar = new GregorianCalendar();</b>
<b class="nc">&nbsp;    this.numberFormat = new DecimalFormat();</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public Date parse(String source) {
<b class="nc">&nbsp;    return parse(source, new ParsePosition(0));</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public Date parse(String source, ParsePosition pos) {
<b class="nc">&nbsp;    return fmt.parse(source, pos);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public StringBuffer format(Date date, StringBuffer toAppendTo, FieldPosition fieldPosition) {
<b class="nc">&nbsp;    return fmt.format(date, toAppendTo, fieldPosition);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public Object clone() {
<b class="nc">&nbsp;    return super.clone();</b>
&nbsp;  }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
