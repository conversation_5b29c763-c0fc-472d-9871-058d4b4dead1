


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > DatabaseConfig</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.config</a>
</div>

<h1>Coverage Summary for Class: DatabaseConfig (be.fgov.onerva.person.backend.config)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">DatabaseConfig</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
</tr>
  <tr>
    <td class="name">DatabaseConfig$Mfx</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (5/5)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (9/9)
  </span>
</td>
  </tr>
  <tr>
    <td class="name">DatabaseConfig$Person</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (5/5)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (8/8)
  </span>
</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (11/11)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (18/18)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.config;
&nbsp;
&nbsp;import be.fgov.onerva.person.backend.citizen.model.CitizenEntity;
&nbsp;import be.fgov.onerva.person.backend.request.model.PersonRequest;
&nbsp;import be.fgov.onerva.person.backend.citizeninfo.model.CitizenInfoEntity;
&nbsp;import com.zaxxer.hikari.HikariDataSource;
&nbsp;import org.springframework.beans.factory.annotation.Qualifier;
&nbsp;import org.springframework.boot.autoconfigure.flyway.FlywayDataSource;
&nbsp;import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
&nbsp;import org.springframework.boot.context.properties.ConfigurationProperties;
&nbsp;import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
&nbsp;import org.springframework.context.annotation.Bean;
&nbsp;import org.springframework.context.annotation.Configuration;
&nbsp;import org.springframework.context.annotation.Primary;
&nbsp;import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
&nbsp;import org.springframework.orm.jpa.JpaTransactionManager;
&nbsp;import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
&nbsp;import org.springframework.transaction.PlatformTransactionManager;
&nbsp;
&nbsp;import java.util.Map;
&nbsp;
&nbsp;@Configuration(proxyBeanMethods = false)
<b class="fc">&nbsp;public class DatabaseConfig {</b>
&nbsp;
&nbsp;    @EnableJpaRepositories(
&nbsp;            basePackages = {&quot;be.fgov.onerva.person.backend.citizen&quot;, &quot;be.fgov.onerva.person.backend.citizeninfo&quot;},
&nbsp;            entityManagerFactoryRef = &quot;mfxEmf&quot;,
&nbsp;            transactionManagerRef= &quot;mfxTransactionManager&quot;
&nbsp;    )
&nbsp;    @Configuration(proxyBeanMethods = false)
<b class="fc">&nbsp;    static class Mfx {</b>
&nbsp;
&nbsp;        @Bean
&nbsp;        @Primary
&nbsp;        @ConfigurationProperties(&quot;datasource.mfx&quot;)
&nbsp;        public DataSourceProperties mfxDataSourceProperties() {
<b class="fc">&nbsp;            return new DataSourceProperties();</b>
&nbsp;        }
&nbsp;
&nbsp;        @Bean
&nbsp;        @Primary
&nbsp;        @ConfigurationProperties(&quot;datasource.mfx.hikari&quot;)
&nbsp;        public HikariDataSource mfxDataSource(DataSourceProperties mfxDataSourceProperties) {
<b class="fc">&nbsp;                return mfxDataSourceProperties.initializeDataSourceBuilder().type(HikariDataSource.class).build();</b>
&nbsp;        }
&nbsp;
&nbsp;        @Primary
&nbsp;        @Bean
&nbsp;        public LocalContainerEntityManagerFactoryBean mfxEmf(EntityManagerFactoryBuilder builder, HikariDataSource mfxDataSource) {
<b class="fc">&nbsp;            return builder</b>
<b class="fc">&nbsp;                    .dataSource(mfxDataSource)</b>
<b class="fc">&nbsp;                    .properties(Map.of(&quot;hibernate.dialect&quot;, &quot;org.hibernate.dialect.SQLServerDialect&quot;))</b>
<b class="fc">&nbsp;                    .packages(CitizenEntity.class, CitizenInfoEntity.class)</b>
<b class="fc">&nbsp;                    .build();</b>
&nbsp;        }
&nbsp;
&nbsp;        @Primary
&nbsp;        @Bean
&nbsp;        public PlatformTransactionManager mfxTransactionManager(LocalContainerEntityManagerFactoryBean mfxEmf) {
<b class="fc">&nbsp;            return new JpaTransactionManager(mfxEmf.getObject());</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    @EnableJpaRepositories(
&nbsp;            basePackages = &quot;be.fgov.onerva.person.backend.request&quot;,
&nbsp;            entityManagerFactoryRef = &quot;personEmf&quot;,
&nbsp;            transactionManagerRef= &quot;personTransactionManager&quot;
&nbsp;    )
&nbsp;    @Configuration(proxyBeanMethods = false)
<b class="fc">&nbsp;    static class Person {</b>
&nbsp;
&nbsp;        @Bean
&nbsp;        @ConfigurationProperties(&quot;datasource.person&quot;)
&nbsp;        public DataSourceProperties personDataSourceProperties() {
<b class="fc">&nbsp;            return new DataSourceProperties();</b>
&nbsp;        }
&nbsp;
&nbsp;        @FlywayDataSource
&nbsp;        @Bean
&nbsp;        @ConfigurationProperties(&quot;datasource.person.hikari&quot;)
&nbsp;        public HikariDataSource personDataSource(@Qualifier(&quot;personDataSourceProperties&quot;) DataSourceProperties personDataSourceProperties) {
<b class="fc">&nbsp;            return personDataSourceProperties.initializeDataSourceBuilder().type(HikariDataSource.class).build();</b>
&nbsp;        }
&nbsp;
&nbsp;        @Bean
&nbsp;        public LocalContainerEntityManagerFactoryBean personEmf(EntityManagerFactoryBuilder builder, @Qualifier(&quot;personDataSource&quot;) HikariDataSource personDataSource) {
<b class="fc">&nbsp;            return builder</b>
<b class="fc">&nbsp;                    .dataSource(personDataSource)</b>
<b class="fc">&nbsp;                    .packages(PersonRequest.class)</b>
<b class="fc">&nbsp;                    .build();</b>
&nbsp;        }
&nbsp;
&nbsp;        @Bean
&nbsp;        public PlatformTransactionManager personTransactionManager(@Qualifier(&quot;personEmf&quot;) LocalContainerEntityManagerFactoryBean personEmf) {
<b class="fc">&nbsp;            return new JpaTransactionManager(personEmf.getObject());</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
