


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenInfoDTO</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">backend.rest.model</a>
</div>

<h1>Coverage Summary for Class: CitizenInfoDTO (backend.rest.model)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">CitizenInfoDTO</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    90.9%
  </span>
  <span class="absValue">
    (90/99)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    48.6%
  </span>
  <span class="absValue">
    (34/70)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    75.1%
  </span>
  <span class="absValue">
    (163/217)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package backend.rest.model;
&nbsp;
&nbsp;import java.net.URI;
&nbsp;import java.util.Objects;
&nbsp;import backend.rest.model.BankAccountDTO;
&nbsp;import backend.rest.model.CitizenInfoUnionDueDTO;
&nbsp;import backend.rest.model.ForeignAddressDTO;
&nbsp;import com.fasterxml.jackson.annotation.JsonProperty;
&nbsp;import com.fasterxml.jackson.annotation.JsonCreator;
&nbsp;import com.fasterxml.jackson.annotation.JsonTypeName;
&nbsp;import java.math.BigDecimal;
&nbsp;import java.time.LocalDate;
&nbsp;import java.util.ArrayList;
&nbsp;import java.util.Arrays;
&nbsp;import java.util.List;
&nbsp;import org.springframework.format.annotation.DateTimeFormat;
&nbsp;import org.springframework.lang.Nullable;
&nbsp;import java.time.OffsetDateTime;
&nbsp;import jakarta.validation.Valid;
&nbsp;import jakarta.validation.constraints.*;
&nbsp;import org.hibernate.validator.constraints.*;
&nbsp;import io.swagger.v3.oas.annotations.media.Schema;
&nbsp;
&nbsp;
&nbsp;import java.util.*;
&nbsp;import jakarta.annotation.Generated;
&nbsp;
&nbsp;/**
&nbsp; * CitizenInfoDTO
&nbsp; */
&nbsp;
&nbsp;@JsonTypeName(&quot;CitizenInfo&quot;)
&nbsp;@Generated(value = &quot;org.openapitools.codegen.languages.SpringCodegen&quot;, date = &quot;2025-07-16T14:11:30.627767400+02:00[Europe/Brussels]&quot;, comments = &quot;Generator version: 7.12.0&quot;)
<b class="fc">&nbsp;public class CitizenInfoDTO {</b>
&nbsp;
<b class="fc">&nbsp;  @Deprecated</b>
&nbsp;  private @Nullable Long id = null;
&nbsp;
&nbsp;  private @Nullable String ssin;
&nbsp;
&nbsp;  private @Nullable BigDecimal numPens;
&nbsp;
&nbsp;  private @Nullable String lastName;
&nbsp;
&nbsp;  private @Nullable String firstName;
&nbsp;
&nbsp;  private @Nullable String address;
&nbsp;
&nbsp;  @Deprecated
&nbsp;  private @Nullable String postalCode;
&nbsp;
&nbsp;  private @Nullable ForeignAddressDTO addressObj;
&nbsp;
&nbsp;  private @Nullable BigDecimal numBox;
&nbsp;
&nbsp;  private @Nullable BigDecimal OP;
&nbsp;
&nbsp;  private @Nullable BigDecimal unemploymentOffice;
&nbsp;
<b class="fc">&nbsp;  @Deprecated</b>
&nbsp;  private @Nullable String iban = null;
&nbsp;
&nbsp;  private @Nullable BankAccountDTO bankAccount;
&nbsp;
&nbsp;  private @Nullable Integer paymentMode;
&nbsp;
<b class="fc">&nbsp;  private @Nullable String language = null;</b>
&nbsp;
&nbsp;  private @Nullable String sex;
&nbsp;
&nbsp;  private @Nullable BigDecimal flagNation;
&nbsp;
<b class="fc">&nbsp;  @Deprecated</b>
&nbsp;  private @Nullable BigDecimal flagVCpte = null;
&nbsp;
<b class="fc">&nbsp;  private @Nullable String email = null;</b>
&nbsp;
<b class="fc">&nbsp;  private @Nullable String emailReg = null;</b>
&nbsp;
&nbsp;  private @Nullable String flagToPurge;
&nbsp;
&nbsp;  private @Nullable Integer lastModifDate;
&nbsp;
<b class="fc">&nbsp;  private @Nullable String telephoneOnem = null;</b>
&nbsp;
<b class="fc">&nbsp;  private @Nullable String gsmOnem = null;</b>
&nbsp;
<b class="fc">&nbsp;  private @Nullable String telephoneReg = null;</b>
&nbsp;
<b class="fc">&nbsp;  private @Nullable String gsmReg = null;</b>
&nbsp;
<b class="fc">&nbsp;  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)</b>
&nbsp;  private @Nullable LocalDate birthDate = null;
&nbsp;
<b class="fc">&nbsp;  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)</b>
&nbsp;  private @Nullable LocalDate deceasedDate = null;
&nbsp;
&nbsp;  @Valid
&nbsp;  private @Nullable List&lt;String&gt; bisNumber;
&nbsp;
&nbsp;  private @Nullable Integer employmentContract;
&nbsp;
<b class="fc">&nbsp;  private @Nullable CitizenInfoUnionDueDTO unionDue = null;</b>
&nbsp;
&nbsp;  public CitizenInfoDTO id(Long id) {
<b class="nc">&nbsp;    this.id = id;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Was never implemented.
&nbsp;   * @return id
&nbsp;   * @deprecated
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;id&quot;, description = &quot;Was never implemented.&quot;, deprecated = true, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;id&quot;)
&nbsp;  @Deprecated
&nbsp;  public Long getId() {
<b class="fc">&nbsp;    return id;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * @deprecated
&nbsp;   */
&nbsp;  @Deprecated
&nbsp;  public void setId(Long id) {
<b class="fc">&nbsp;    this.id = id;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO ssin(String ssin) {
<b class="fc">&nbsp;    this.ssin = ssin;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get ssin
&nbsp;   * @return ssin
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;ssin&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;ssin&quot;)
&nbsp;  public String getSsin() {
<b class="fc">&nbsp;    return ssin;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setSsin(String ssin) {
<b class="fc">&nbsp;    this.ssin = ssin;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO numPens(BigDecimal numPens) {
<b class="fc">&nbsp;    this.numPens = numPens;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get numPens
&nbsp;   * @return numPens
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;numPens&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;numPens&quot;)
&nbsp;  public BigDecimal getNumPens() {
<b class="fc">&nbsp;    return numPens;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setNumPens(BigDecimal numPens) {
<b class="fc">&nbsp;    this.numPens = numPens;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO lastName(String lastName) {
<b class="fc">&nbsp;    this.lastName = lastName;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get lastName
&nbsp;   * @return lastName
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;lastName&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;lastName&quot;)
&nbsp;  public String getLastName() {
<b class="fc">&nbsp;    return lastName;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setLastName(String lastName) {
<b class="fc">&nbsp;    this.lastName = lastName;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO firstName(String firstName) {
<b class="fc">&nbsp;    this.firstName = firstName;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get firstName
&nbsp;   * @return firstName
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;firstName&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;firstName&quot;)
&nbsp;  public String getFirstName() {
<b class="fc">&nbsp;    return firstName;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setFirstName(String firstName) {
<b class="fc">&nbsp;    this.firstName = firstName;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO address(String address) {
<b class="fc">&nbsp;    this.address = address;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * the address line, see addressObj for more info
&nbsp;   * @return address
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;address&quot;, description = &quot;the address line, see addressObj for more info&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;address&quot;)
&nbsp;  public String getAddress() {
<b class="fc">&nbsp;    return address;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setAddress(String address) {
<b class="fc">&nbsp;    this.address = address;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO postalCode(String postalCode) {
<b class="fc">&nbsp;    this.postalCode = postalCode;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * use the addressObj instead
&nbsp;   * @return postalCode
&nbsp;   * @deprecated
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;postalCode&quot;, description = &quot;use the addressObj instead&quot;, deprecated = true, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;postalCode&quot;)
&nbsp;  @Deprecated
&nbsp;  public String getPostalCode() {
<b class="fc">&nbsp;    return postalCode;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * @deprecated
&nbsp;   */
&nbsp;  @Deprecated
&nbsp;  public void setPostalCode(String postalCode) {
<b class="fc">&nbsp;    this.postalCode = postalCode;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO addressObj(ForeignAddressDTO addressObj) {
<b class="fc">&nbsp;    this.addressObj = addressObj;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get addressObj
&nbsp;   * @return addressObj
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;addressObj&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;addressObj&quot;)
&nbsp;  public ForeignAddressDTO getAddressObj() {
<b class="fc">&nbsp;    return addressObj;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setAddressObj(ForeignAddressDTO addressObj) {
<b class="fc">&nbsp;    this.addressObj = addressObj;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO numBox(BigDecimal numBox) {
<b class="fc">&nbsp;    this.numBox = numBox;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get numBox
&nbsp;   * @return numBox
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;numBox&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;numBox&quot;)
&nbsp;  public BigDecimal getNumBox() {
<b class="fc">&nbsp;    return numBox;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setNumBox(BigDecimal numBox) {
<b class="fc">&nbsp;    this.numBox = numBox;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO OP(BigDecimal OP) {
<b class="fc">&nbsp;    this.OP = OP;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get OP
&nbsp;   * @return OP
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;OP&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;OP&quot;)
&nbsp;  public BigDecimal getOP() {
<b class="fc">&nbsp;    return OP;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setOP(BigDecimal OP) {
<b class="fc">&nbsp;    this.OP = OP;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO unemploymentOffice(BigDecimal unemploymentOffice) {
<b class="fc">&nbsp;    this.unemploymentOffice = unemploymentOffice;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get unemploymentOffice
&nbsp;   * @return unemploymentOffice
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;unemploymentOffice&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;unemploymentOffice&quot;)
&nbsp;  public BigDecimal getUnemploymentOffice() {
<b class="fc">&nbsp;    return unemploymentOffice;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setUnemploymentOffice(BigDecimal unemploymentOffice) {
<b class="fc">&nbsp;    this.unemploymentOffice = unemploymentOffice;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO iban(String iban) {
<b class="fc">&nbsp;    this.iban = iban;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * use the bankAccount instead
&nbsp;   * @return iban
&nbsp;   * @deprecated
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;iban&quot;, description = &quot;use the bankAccount instead&quot;, deprecated = true, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;iban&quot;)
&nbsp;  @Deprecated
&nbsp;  public String getIban() {
<b class="fc">&nbsp;    return iban;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * @deprecated
&nbsp;   */
&nbsp;  @Deprecated
&nbsp;  public void setIban(String iban) {
<b class="fc">&nbsp;    this.iban = iban;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO bankAccount(BankAccountDTO bankAccount) {
<b class="fc">&nbsp;    this.bankAccount = bankAccount;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get bankAccount
&nbsp;   * @return bankAccount
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;bankAccount&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;bankAccount&quot;)
&nbsp;  public BankAccountDTO getBankAccount() {
<b class="fc">&nbsp;    return bankAccount;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setBankAccount(BankAccountDTO bankAccount) {
<b class="fc">&nbsp;    this.bankAccount = bankAccount;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO paymentMode(Integer paymentMode) {
<b class="fc">&nbsp;    this.paymentMode = paymentMode;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * List of possible values http://services/lookupwppt/lookups/signaletic/SignaleticPaymentWayList.seam
&nbsp;   * @return paymentMode
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;paymentMode&quot;, description = &quot;List of possible values http://services/lookupwppt/lookups/signaletic/SignaleticPaymentWayList.seam&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;paymentMode&quot;)
&nbsp;  public Integer getPaymentMode() {
<b class="fc">&nbsp;    return paymentMode;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setPaymentMode(Integer paymentMode) {
<b class="fc">&nbsp;    this.paymentMode = paymentMode;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO language(String language) {
<b class="fc">&nbsp;    this.language = language;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get language
&nbsp;   * @return language
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;language&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;language&quot;)
&nbsp;  public String getLanguage() {
<b class="fc">&nbsp;    return language;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setLanguage(String language) {
<b class="fc">&nbsp;    this.language = language;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO sex(String sex) {
<b class="fc">&nbsp;    this.sex = sex;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get sex
&nbsp;   * @return sex
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;sex&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;sex&quot;)
&nbsp;  public String getSex() {
<b class="fc">&nbsp;    return sex;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setSex(String sex) {
<b class="fc">&nbsp;    this.sex = sex;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO flagNation(BigDecimal flagNation) {
<b class="fc">&nbsp;    this.flagNation = flagNation;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get flagNation
&nbsp;   * @return flagNation
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;flagNation&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;flagNation&quot;)
&nbsp;  public BigDecimal getFlagNation() {
<b class="fc">&nbsp;    return flagNation;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setFlagNation(BigDecimal flagNation) {
<b class="fc">&nbsp;    this.flagNation = flagNation;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO flagVCpte(BigDecimal flagVCpte) {
<b class="nc">&nbsp;    this.flagVCpte = flagVCpte;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Was never implemented.
&nbsp;   * @return flagVCpte
&nbsp;   * @deprecated
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;flagVCpte&quot;, description = &quot;Was never implemented.&quot;, deprecated = true, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;flagVCpte&quot;)
&nbsp;  @Deprecated
&nbsp;  public BigDecimal getFlagVCpte() {
<b class="fc">&nbsp;    return flagVCpte;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * @deprecated
&nbsp;   */
&nbsp;  @Deprecated
&nbsp;  public void setFlagVCpte(BigDecimal flagVCpte) {
<b class="fc">&nbsp;    this.flagVCpte = flagVCpte;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO email(String email) {
<b class="fc">&nbsp;    this.email = email;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get email
&nbsp;   * @return email
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;email&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;email&quot;)
&nbsp;  public String getEmail() {
<b class="fc">&nbsp;    return email;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setEmail(String email) {
<b class="fc">&nbsp;    this.email = email;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO emailReg(String emailReg) {
<b class="fc">&nbsp;    this.emailReg = emailReg;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get emailReg
&nbsp;   * @return emailReg
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;emailReg&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;emailReg&quot;)
&nbsp;  public String getEmailReg() {
<b class="fc">&nbsp;    return emailReg;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setEmailReg(String emailReg) {
<b class="fc">&nbsp;    this.emailReg = emailReg;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO flagToPurge(String flagToPurge) {
<b class="fc">&nbsp;    this.flagToPurge = flagToPurge;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get flagToPurge
&nbsp;   * @return flagToPurge
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;flagToPurge&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;flagToPurge&quot;)
&nbsp;  public String getFlagToPurge() {
<b class="fc">&nbsp;    return flagToPurge;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setFlagToPurge(String flagToPurge) {
<b class="fc">&nbsp;    this.flagToPurge = flagToPurge;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO lastModifDate(Integer lastModifDate) {
<b class="fc">&nbsp;    this.lastModifDate = lastModifDate;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get lastModifDate
&nbsp;   * @return lastModifDate
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;lastModifDate&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;lastModifDate&quot;)
&nbsp;  public Integer getLastModifDate() {
<b class="fc">&nbsp;    return lastModifDate;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setLastModifDate(Integer lastModifDate) {
<b class="fc">&nbsp;    this.lastModifDate = lastModifDate;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO telephoneOnem(String telephoneOnem) {
<b class="nc">&nbsp;    this.telephoneOnem = telephoneOnem;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get telephoneOnem
&nbsp;   * @return telephoneOnem
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;telephoneOnem&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;telephoneOnem&quot;)
&nbsp;  public String getTelephoneOnem() {
<b class="fc">&nbsp;    return telephoneOnem;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setTelephoneOnem(String telephoneOnem) {
<b class="fc">&nbsp;    this.telephoneOnem = telephoneOnem;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO gsmOnem(String gsmOnem) {
<b class="nc">&nbsp;    this.gsmOnem = gsmOnem;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get gsmOnem
&nbsp;   * @return gsmOnem
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;gsmOnem&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;gsmOnem&quot;)
&nbsp;  public String getGsmOnem() {
<b class="fc">&nbsp;    return gsmOnem;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setGsmOnem(String gsmOnem) {
<b class="fc">&nbsp;    this.gsmOnem = gsmOnem;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO telephoneReg(String telephoneReg) {
<b class="fc">&nbsp;    this.telephoneReg = telephoneReg;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get telephoneReg
&nbsp;   * @return telephoneReg
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;telephoneReg&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;telephoneReg&quot;)
&nbsp;  public String getTelephoneReg() {
<b class="fc">&nbsp;    return telephoneReg;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setTelephoneReg(String telephoneReg) {
<b class="fc">&nbsp;    this.telephoneReg = telephoneReg;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO gsmReg(String gsmReg) {
<b class="fc">&nbsp;    this.gsmReg = gsmReg;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get gsmReg
&nbsp;   * @return gsmReg
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;gsmReg&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;gsmReg&quot;)
&nbsp;  public String getGsmReg() {
<b class="fc">&nbsp;    return gsmReg;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setGsmReg(String gsmReg) {
<b class="fc">&nbsp;    this.gsmReg = gsmReg;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO birthDate(LocalDate birthDate) {
<b class="nc">&nbsp;    this.birthDate = birthDate;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get birthDate
&nbsp;   * @return birthDate
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;birthDate&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;birthDate&quot;)
&nbsp;  public LocalDate getBirthDate() {
<b class="fc">&nbsp;    return birthDate;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setBirthDate(LocalDate birthDate) {
<b class="fc">&nbsp;    this.birthDate = birthDate;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO deceasedDate(LocalDate deceasedDate) {
<b class="fc">&nbsp;    this.deceasedDate = deceasedDate;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get deceasedDate
&nbsp;   * @return deceasedDate
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;deceasedDate&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;deceasedDate&quot;)
&nbsp;  public LocalDate getDeceasedDate() {
<b class="fc">&nbsp;    return deceasedDate;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setDeceasedDate(LocalDate deceasedDate) {
<b class="fc">&nbsp;    this.deceasedDate = deceasedDate;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO bisNumber(List&lt;String&gt; bisNumber) {
<b class="fc">&nbsp;    this.bisNumber = bisNumber;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO addBisNumberItem(String bisNumberItem) {
<b class="nc">&nbsp;    if (this.bisNumber == null) {</b>
<b class="nc">&nbsp;      this.bisNumber = new ArrayList&lt;&gt;();</b>
&nbsp;    }
<b class="nc">&nbsp;    this.bisNumber.add(bisNumberItem);</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get bisNumber
&nbsp;   * @return bisNumber
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;bisNumber&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;bisNumber&quot;)
&nbsp;  public List&lt;String&gt; getBisNumber() {
<b class="fc">&nbsp;    return bisNumber;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setBisNumber(List&lt;String&gt; bisNumber) {
<b class="fc">&nbsp;    this.bisNumber = bisNumber;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO employmentContract(Integer employmentContract) {
<b class="fc">&nbsp;    this.employmentContract = employmentContract;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Type of employment contract. List of possible values http://services/lookupwppt/lookups/signaletic/SignaleticWorkContractTypeList.seam
&nbsp;   * @return employmentContract
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;employmentContract&quot;, description = &quot;Type of employment contract. List of possible values http://services/lookupwppt/lookups/signaletic/SignaleticWorkContractTypeList.seam&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;employmentContract&quot;)
&nbsp;  public Integer getEmploymentContract() {
<b class="fc">&nbsp;    return employmentContract;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setEmploymentContract(Integer employmentContract) {
<b class="fc">&nbsp;    this.employmentContract = employmentContract;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoDTO unionDue(CitizenInfoUnionDueDTO unionDue) {
<b class="fc">&nbsp;    this.unionDue = unionDue;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get unionDue
&nbsp;   * @return unionDue
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;unionDue&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;unionDue&quot;)
&nbsp;  public CitizenInfoUnionDueDTO getUnionDue() {
<b class="fc">&nbsp;    return unionDue;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setUnionDue(CitizenInfoUnionDueDTO unionDue) {
<b class="fc">&nbsp;    this.unionDue = unionDue;</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public boolean equals(Object o) {
<b class="fc">&nbsp;    if (this == o) {</b>
<b class="fc">&nbsp;      return true;</b>
&nbsp;    }
<b class="pc">&nbsp;    if (o == null || getClass() != o.getClass()) {</b>
<b class="nc">&nbsp;      return false;</b>
&nbsp;    }
<b class="fc">&nbsp;    CitizenInfoDTO citizenInfo = (CitizenInfoDTO) o;</b>
<b class="pc">&nbsp;    return Objects.equals(this.id, citizenInfo.id) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.ssin, citizenInfo.ssin) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.numPens, citizenInfo.numPens) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.lastName, citizenInfo.lastName) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.firstName, citizenInfo.firstName) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.address, citizenInfo.address) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.postalCode, citizenInfo.postalCode) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.addressObj, citizenInfo.addressObj) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.numBox, citizenInfo.numBox) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.OP, citizenInfo.OP) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.unemploymentOffice, citizenInfo.unemploymentOffice) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.iban, citizenInfo.iban) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.bankAccount, citizenInfo.bankAccount) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.paymentMode, citizenInfo.paymentMode) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.language, citizenInfo.language) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.sex, citizenInfo.sex) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.flagNation, citizenInfo.flagNation) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.flagVCpte, citizenInfo.flagVCpte) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.email, citizenInfo.email) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.emailReg, citizenInfo.emailReg) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.flagToPurge, citizenInfo.flagToPurge) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.lastModifDate, citizenInfo.lastModifDate) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.telephoneOnem, citizenInfo.telephoneOnem) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.gsmOnem, citizenInfo.gsmOnem) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.telephoneReg, citizenInfo.telephoneReg) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.gsmReg, citizenInfo.gsmReg) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.birthDate, citizenInfo.birthDate) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.deceasedDate, citizenInfo.deceasedDate) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.bisNumber, citizenInfo.bisNumber) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.employmentContract, citizenInfo.employmentContract) &amp;&amp;</b>
<b class="fc">&nbsp;        Objects.equals(this.unionDue, citizenInfo.unionDue);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public int hashCode() {
<b class="nc">&nbsp;    return Objects.hash(id, ssin, numPens, lastName, firstName, address, postalCode, addressObj, numBox, OP, unemploymentOffice, iban, bankAccount, paymentMode, language, sex, flagNation, flagVCpte, email, emailReg, flagToPurge, lastModifDate, telephoneOnem, gsmOnem, telephoneReg, gsmReg, birthDate, deceasedDate, bisNumber, employmentContract, unionDue);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public String toString() {
<b class="nc">&nbsp;    StringBuilder sb = new StringBuilder();</b>
<b class="nc">&nbsp;    sb.append(&quot;class CitizenInfoDTO {\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    id: &quot;).append(toIndentedString(id)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    ssin: &quot;).append(toIndentedString(ssin)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    numPens: &quot;).append(toIndentedString(numPens)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    lastName: &quot;).append(toIndentedString(lastName)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    firstName: &quot;).append(toIndentedString(firstName)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    address: &quot;).append(toIndentedString(address)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    postalCode: &quot;).append(toIndentedString(postalCode)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    addressObj: &quot;).append(toIndentedString(addressObj)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    numBox: &quot;).append(toIndentedString(numBox)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    OP: &quot;).append(toIndentedString(OP)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    unemploymentOffice: &quot;).append(toIndentedString(unemploymentOffice)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    iban: &quot;).append(toIndentedString(iban)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    bankAccount: &quot;).append(toIndentedString(bankAccount)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    paymentMode: &quot;).append(toIndentedString(paymentMode)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    language: &quot;).append(toIndentedString(language)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    sex: &quot;).append(toIndentedString(sex)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    flagNation: &quot;).append(toIndentedString(flagNation)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    flagVCpte: &quot;).append(toIndentedString(flagVCpte)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    email: &quot;).append(toIndentedString(email)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    emailReg: &quot;).append(toIndentedString(emailReg)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    flagToPurge: &quot;).append(toIndentedString(flagToPurge)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    lastModifDate: &quot;).append(toIndentedString(lastModifDate)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    telephoneOnem: &quot;).append(toIndentedString(telephoneOnem)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    gsmOnem: &quot;).append(toIndentedString(gsmOnem)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    telephoneReg: &quot;).append(toIndentedString(telephoneReg)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    gsmReg: &quot;).append(toIndentedString(gsmReg)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    birthDate: &quot;).append(toIndentedString(birthDate)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    deceasedDate: &quot;).append(toIndentedString(deceasedDate)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    bisNumber: &quot;).append(toIndentedString(bisNumber)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    employmentContract: &quot;).append(toIndentedString(employmentContract)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    unionDue: &quot;).append(toIndentedString(unionDue)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;}&quot;);</b>
<b class="nc">&nbsp;    return sb.toString();</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Convert the given object to string with each line indented by 4 spaces
&nbsp;   * (except the first line).
&nbsp;   */
&nbsp;  private String toIndentedString(Object o) {
<b class="nc">&nbsp;    if (o == null) {</b>
<b class="nc">&nbsp;      return &quot;null&quot;;</b>
&nbsp;    }
<b class="nc">&nbsp;    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</b>
&nbsp;  }
&nbsp;}
&nbsp;
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
