


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenInfoController</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.citizeninfo</a>
</div>

<h1>Coverage Summary for Class: CitizenInfoController (be.fgov.onerva.person.backend.citizeninfo)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">CitizenInfoController</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (2/2)
  </span>
</td>
</tr>
  <tr>
    <td class="name">CitizenInfoController$$SpringCGLIB$$0</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (2/2)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.citizeninfo;
&nbsp;
&nbsp;import backend.api.CitizenInfoApi;
&nbsp;import backend.rest.model.CitizenInfoPageDTO;
&nbsp;import be.fgov.onerva.person.backend.citizeninfo.service.CitizenInfoService;
&nbsp;import lombok.RequiredArgsConstructor;
&nbsp;import org.springframework.data.domain.PageRequest;
&nbsp;import org.springframework.data.domain.Sort;
&nbsp;import org.springframework.http.ResponseEntity;
&nbsp;import org.springframework.web.bind.annotation.CrossOrigin;
&nbsp;import org.springframework.web.bind.annotation.RequestMapping;
&nbsp;import org.springframework.web.bind.annotation.RestController;
&nbsp;
&nbsp;import java.util.List;
&nbsp;
&nbsp;import static org.springframework.http.ResponseEntity.ok;
&nbsp;
&nbsp;@RequiredArgsConstructor
&nbsp;@RestController
&nbsp;@CrossOrigin(origins = &quot;*&quot;, maxAge = 3600)
&nbsp;@RequestMapping(&quot;/api&quot;)
&nbsp;public class CitizenInfoController implements CitizenInfoApi {
&nbsp;
&nbsp;    private final CitizenInfoService service;
&nbsp;
&nbsp;
&nbsp;    @Override public ResponseEntity&lt;CitizenInfoPageDTO&gt; searchCitizenInfo(List&lt;String&gt; ssins, List&lt;Integer&gt; citizenId, String dataReturned, Integer pageNumber, Integer pageSize) {
<b class="fc">&nbsp;        var citizenInfo = service.findCitizenInfo(ssins, citizenId, PageRequest.of(pageNumber, pageSize, Sort.by(Sort.Direction.ASC, &quot;numPens&quot;)));</b>
<b class="fc">&nbsp;        return ok(citizenInfo);</b>
&nbsp;    }
&nbsp;
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
