


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > HttpBasicAuth</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.wave.auth</a>
</div>

<h1>Coverage Summary for Class: HttpBasicAuth (be.fgov.onerva.wave.auth)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">HttpBasicAuth</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/6)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/8)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/8)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;/*
&nbsp; * WO facade API
&nbsp; * API to manage tasks (and processes) in a simplified manner
&nbsp; *
&nbsp; * The version of the OpenAPI document: v1
&nbsp; * 
&nbsp; *
&nbsp; * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
&nbsp; * https://openapi-generator.tech
&nbsp; * Do not edit the class manually.
&nbsp; */
&nbsp;
&nbsp;
&nbsp;package be.fgov.onerva.wave.auth;
&nbsp;
&nbsp;import java.nio.charset.StandardCharsets;
&nbsp;import java.util.Base64;
&nbsp;
&nbsp;import org.springframework.http.HttpHeaders;
&nbsp;import org.springframework.util.MultiValueMap;
&nbsp;
&nbsp;@jakarta.annotation.Generated(value = &quot;org.openapitools.codegen.languages.JavaClientCodegen&quot;, date = &quot;2025-07-16T14:11:32.899250600+02:00[Europe/Brussels]&quot;, comments = &quot;Generator version: 7.12.0&quot;)
<b class="nc">&nbsp;public class HttpBasicAuth implements Authentication {</b>
&nbsp;    private String username;
&nbsp;    private String password;
&nbsp;
&nbsp;    public String getUsername() {
<b class="nc">&nbsp;        return username;</b>
&nbsp;    }
&nbsp;
&nbsp;    public void setUsername(String username) {
<b class="nc">&nbsp;        this.username = username;</b>
&nbsp;    }
&nbsp;
&nbsp;    public String getPassword() {
<b class="nc">&nbsp;        return password;</b>
&nbsp;    }
&nbsp;
&nbsp;    public void setPassword(String password) {
<b class="nc">&nbsp;        this.password = password;</b>
&nbsp;    }
&nbsp;
&nbsp;    @Override
&nbsp;    public void applyToParams(MultiValueMap&lt;String, String&gt; queryParams, HttpHeaders headerParams, MultiValueMap&lt;String, String&gt; cookieParams) {
<b class="nc">&nbsp;        if (username == null &amp;&amp; password == null) {</b>
&nbsp;            return;
&nbsp;        }
<b class="nc">&nbsp;        String str = (username == null ? &quot;&quot; : username) + &quot;:&quot; + (password == null ? &quot;&quot; : password);</b>
<b class="nc">&nbsp;        headerParams.add(HttpHeaders.AUTHORIZATION, &quot;Basic &quot; + Base64.getEncoder().encodeToString(str.getBytes(StandardCharsets.UTF_8)));</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
