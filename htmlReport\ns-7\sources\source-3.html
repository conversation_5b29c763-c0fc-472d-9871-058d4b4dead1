


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenService</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.citizen.service</a>
</div>

<h1>Coverage Summary for Class: CitizenService (be.fgov.onerva.person.backend.citizen.service)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">CitizenService</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (9/9)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    67.6%
  </span>
  <span class="absValue">
    (46/68)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    85.3%
  </span>
  <span class="absValue">
    (64/75)
  </span>
</td>
</tr>
  <tr>
    <td class="name">CitizenService$$SpringCGLIB$$0</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (9/9)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    67.6%
  </span>
  <span class="absValue">
    (46/68)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    85.3%
  </span>
  <span class="absValue">
    (64/75)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.citizen.service;
&nbsp;
&nbsp;import be.fgov.onerva.common.utils.InssUtils;
&nbsp;import be.fgov.onerva.common.utils.PensionNumberUtils;
&nbsp;import be.fgov.onerva.person.backend.citizen.model.BusinessDomain;
&nbsp;import be.fgov.onerva.person.backend.citizen.model.CitizenCreationRequest;
&nbsp;import be.fgov.onerva.person.backend.citizen.model.CitizenEntity;
&nbsp;import be.fgov.onerva.person.backend.citizen.model.CitizenUpdateRequest;
&nbsp;import be.fgov.onerva.person.backend.citizen.persistence.CitizenRepository;
&nbsp;import be.fgov.onerva.person.backend.lookup.LookupClient;
&nbsp;import be.fgov.onerva.person.backend.request.model.PersonRequest;
&nbsp;import be.fgov.onerva.person.backend.request.service.PersonRequestService;
&nbsp;import be.fgov.onerva.person.backend.validation.CountryCodeValidator;
&nbsp;import be.fgov.onerva.person.backend.validation.IbanValidator;
&nbsp;import be.fgov.onerva.person.backend.validation.NationalityCodeValidator;
&nbsp;import be.fgov.onerva.wave.api.UserApi;
&nbsp;import be.fgov.onerva.wave.model.User;
&nbsp;import be.fgov.onerva.wave.model.UserCriteria;
&nbsp;import io.github.perplexhub.rsql.RSQLJPASupport;
&nbsp;import jakarta.validation.Valid;
&nbsp;import jakarta.validation.constraints.NotNull;
&nbsp;import lombok.RequiredArgsConstructor;
&nbsp;import lombok.extern.slf4j.Slf4j;
&nbsp;import org.springframework.data.domain.Page;
&nbsp;import org.springframework.data.domain.Pageable;
&nbsp;import org.springframework.http.ProblemDetail;
&nbsp;import org.springframework.stereotype.Service;
&nbsp;import org.springframework.validation.annotation.Validated;
&nbsp;import org.springframework.web.ErrorResponseException;
&nbsp;import org.springframework.web.client.HttpClientErrorException;
&nbsp;
<b class="fc">&nbsp;@Slf4j</b>
&nbsp;@Service
&nbsp;@Validated
&nbsp;@RequiredArgsConstructor
&nbsp;public class CitizenService {
&nbsp;
&nbsp;    private final CitizenRepository citizenRepository;
&nbsp;    private final PersonRequestService personRequestService;
&nbsp;    private final LookupClient lookupClient;
&nbsp;    private final UserApi userApi;
&nbsp;    private final CountryCodeValidator countryCodeValidator;
&nbsp;    private final NationalityCodeValidator nationalityCodeValidator;
&nbsp;    private final IbanValidator ibanValidator;
&nbsp;
&nbsp;    public CitizenEntity getByNiss(@NotNull String niss) {
<b class="fc">&nbsp;        var numpens = PensionNumberUtils.convertFromInss(Long.parseLong(niss));</b>
<b class="fc">&nbsp;        if (numpens == null) {</b>
<b class="fc">&nbsp;            throw new CitizenNotFoundException();</b>
&nbsp;        }
<b class="fc">&nbsp;        return citizenRepository.findById(numpens).orElseThrow(CitizenNotFoundException::new);</b>
&nbsp;    }
&nbsp;
&nbsp;    public CitizenEntity getByNumbox(@NotNull Integer numbox) {
&nbsp;        // TODO quick fix ... should be further analyzed
<b class="fc">&nbsp;        return citizenRepository.findByNumBox(numbox).stream()</b>
<b class="fc">&nbsp;                .filter(citizen -&gt; citizen.getLastId() == 9)</b>
<b class="fc">&nbsp;                .findAny()</b>
<b class="fc">&nbsp;                .orElseThrow(CitizenNotFoundException::new);</b>
&nbsp;    }
&nbsp;
&nbsp;    public Page&lt;CitizenEntity&gt; searchCitizenByQuery(@NotNull String query, @NotNull Pageable pageable) {
<b class="fc">&nbsp;        return citizenRepository.findAll(RSQLJPASupport.rsql(query), pageable);</b>
&nbsp;    }
&nbsp;
&nbsp;    public PersonRequest createCitizen(@Valid @NotNull CitizenCreationRequest request) {
<b class="fc">&nbsp;        if (request.isAllowance()) {</b>
<b class="fc">&nbsp;            throw new UnsupportedOperationException(&quot;Only supported for citizens that are not asking an allowance.&quot;);</b>
&nbsp;        }
<b class="fc">&nbsp;        if (request.getDomain() != BusinessDomain.ADMISSIBILITY) {</b>
<b class="fc">&nbsp;            throw new UnsupportedOperationException(&quot;Only business domain ADMISSIBILITY supported for the moment.&quot;);</b>
&nbsp;        }
<b class="fc">&nbsp;        long inss = Long.parseLong(request.getNiss());</b>
<b class="fc">&nbsp;        if (!InssUtils.isValid(inss)) {</b>
<b class="fc">&nbsp;            throw new IllegalArgumentException(&quot;Invalid inss: &quot; + request.getNiss());</b>
&nbsp;        }
<b class="fc">&nbsp;        var numpens = PensionNumberUtils.convertFromInss(inss);</b>
<b class="fc">&nbsp;        if (citizenRepository.existsById(numpens)) {</b>
<b class="fc">&nbsp;            throw new CitizenExistsException();</b>
&nbsp;        }
<b class="fc">&nbsp;        return personRequestService.createMinimalPersonInfo(request.getFirstname(), request.getLastname(),</b>
<b class="fc">&nbsp;                request.getNiss(), request.getCorrelationId());</b>
&nbsp;    }
&nbsp;
&nbsp;    public PersonRequest updateCitizen(@Valid @NotNull CitizenUpdateRequest request) {
<b class="fc">&nbsp;        long inss = Long.parseLong(request.getNiss());</b>
<b class="fc">&nbsp;        if (!InssUtils.isValid(inss)) {</b>
<b class="fc">&nbsp;            throw new IllegalArgumentException(&quot;Invalid inss: &quot; + request.getNiss());</b>
&nbsp;        }
<b class="fc">&nbsp;        var numpens = PensionNumberUtils.convertFromInss(inss);</b>
<b class="fc">&nbsp;        if (!citizenRepository.existsById(numpens)) {</b>
<b class="fc">&nbsp;            throw new CitizenNotFoundException();</b>
&nbsp;        }
&nbsp;        // Validate nationality code using CountryCodeValidator
<b class="fc">&nbsp;        if (request.getNationalityCode() != null) {</b>
<b class="fc">&nbsp;            if (!nationalityCodeValidator.isValid(request.getNationalityCode())) {</b>
<b class="fc">&nbsp;                throw new IllegalArgumentException(&quot;Invalid nationality code: &quot; + request.getNationalityCode());</b>
&nbsp;            }
&nbsp;        }
&nbsp;
&nbsp;        // Validate address country code
<b class="pc">&nbsp;        if (request.getAddress() != null &amp;&amp; request.getAddress().getCountryCode() != null) {</b>
<b class="pc">&nbsp;            if (!countryCodeValidator.isValid(request.getAddress().getCountryCode())) {</b>
<b class="nc">&nbsp;                throw new IllegalArgumentException(&quot;Invalid country code: &quot; + request.getAddress().getCountryCode());</b>
&nbsp;            }
&nbsp;        }
&nbsp;        // Validate zip code based on country
<b class="fc">&nbsp;        validateZipCode(request);</b>
&nbsp;
&nbsp;        // Validate language code (1=fr, 2=nl, 3=de)
<b class="pc">&nbsp;        if (request.getLanguageCode() != null) {</b>
<b class="nc">&nbsp;            if (request.getLanguageCode() &lt; 1 || request.getLanguageCode() &gt; 3) {</b>
<b class="nc">&nbsp;                throw new IllegalArgumentException(</b>
<b class="nc">&nbsp;                        &quot;Invalid language code: &quot; + request.getLanguageCode() + &quot;. Must be 1=fr, 2=nl, or 3=de&quot;);</b>
&nbsp;            }
&nbsp;        }
&nbsp;
&nbsp;        // Validate bank information
<b class="fc">&nbsp;        validateBankInfo(request);</b>
&nbsp;
&nbsp;        // Validate union due information
<b class="fc">&nbsp;        validateUnionDueInfo(request);</b>
&nbsp;        try {
<b class="fc">&nbsp;            User user = userApi.searchUsers(new UserCriteria().username(request.getUsername()));</b>
&nbsp;
<b class="fc">&nbsp;            return personRequestService.updatePersonInfo(request, user);</b>
&nbsp;        } catch (HttpClientErrorException.Unauthorized | HttpClientErrorException.Forbidden e) {
<b class="fc">&nbsp;            log.error(&quot;Not authorized to call the user endpoint.&quot;, e);</b>
<b class="fc">&nbsp;            throw new ErrorResponseException(</b>
<b class="fc">&nbsp;                    e.getStatusCode(),</b>
<b class="fc">&nbsp;                    ProblemDetail.forStatusAndDetail(e.getStatusCode(),</b>
&nbsp;                            &quot;Not authorized to call the user endpoint! Check client security.&quot;),
&nbsp;                    e);
&nbsp;        } catch (HttpClientErrorException e) {
<b class="fc">&nbsp;            throw new IllegalArgumentException(&quot;Could not find user: &quot; + request.getUsername(), e);</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Validates zip code based on country code.
&nbsp;     * For Belgian addresses (country code 1), validates against Belgian zip codes.
&nbsp;     * For foreign addresses, validates format (up to 10 characters).
&nbsp;     */
&nbsp;    private void validateZipCode(CitizenUpdateRequest request) {
<b class="pc">&nbsp;        if (request.getAddress() == null || request.getAddress().getZip() == null) {</b>
&nbsp;            return;
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        String zip = request.getAddress().getZip();</b>
<b class="fc">&nbsp;        Integer countryCode = request.getAddress().getCountryCode();</b>
&nbsp;
<b class="fc">&nbsp;        if (countryCodeValidator.isBelgium(countryCode)) {</b>
&nbsp;            // Belgian zip code validation (4 digits)
&nbsp;            try {
<b class="fc">&nbsp;                int zipCode = Integer.parseInt(zip);</b>
<b class="pc">&nbsp;                if (lookupClient.findBelgianZipCode(zipCode).isEmpty()) {</b>
<b class="nc">&nbsp;                    throw new IllegalArgumentException(&quot;Invalid Belgian zip code: &quot; + zip);</b>
&nbsp;                }
&nbsp;            } catch (NumberFormatException e) {
<b class="fc">&nbsp;                throw new IllegalArgumentException(&quot;Invalid Belgian zip code format: &quot; + zip + &quot;. Must be 4 digits&quot;);</b>
&nbsp;            }
&nbsp;        } else {
&nbsp;            // Foreign zip code validation (up to 10 characters)
<b class="pc">&nbsp;            if (zip.length() &gt; 10) {</b>
<b class="nc">&nbsp;                throw new IllegalArgumentException(&quot;Foreign zip code cannot exceed 10 characters: &quot; + zip);</b>
&nbsp;            }
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Validates bank information including IBAN, BIC, and business rules.
&nbsp;     */
&nbsp;    private void validateBankInfo(CitizenUpdateRequest request) {
<b class="fc">&nbsp;        String iban = null;</b>
<b class="fc">&nbsp;        String bic = null;</b>
<b class="pc">&nbsp;        Integer countryCode = request.getAddress() != null ? request.getAddress().getCountryCode() : null;</b>
&nbsp;
<b class="fc">&nbsp;        if (request.getBankInfo() != null) {</b>
<b class="fc">&nbsp;            iban = request.getBankInfo().getIban();</b>
<b class="fc">&nbsp;            bic = request.getBankInfo().getBic();</b>
&nbsp;        }
&nbsp;
&nbsp;        // Validate IBAN if provided
<b class="pc">&nbsp;        if (iban != null &amp;&amp; !iban.trim().isEmpty()) {</b>
<b class="pc">&nbsp;            if (!ibanValidator.isValid(iban)) {</b>
<b class="nc">&nbsp;                throw new IllegalArgumentException(&quot;Invalid IBAN: &quot; + iban);</b>
&nbsp;            }
&nbsp;        }
&nbsp;
&nbsp;        // BIC mandatory validation for non-Belgian IBANs
<b class="pc">&nbsp;        if (iban != null &amp;&amp; !iban.trim().isEmpty() &amp;&amp; !countryCodeValidator.isBelgium(countryCode)) {</b>
<b class="nc">&nbsp;            if (bic == null || bic.trim().isEmpty()) {</b>
<b class="nc">&nbsp;                throw new IllegalArgumentException(&quot;BIC is mandatory for non-Belgian IBANs&quot;);</b>
&nbsp;            }
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Validates union due information.
&nbsp;     */
&nbsp;    private void validateUnionDueInfo(CitizenUpdateRequest request) {
&nbsp;        // Check both new nested structure and deprecated fields for backward
&nbsp;        // compatibility
<b class="fc">&nbsp;        if (request.getUnionDueInfo() != null) {</b>
<b class="pc">&nbsp;            if (request.getUnionDueInfo().getUnionDue() == null) {</b>
<b class="nc">&nbsp;                throw new IllegalArgumentException(&quot;Union due status is required when union due info is provided&quot;);</b>
&nbsp;            }
<b class="pc">&nbsp;            if (request.getUnionDueInfo().getValidFrom() == null) {</b>
<b class="nc">&nbsp;                throw new IllegalArgumentException(&quot;Valid from date is required for union due info&quot;);</b>
&nbsp;            }
&nbsp;        }
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
