trigger:
  branches:
    include:
      - '*'

pool: 'Azure-UbuntuMinimal'

# Specifies additional resources to be used in the Pipeline.
# In this case, we import our custom templates.
resources:
  repositories:
    - repository: templates
      type: git
      name: self-services-build-templates/self-services-build-templates

# Specifies which variable groups will be used in our Pipeline.
variables:
  - group: ProjectVariables
  - group: ManagedVault
  - group: ManagedVariables
stages:
  - stage: BuildOnAzure
    displayName: Build on Azure
    jobs:
      - template: buildsh/build/stages/jobs/build.yaml@templates
        parameters:
          jobName: Build
          jobDisplayName: Build 🤠🤠🤠🤠
          scripts:
            - './build.sh -p ci next-version'
            - './build.sh -p ci build'
            - './build.sh -p ci publish'
          additionalSteps:
            - publish: target/helm/environments
              artifact: environments
            - publish: target/metadata.yaml
              artifact: metadata
            - publish: target/jreleaser/release/CHANGELOG.md
              artifact: changelog
            - publish: backend/target/jacoco.exec
              artifact: jacoco_unit
          publishDefaultArtifacts: false
  - stage: RunCITests
    dependsOn: BuildOnAzure
    displayName: CI Tests
    jobs:
      - template: buildsh/deploy/stages/jobs/deploy.yaml@templates
        parameters:
          environment: ci
          helmValuesPath: target/helm/environments
          displayName: "Deploy & Run CI Tests"
          additionalSteps:
            - template: buildsh/run-script.yaml@templates
              parameters:
                script: set -e
            - template: buildsh/run-script.yaml@templates
              parameters:
                script: ./build.sh -p ci e2e-tests
                displayName: "Run CI Tests"
            - template: buildsh/run-script.yaml@templates
              parameters:
                script: ./build.sh -p ci undeploy
                displayName: "Undeploy CI"
  #            - publish: "backend/target/jacoco-it.exec"
  #              artifact: "jacoco-it-backend"
  - stage: SonarQube
    dependsOn: RunCITests
    displayName: SonarQube
    jobs:
      - template: buildsh/scripts/stages/jobs/scripts.yaml@templates
        parameters:
          displayName: "SonarQube"
          jobName: "sonarqube"
          preSteps:
            #            - task: DownloadPipelineArtifact@2
            #              inputs:
            #                buildType: 'current'
            #                artifact: "jacoco-it-backend"
            #                path: $(Build.SourcesDirectory)/backend/target
            - task: DownloadPipelineArtifact@2
              inputs:
                buildType: 'current'
                artifact: "jacoco_unit"
                path: $(Build.SourcesDirectory)/backend/target
          scripts:
            - "./build.sh -p ci sonarqube"
  - stage: DeployInTest
    dependsOn: SonarQube
    displayName: Deploy in Test
    variables:
      - group: "test"
    jobs:
      - template: buildsh/deploy/stages/jobs/deploy.yaml@templates
        parameters:
          extraEnv:
            keycloak_admin_password: $(keycloak-admin-password-test)
            mfx_datasource_password: $(mfx_datasource_password)
            person_datasource_password: $(person_datasource_password)
            ibm_mq_password: $(ibm_mq_password)
            rabbitmq_password: $(rabbitmq_password)
            flagsmith_environment_id: $(flagsmith_environment_id)
          environment: test
          helmValuesPath: "helm/environments"
  - stage: DeployInVal
    displayName: Deploy in Val
    dependsOn: DeployInTest
    variables:
      - group: "val"
    jobs:
      - template: buildsh/deploy/stages/jobs/deploy.yaml@templates
        parameters:
          extraEnv:
            keycloak_admin_password: $(keycloak-admin-password-test)
            mfx_datasource_password: $(mfx_datasource_password)
            person_datasource_password: $(person_datasource_password)
            ibm_mq_password: $(ibm_mq_password)
            rabbitmq_password: $(rabbitmq_password)
            flagsmith_environment_id: $(flagsmith_environment_id)
          environment: val
          helmValuesPath: "helm/environments"
  - stage: DeployInProd
    displayName: Deploy in Prod
    dependsOn: DeployInVal
    variables:
      - group: "prod"
    jobs:
      - template: buildsh/deploy/stages/jobs/deploy.yaml@templates
        parameters:
          extraEnv:
            keycloak_admin_password: $(keycloak-admin-password-test)
            mfx_datasource_password: $(mfx_datasource_password)
            person_datasource_password: $(person_datasource_password)
            ibm_mq_password: $(ibm_mq_password)
            rabbitmq_password: $(rabbitmq_password)
            flagsmith_environment_id: $(flagsmith_environment_id)
          environment: prod
          helmValuesPath: "helm/environments"
