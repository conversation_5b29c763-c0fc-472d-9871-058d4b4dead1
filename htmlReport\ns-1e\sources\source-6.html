


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > StringUtil</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.wave</a>
</div>

<h1>Coverage Summary for Class: StringUtil (be.fgov.onerva.wave)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">StringUtil</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/18)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/22)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;/*
&nbsp; * WO facade API
&nbsp; * API to manage tasks (and processes) in a simplified manner
&nbsp; *
&nbsp; * The version of the OpenAPI document: v1
&nbsp; * 
&nbsp; *
&nbsp; * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
&nbsp; * https://openapi-generator.tech
&nbsp; * Do not edit the class manually.
&nbsp; */
&nbsp;
&nbsp;
&nbsp;package be.fgov.onerva.wave;
&nbsp;
&nbsp;import java.util.Collection;
&nbsp;import java.util.Iterator;
&nbsp;
&nbsp;@jakarta.annotation.Generated(value = &quot;org.openapitools.codegen.languages.JavaClientCodegen&quot;, date = &quot;2025-07-16T14:11:32.899250600+02:00[Europe/Brussels]&quot;, comments = &quot;Generator version: 7.12.0&quot;)
<b class="nc">&nbsp;public class StringUtil {</b>
&nbsp;  /**
&nbsp;   * Check if the given array contains the given value (with case-insensitive comparison).
&nbsp;   *
&nbsp;   * @param array The array
&nbsp;   * @param value The value to search
&nbsp;   * @return true if the array contains the value
&nbsp;   */
&nbsp;  public static boolean containsIgnoreCase(String[] array, String value) {
<b class="nc">&nbsp;    for (String str : array) {</b>
<b class="nc">&nbsp;      if (value == null &amp;&amp; str == null) {</b>
<b class="nc">&nbsp;        return true;</b>
&nbsp;      }
<b class="nc">&nbsp;      if (value != null &amp;&amp; value.equalsIgnoreCase(str)) {</b>
<b class="nc">&nbsp;        return true;</b>
&nbsp;      }
&nbsp;    }
<b class="nc">&nbsp;    return false;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Join an array of strings with the given separator.
&nbsp;   * &lt;p&gt;
&nbsp;   * Note: This might be replaced by utility method from commons-lang or guava someday
&nbsp;   * if one of those libraries is added as dependency.
&nbsp;   * &lt;/p&gt;
&nbsp;   *
&nbsp;   * @param array     The array of strings
&nbsp;   * @param separator The separator
&nbsp;   * @return the resulting string
&nbsp;   */
&nbsp;  public static String join(String[] array, String separator) {
<b class="nc">&nbsp;    int len = array.length;</b>
<b class="nc">&nbsp;    if (len == 0) {</b>
<b class="nc">&nbsp;      return &quot;&quot;;</b>
&nbsp;    }
&nbsp;
<b class="nc">&nbsp;    StringBuilder out = new StringBuilder();</b>
<b class="nc">&nbsp;    out.append(array[0]);</b>
<b class="nc">&nbsp;    for (int i = 1; i &lt; len; i++) {</b>
<b class="nc">&nbsp;      out.append(separator).append(array[i]);</b>
&nbsp;    }
<b class="nc">&nbsp;    return out.toString();</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Join a list of strings with the given separator.
&nbsp;   *
&nbsp;   * @param list      The list of strings
&nbsp;   * @param separator The separator
&nbsp;   * @return the resulting string
&nbsp;   */
&nbsp;  public static String join(Collection&lt;String&gt; list, String separator) {
<b class="nc">&nbsp;    Iterator&lt;String&gt; iterator = list.iterator();</b>
<b class="nc">&nbsp;    StringBuilder out = new StringBuilder();</b>
<b class="nc">&nbsp;    if (iterator.hasNext()) {</b>
<b class="nc">&nbsp;      out.append(iterator.next());</b>
&nbsp;    }
<b class="nc">&nbsp;    while (iterator.hasNext()) {</b>
<b class="nc">&nbsp;      out.append(separator).append(iterator.next());</b>
&nbsp;    }
<b class="nc">&nbsp;    return out.toString();</b>
&nbsp;  }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
