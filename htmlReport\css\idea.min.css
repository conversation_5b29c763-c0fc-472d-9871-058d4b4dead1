/*
 * Copyright 2000-2021 JetBrains s.r.o.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/*
Intellij Idea-like styling (c) <PERSON><PERSON> <<EMAIL>>
*/

.hljs {
    color: #000;
    background: #fff;
}

.hljs-subst,
.hljs-title {
    font-weight: normal;
    color: #000;
}

.hljs-comment,
.hljs-quote {
    color: #808080;
    font-style: italic;
}

.hljs-meta {
    color: #808000;
}

.hljs-tag {
    background: #efefef;
}

.hljs-section,
.hljs-name,
.hljs-literal,
.hljs-keyword,
.hljs-selector-tag,
.hljs-type,
.hljs-selector-id,
.hljs-selector-class {
    font-weight: bold;
    color: #000080;
}

.hljs-attribute,
.hljs-number,
.hljs-regexp,
.hljs-link {
    font-weight: bold;
    color: #0000ff;
}

.hljs-number,
.hljs-regexp,
.hljs-link {
    font-weight: normal;
}

.hljs-string {
    color: #008000;
    font-weight: bold;
}

.hljs-symbol,
.hljs-bullet,
.hljs-formula {
    color: #000;
    background: #d0eded;
    font-style: italic;
}

.hljs-doctag {
    text-decoration: underline;
}

.hljs-variable,
.hljs-template-variable {
    color: #660e7a;
}

.hljs-addition {
    background: #baeeba;
}

.hljs-deletion {
    background: #ffc8bd;
}

.hljs-emphasis {
    font-style: italic;
}

.hljs-strong {
    font-weight: bold;
}

.hljs-ln-numbers {
    display: block;
    float: left;
    width: 3em;
    border-right: 1px solid #ccc;
    font-style: normal;
    text-align: right;
    background-color: #eee;
}
