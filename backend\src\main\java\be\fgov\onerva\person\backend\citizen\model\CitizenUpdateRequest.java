package be.fgov.onerva.person.backend.citizen.model;

import be.fgov.onerva.person.backend.request.model.BankUpdateInfo;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import java.time.LocalDate;

@Builder
@Getter
@ToString
public class CitizenUpdateRequest {
    @NotNull
    @Pattern(regexp = "\\d{11}")
    private String niss;
    @NotNull
    @Valid
    private AddressUpdateRequest address;
    @Min(100)
    @Max(999)
    private Integer nationalityCode;

    /**
     * Birth date
     */
    private LocalDate birthDate;

    /**
     * Language code: 1=fr, 2=nl, 3=de as per API specification
     */
    @Min(1)
    @Max(3)
    private Integer languageCode;

    /**
     * Unemployment office number (2 digits) as per mainframe specification
     */
    @Min(1)
    @Max(99)
    private Integer unemploymentOffice;

    /**
     * Bank information for updates - nested object
     */
    @Valid
    private BankUpdateInfo bankInfo;

    /**
     * Union due information for updates - nested object
     */
    @Valid
    private UnionDueUpdateInfo unionDueInfo;

    @NotNull
    private LocalDate validFrom;
    @NotBlank
    private String username;
    private String correlationId;
}
