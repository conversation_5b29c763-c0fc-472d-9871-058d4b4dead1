-- Add new fields for foreign addresses, birthdate, bank information, and union dues
ALTER TABLE PERSON_REQUEST ADD birth_date DATE;
ALTER TABLE PERSON_REQUEST ADD foreign_zip_code VARCHAR(10);
ALTER TABLE PERSON_REQUEST ADD country_code INTEGER;
ALTER TABLE PERSON_REQUEST ADD iban VARCHAR(34);
ALTER TABLE PERSON_REQUEST ADD bic VARCHAR(11);
ALTER TABLE PERSON_REQUEST ADD account_holder VARCHAR(30);
ALTER TABLE PERSON_REQUEST ADD bank_info_value_date DATE;
ALTER TABLE PERSON_REQUEST ADD union_due_value_date DATE;
ALTER TABLE PERSON_REQUEST ADD language_code INTEGER;
ALTER TABLE PERSON_REQUEST ADD unemployment_office INTEGER;

-- Modify existing zip column to support VARCHAR for foreign addresses
-- First, add the new column
ALTER TABLE PERSON_REQUEST ADD zip_code VARCHAR(10);

