package be.fgov.onerva.person.backend.request.model;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import java.time.LocalDate;

/**
 * DTO for bank information updates matching BankUpdateRequest schema from API specification.
 * Contains IBAN, BIC, account holder, payment type, and value date information.
 */
@Builder
@Getter
@ToString
public class BankUpdateInfo {
    
    /**
     * International Bank Account Number (IBAN) - max 34 characters
     * Pattern validates basic IBAN format (2 letters + 2 digits + up to 30 alphanumeric)
     */
    @Pattern(regexp = "^[A-Z]{2}[0-9]{2}[A-Z0-9]{1,30}$", message = "Invalid IBAN format")
    @Size(max = 34, message = "IBAN cannot exceed 34 characters")
    private String iban;
    
    /**
     * Bank Identifier Code (BIC/SWIFT) - max 11 characters
     * Pat<PERSON> validates BIC format (4 letters + 2 letters + 2 alphanumeric + optional 3 alphanumeric)
     */
    @Pattern(regexp = "^[A-Z]{4}[A-Z]{2}[A-Z0-9]{2}([A-Z0-9]{3})?$", message = "Invalid BIC format")
    @Size(max = 11, message = "BIC cannot exceed 11 characters")
    private String bic;
    
    /**
     * Account holder name - max 30 characters
     */
    @Size(max = 30, message = "Account holder name cannot exceed 30 characters")
    private String accountHolder;
    
    /**
     * Payment type for the bank account
     */
    private PaymentType paymentType;
    
    /**
     * Value date for when the bank information becomes effective
     */
    @NotNull(message = "Valid from date is required")
    private LocalDate validFrom;
}
