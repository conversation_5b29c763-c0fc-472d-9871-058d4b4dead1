package be.fgov.onerva.person.backend.citizen.model;

import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import java.time.LocalDate;

/**
 * DTO for union due information updates matching UnionDueUpdateRequest schema from API specification.
 * Contains union due mandate status and value date information.
 */
@Builder
@Getter
@ToString
public class UnionDueUpdateInfo {
    
    /**
     * Union due mandate status - whether the citizen mandates the union to pay union dues
     */
    @NotNull(message = "Union due status is required")
    private Boolean unionDue;
    
    /**
     * Value date for when the union due information becomes effective
     */
    @NotNull(message = "Valid from date is required")
    private LocalDate validFrom;
}
