


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > IbanValidator</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.validation</a>
</div>

<h1>Coverage Summary for Class: IbanValidator (be.fgov.onerva.person.backend.validation)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">IbanValidator</td>
<td class="coverageStat">
  <span class="percent">
    83.3%
  </span>
  <span class="absValue">
    (5/6)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    32.1%
  </span>
  <span class="absValue">
    (9/28)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    51.5%
  </span>
  <span class="absValue">
    (17/33)
  </span>
</td>
</tr>
  <tr>
    <td class="name">IbanValidator$ValidationResult</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/6)
  </span>
</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    50%
  </span>
  <span class="absValue">
    (5/10)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    32.1%
  </span>
  <span class="absValue">
    (9/28)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    43.6%
  </span>
  <span class="absValue">
    (17/39)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.validation;
&nbsp;
&nbsp;import org.springframework.stereotype.Component;
&nbsp;
&nbsp;import java.math.BigInteger;
&nbsp;import java.util.regex.Pattern;
&nbsp;
&nbsp;/**
&nbsp; * IBAN validation utility class implementing MOD-97 checksum validation
&nbsp; * as specified in ISO 13616 standard and mentioned in the API specification.
&nbsp; */
&nbsp;@Component
<b class="fc">&nbsp;public class IbanValidator {</b>
&nbsp;
<b class="fc">&nbsp;    private static final Pattern IBAN_PATTERN = Pattern.compile(&quot;^[A-Z]{2}[0-9]{2}[A-Z0-9]{1,30}$&quot;);</b>
&nbsp;    private static final int MIN_IBAN_LENGTH = 15;
&nbsp;    private static final int MAX_IBAN_LENGTH = 34;
&nbsp;
&nbsp;    /**
&nbsp;     * Validates an IBAN using both format and MOD-97 checksum validation.
&nbsp;     *
&nbsp;     * @param iban the IBAN to validate (can be null or empty)
&nbsp;     * @return true if the IBAN is valid, false otherwise
&nbsp;     */
&nbsp;    public boolean isValid(String iban) {
<b class="pc">&nbsp;        if (iban == null || iban.trim().isEmpty()) {</b>
<b class="nc">&nbsp;            return false;</b>
&nbsp;        }
&nbsp;
&nbsp;        // Remove spaces and convert to uppercase
<b class="fc">&nbsp;        String cleanIban = iban.replaceAll(&quot;\\s&quot;, &quot;&quot;).toUpperCase();</b>
&nbsp;
&nbsp;        // Check basic format
<b class="pc">&nbsp;        if (!isValidFormat(cleanIban)) {</b>
<b class="nc">&nbsp;            return false;</b>
&nbsp;        }
&nbsp;
&nbsp;        // Check MOD-97 checksum
<b class="fc">&nbsp;        return isValidChecksum(cleanIban);</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Validates the basic format of an IBAN.
&nbsp;     *
&nbsp;     * @param iban the cleaned IBAN string
&nbsp;     * @return true if format is valid, false otherwise
&nbsp;     */
&nbsp;    private boolean isValidFormat(String iban) {
&nbsp;        // Check length
<b class="pc">&nbsp;        if (iban.length() &lt; MIN_IBAN_LENGTH || iban.length() &gt; MAX_IBAN_LENGTH) {</b>
<b class="nc">&nbsp;            return false;</b>
&nbsp;        }
&nbsp;
&nbsp;        // Check pattern: 2 letters + 2 digits + up to 30 alphanumeric
<b class="fc">&nbsp;        return IBAN_PATTERN.matcher(iban).matches();</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Validates the MOD-97 checksum of an IBAN according to ISO 13616.
&nbsp;     * 
&nbsp;     * Algorithm:
&nbsp;     * 1. Move the first 4 characters to the end
&nbsp;     * 2. Replace letters with numbers (A=10, B=11, ..., Z=35)
&nbsp;     * 3. Calculate mod 97 of the resulting number
&nbsp;     * 4. The result should be 1 for a valid IBAN
&nbsp;     *
&nbsp;     * @param iban the cleaned IBAN string
&nbsp;     * @return true if checksum is valid, false otherwise
&nbsp;     */
&nbsp;    private boolean isValidChecksum(String iban) {
&nbsp;        try {
&nbsp;            // Step 1: Move first 4 characters to the end
<b class="fc">&nbsp;            String rearranged = iban.substring(4) + iban.substring(0, 4);</b>
&nbsp;
&nbsp;            // Step 2: Replace letters with numbers
<b class="fc">&nbsp;            StringBuilder numericString = new StringBuilder();</b>
<b class="fc">&nbsp;            for (char c : rearranged.toCharArray()) {</b>
<b class="fc">&nbsp;                if (Character.isLetter(c)) {</b>
&nbsp;                    // A=10, B=11, ..., Z=35
<b class="fc">&nbsp;                    numericString.append(c - &#39;A&#39; + 10);</b>
&nbsp;                } else {
<b class="fc">&nbsp;                    numericString.append(c);</b>
&nbsp;                }
&nbsp;            }
&nbsp;
&nbsp;            // Step 3: Calculate mod 97
<b class="fc">&nbsp;            BigInteger number = new BigInteger(numericString.toString());</b>
<b class="fc">&nbsp;            BigInteger remainder = number.remainder(BigInteger.valueOf(97));</b>
&nbsp;
&nbsp;            // Step 4: Check if remainder is 1
<b class="fc">&nbsp;            return remainder.equals(BigInteger.ONE);</b>
&nbsp;
&nbsp;        } catch (NumberFormatException e) {
<b class="nc">&nbsp;            return false;</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Validates an IBAN and provides detailed error information.
&nbsp;     *
&nbsp;     * @param iban the IBAN to validate
&nbsp;     * @return ValidationResult containing validation status and error details
&nbsp;     */
&nbsp;    public ValidationResult validateWithDetails(String iban) {
<b class="nc">&nbsp;        if (iban == null || iban.trim().isEmpty()) {</b>
<b class="nc">&nbsp;            return new ValidationResult(false, &quot;IBAN cannot be null or empty&quot;);</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        String cleanIban = iban.replaceAll(&quot;\\s&quot;, &quot;&quot;).toUpperCase();</b>
&nbsp;
<b class="nc">&nbsp;        if (!isValidFormat(cleanIban)) {</b>
<b class="nc">&nbsp;            if (cleanIban.length() &lt; MIN_IBAN_LENGTH || cleanIban.length() &gt; MAX_IBAN_LENGTH) {</b>
<b class="nc">&nbsp;                return new ValidationResult(false, </b>
<b class="nc">&nbsp;                    String.format(&quot;IBAN length must be between %d and %d characters&quot;, MIN_IBAN_LENGTH, MAX_IBAN_LENGTH));</b>
&nbsp;            }
<b class="nc">&nbsp;            if (!IBAN_PATTERN.matcher(cleanIban).matches()) {</b>
<b class="nc">&nbsp;                return new ValidationResult(false, &quot;IBAN format is invalid. Expected: 2 letters + 2 digits + up to 30 alphanumeric characters&quot;);</b>
&nbsp;            }
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        if (!isValidChecksum(cleanIban)) {</b>
<b class="nc">&nbsp;            return new ValidationResult(false, &quot;IBAN checksum validation failed (MOD-97)&quot;);</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        return new ValidationResult(true, &quot;IBAN is valid&quot;);</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Result of IBAN validation with detailed information.
&nbsp;     */
&nbsp;    public static class ValidationResult {
&nbsp;        private final boolean valid;
&nbsp;        private final String message;
&nbsp;
<b class="nc">&nbsp;        public ValidationResult(boolean valid, String message) {</b>
<b class="nc">&nbsp;            this.valid = valid;</b>
<b class="nc">&nbsp;            this.message = message;</b>
&nbsp;        }
&nbsp;
&nbsp;        public boolean isValid() {
<b class="nc">&nbsp;            return valid;</b>
&nbsp;        }
&nbsp;
&nbsp;        public String getMessage() {
<b class="nc">&nbsp;            return message;</b>
&nbsp;        }
&nbsp;
&nbsp;        @Override
&nbsp;        public String toString() {
<b class="nc">&nbsp;            return String.format(&quot;ValidationResult{valid=%s, message=&#39;%s&#39;}&quot;, valid, message);</b>
&nbsp;        }
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
