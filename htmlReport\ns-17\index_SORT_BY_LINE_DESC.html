
<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > be.fgov.onerva.person.backend.request.mapper</title>
  <style type="text/css">
    @import "../css/coverage.css";
    @import "../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../js/highlight.min.js"></script>
  <script type="text/javascript" src="../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">

<div class="breadCrumbs">
Current scope:     <a href="../index_SORT_BY_LINE_DESC.html">all classes</a>
    <span class="separator">|</span>
be.fgov.onerva.person.backend.request.mapper</div>

<h1>Coverage Summary for Package: be.fgov.onerva.person.backend.request.mapper</h1>
<table class="coverageStats">
  <tr>
    <th class="name">Package</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
  </tr>
  <tr>
    <td class="name">be.fgov.onerva.person.backend.request.mapper</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (5/5)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    86.7%
  </span>
  <span class="absValue">
    (13/15)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    49%
  </span>
  <span class="absValue">
    (25/51)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    73.4%
  </span>
  <span class="absValue">
    (91/124)
  </span>
</td>
  </tr>
</table>

<br/>
<br/>

<table class="coverageStats">
<tr>
  <th class="name  
">
<a href="index.html">Class</a>  </th>
<th class="coverageStat 
">
  <a href="index_SORT_BY_CLASS.html">Class, %</a>
</th>
<th class="coverageStat 
">
  <a href="index_SORT_BY_METHOD.html">Method, %</a>
</th>
<th class="coverageStat 
">
  <a href="index_SORT_BY_BLOCK.html">Branch, %</a>
</th>
<th class="coverageStat sortedDesc
">
  <a href="index_SORT_BY_LINE.html">Line, %</a>
</th>
</tr>
  <tr>
    <td class="name"><a href="sources/source-3.html">PersonRequestMapper</a></td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (2/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (2/2)
  </span>
</td>
  </tr>
  <tr>
    <td class="name"><a href="sources/source-1.html">PersonEventMapper</a></td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (4/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (5/5)
  </span>
</td>
  </tr>
  <tr>
    <td class="name"><a href="sources/source-4.html">PersonRequestMapperImpl</a></td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (2/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (8/8)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    71.4%
  </span>
  <span class="absValue">
    (15/21)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    88.2%
  </span>
  <span class="absValue">
    (60/68)
  </span>
</td>
  </tr>
  <tr>
    <td class="name"><a href="sources/source-2.html">PersonEventMapperImpl</a></td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    60%
  </span>
  <span class="absValue">
    (3/5)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    16.7%
  </span>
  <span class="absValue">
    (4/24)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    49%
  </span>
  <span class="absValue">
    (24/49)
  </span>
</td>
  </tr>
</table>

</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
