


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenController</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.citizen</a>
</div>

<h1>Coverage Summary for Class: CitizenController (be.fgov.onerva.person.backend.citizen)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">CitizenController</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (8/8)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    83.3%
  </span>
  <span class="absValue">
    (5/6)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (23/23)
  </span>
</td>
</tr>
  <tr>
    <td class="name">CitizenController$$SpringCGLIB$$0</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (8/8)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    83.3%
  </span>
  <span class="absValue">
    (5/6)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (23/23)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.citizen;
&nbsp;
&nbsp;
&nbsp;import backend.api.CitizenApi;
&nbsp;import backend.rest.model.CitizenCreationRequestDTO;
&nbsp;import backend.rest.model.CitizenDTO;
&nbsp;import backend.rest.model.CitizenPageDTO;
&nbsp;import backend.rest.model.CitizenUpdateRequestDTO;
&nbsp;import be.fgov.onerva.person.backend.citizen.mapper.CitizenMapper;
&nbsp;import be.fgov.onerva.person.backend.citizen.service.CitizenService;
&nbsp;import be.fgov.onerva.person.backend.request.PersonRequestController;
&nbsp;import be.fgov.onerva.person.backend.request.model.PersonRequest;
&nbsp;import lombok.RequiredArgsConstructor;
&nbsp;import lombok.extern.slf4j.Slf4j;
&nbsp;import org.mapstruct.factory.Mappers;
&nbsp;import org.springframework.data.domain.PageRequest;
&nbsp;import org.springframework.data.domain.Sort;
&nbsp;import org.springframework.http.HttpStatusCode;
&nbsp;import org.springframework.http.ResponseEntity;
&nbsp;import org.springframework.web.bind.annotation.CrossOrigin;
&nbsp;import org.springframework.web.bind.annotation.RequestMapping;
&nbsp;import org.springframework.web.bind.annotation.RestController;
&nbsp;import org.springframework.web.server.ResponseStatusException;
&nbsp;import org.springframework.web.servlet.mvc.method.annotation.MvcUriComponentsBuilder;
&nbsp;
&nbsp;import java.net.URI;
&nbsp;import java.util.regex.Pattern;
&nbsp;
<b class="fc">&nbsp;@Slf4j</b>
&nbsp;@RestController
&nbsp;@CrossOrigin(origins = &quot;*&quot;, maxAge = 3600)
&nbsp;@RequestMapping(&quot;/api&quot;)
&nbsp;@RequiredArgsConstructor
&nbsp;public class CitizenController implements CitizenApi {
&nbsp;
<b class="fc">&nbsp;    private static final Pattern NUMERIC = Pattern.compile(&quot;\\d+&quot;);</b>
&nbsp;
&nbsp;    private final CitizenService citizenService;
&nbsp;    private CitizenMapper citizenMapper = Mappers.getMapper(CitizenMapper.class);
&nbsp;
&nbsp;    @Override
&nbsp;    public ResponseEntity&lt;CitizenDTO&gt; getByNiss(String niss) {
<b class="fc">&nbsp;        isNumeric(niss);</b>
<b class="fc">&nbsp;        var citizenEntity = citizenService.getByNiss(niss);</b>
<b class="fc">&nbsp;        var citizenDto = citizenMapper.map(citizenEntity);</b>
<b class="fc">&nbsp;        return ResponseEntity.ok(citizenDto);</b>
&nbsp;    }
&nbsp;
&nbsp;    void isNumeric (String input) {
<b class="pc">&nbsp;        if (input == null || !NUMERIC.matcher(input).matches()) {</b>
<b class="fc">&nbsp;            throw new ResponseStatusException(HttpStatusCode.valueOf(400), &quot;Inss not numeric: &quot; + input);</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    @Override
&nbsp;    public ResponseEntity&lt;CitizenDTO&gt; getByNumbox(Integer numbox) {
<b class="fc">&nbsp;        return ResponseEntity.ok(</b>
<b class="fc">&nbsp;                citizenMapper.map(</b>
<b class="fc">&nbsp;                        citizenService.getByNumbox(numbox)</b>
&nbsp;                )
&nbsp;        );
&nbsp;    }
&nbsp;
&nbsp;    @Override
&nbsp;    public ResponseEntity&lt;CitizenPageDTO&gt; searchCitizen(String query, Integer pageNumber, Integer pageSize) {
<b class="fc">&nbsp;        var citizenPage = citizenService.searchCitizenByQuery(query, PageRequest.of(pageNumber, pageSize, Sort.by(Sort.Direction.ASC, &quot;numBr&quot;, &quot;fullName&quot;)));</b>
<b class="fc">&nbsp;        var citizenPageDto = citizenMapper.mapPageToDto(citizenPage);</b>
<b class="fc">&nbsp;        return ResponseEntity.ok(citizenPageDto);</b>
&nbsp;    }
&nbsp;
&nbsp;    @Override
&nbsp;    public ResponseEntity&lt;Void&gt; createCitizen(String businessDomain, Boolean allowance, CitizenCreationRequestDTO citizenDTO) {
<b class="fc">&nbsp;        if (citizenDTO.getFallbackUrl() != null) {</b>
<b class="fc">&nbsp;            throw new IllegalArgumentException(&quot;Fallback URL is no longer supported! Use messaging for response.&quot;);</b>
&nbsp;        }
<b class="fc">&nbsp;        var creationRequest = citizenMapper.map(citizenDTO, businessDomain, allowance);</b>
<b class="fc">&nbsp;        PersonRequest personRequest = citizenService.createCitizen(creationRequest);</b>
&nbsp;
<b class="fc">&nbsp;        return ResponseEntity.created(getLink(personRequest)).build();</b>
&nbsp;    }
&nbsp;
&nbsp;    @Override
&nbsp;    public ResponseEntity&lt;Void&gt; updateCitizen(String niss, String username, CitizenUpdateRequestDTO citizenUpdateRequestDTO) {
<b class="fc">&nbsp;        var updateRequest = citizenMapper.map(citizenUpdateRequestDTO, niss, username);</b>
<b class="fc">&nbsp;        PersonRequest personRequest = citizenService.updateCitizen(updateRequest);</b>
&nbsp;
<b class="fc">&nbsp;        return ResponseEntity.noContent().location(getLink(personRequest)).build();</b>
&nbsp;    }
&nbsp;
&nbsp;    URI getLink(PersonRequest personRequest) {
<b class="fc">&nbsp;        return MvcUriComponentsBuilder.fromController(PersonRequestController.class).path(&quot;/citizen/requests/{id}&quot;).build(personRequest.getId());</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
