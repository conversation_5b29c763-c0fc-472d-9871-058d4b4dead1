


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > Address</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.request.model</a>
</div>

<h1>Coverage Summary for Class: Address (be.fgov.onerva.person.backend.request.model)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
</tr>
<tr>
  <td class="name">Address</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.request.model;
&nbsp;
&nbsp;import jakarta.persistence.Column;
&nbsp;import jakarta.persistence.Embeddable;
&nbsp;import jakarta.validation.constraints.Max;
&nbsp;import jakarta.validation.constraints.Min;
&nbsp;import jakarta.validation.constraints.NotBlank;
&nbsp;import lombok.*;
&nbsp;
&nbsp;@Embeddable
&nbsp;@Getter
&nbsp;@Builder
&nbsp;@ToString
&nbsp;@NoArgsConstructor(access = AccessLevel.PACKAGE)
&nbsp;@AllArgsConstructor(access = AccessLevel.PACKAGE)
&nbsp;public class Address {
&nbsp;  @NotBlank
&nbsp;  private String street;
&nbsp;
&nbsp;  private String number;
&nbsp;
&nbsp;  private String box;
&nbsp;  @Column(length = 10)
&nbsp;  private String zip;
&nbsp;  @NotBlank
&nbsp;  @Column(length = 35)
&nbsp;  private String city;
&nbsp;  @Min(1)
&nbsp;  @Max(999)
&nbsp;  private Integer countryCode;
&nbsp;  @Column(length = 10)
&nbsp;  private String foreignZipCode;
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
