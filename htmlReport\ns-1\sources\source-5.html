


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenRequestsApi</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">backend.api</a>
</div>

<h1>Coverage Summary for Class: CitizenRequestsApi (backend.api)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">CitizenRequestsApi</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/5)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/8)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/13)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;/**
&nbsp; * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.12.0).
&nbsp; * https://openapi-generator.tech
&nbsp; * Do not edit the class manually.
&nbsp; */
&nbsp;package backend.api;
&nbsp;
&nbsp;import backend.rest.model.CitizenRequestDTO;
&nbsp;import backend.rest.model.CitizenRequestPageDTO;
&nbsp;import org.springframework.data.domain.Pageable;
&nbsp;import org.springdoc.core.annotations.ParameterObject;
&nbsp;import io.swagger.v3.oas.annotations.ExternalDocumentation;
&nbsp;import io.swagger.v3.oas.annotations.Operation;
&nbsp;import io.swagger.v3.oas.annotations.Parameter;
&nbsp;import io.swagger.v3.oas.annotations.Parameters;
&nbsp;import io.swagger.v3.oas.annotations.media.ArraySchema;
&nbsp;import io.swagger.v3.oas.annotations.media.Content;
&nbsp;import io.swagger.v3.oas.annotations.media.Schema;
&nbsp;import io.swagger.v3.oas.annotations.responses.ApiResponse;
&nbsp;import io.swagger.v3.oas.annotations.security.SecurityRequirement;
&nbsp;import io.swagger.v3.oas.annotations.tags.Tag;
&nbsp;import io.swagger.v3.oas.annotations.enums.ParameterIn;
&nbsp;import org.springframework.http.HttpStatus;
&nbsp;import org.springframework.http.MediaType;
&nbsp;import org.springframework.http.ResponseEntity;
&nbsp;import org.springframework.validation.annotation.Validated;
&nbsp;import org.springframework.web.bind.annotation.*;
&nbsp;import org.springframework.web.context.request.NativeWebRequest;
&nbsp;import org.springframework.web.multipart.MultipartFile;
&nbsp;
&nbsp;import jakarta.validation.Valid;
&nbsp;import jakarta.validation.constraints.*;
&nbsp;import java.util.List;
&nbsp;import java.util.Map;
&nbsp;import java.util.Optional;
&nbsp;import jakarta.annotation.Generated;
&nbsp;
&nbsp;@Generated(value = &quot;org.openapitools.codegen.languages.SpringCodegen&quot;, date = &quot;2025-07-16T14:11:30.627767400+02:00[Europe/Brussels]&quot;, comments = &quot;Generator version: 7.12.0&quot;)
&nbsp;@Validated
&nbsp;@Tag(name = &quot;CitizenRequests&quot;, description = &quot;the CitizenRequests API&quot;)
&nbsp;public interface CitizenRequestsApi {
&nbsp;
&nbsp;    default Optional&lt;NativeWebRequest&gt; getRequest() {
<b class="nc">&nbsp;        return Optional.empty();</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * GET /citizen/requests/{id}
&nbsp;     * Get a citizen request by id
&nbsp;     *
&nbsp;     * @param id  (required)
&nbsp;     * @return CitizenRequest (status code 200)
&nbsp;     */
&nbsp;    @Operation(
&nbsp;        operationId = &quot;getById&quot;,
&nbsp;        description = &quot;Get a citizen request by id&quot;,
&nbsp;        tags = { &quot;CitizenRequests&quot; },
&nbsp;        responses = {
&nbsp;            @ApiResponse(responseCode = &quot;200&quot;, description = &quot;CitizenRequest&quot;, content = {
&nbsp;                @Content(mediaType = &quot;application/json&quot;, schema = @Schema(implementation = CitizenRequestDTO.class))
&nbsp;            })
&nbsp;        }
&nbsp;    )
&nbsp;    @RequestMapping(
&nbsp;        method = RequestMethod.GET,
&nbsp;        value = &quot;/citizen/requests/{id}&quot;,
&nbsp;        produces = { &quot;application/json&quot; }
&nbsp;    )
&nbsp;    
&nbsp;    default ResponseEntity&lt;CitizenRequestDTO&gt; getById(
&nbsp;        @Parameter(name = &quot;id&quot;, description = &quot;&quot;, required = true, in = ParameterIn.PATH) @PathVariable(&quot;id&quot;) Long id
&nbsp;    ) {
<b class="nc">&nbsp;        getRequest().ifPresent(request -&gt; {</b>
<b class="nc">&nbsp;            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader(&quot;Accept&quot;))) {</b>
<b class="nc">&nbsp;                if (mediaType.isCompatibleWith(MediaType.valueOf(&quot;application/json&quot;))) {</b>
<b class="nc">&nbsp;                    String exampleString = &quot;{ \&quot;firstname\&quot; : \&quot;firstname\&quot;, \&quot;address\&quot; : { \&quot;zip\&quot; : \&quot;1000\&quot;, \&quot;number\&quot; : \&quot;number\&quot;, \&quot;city\&quot; : \&quot;city\&quot;, \&quot;street\&quot; : \&quot;street\&quot;, \&quot;countryCode\&quot; : 1, \&quot;box\&quot; : \&quot;box\&quot; }, \&quot;created\&quot; : \&quot;2000-01-23T04:56:07.000+00:00\&quot;, \&quot;retryCount\&quot; : 2, \&quot;niss\&quot; : \&quot;niss\&quot;, \&quot;valueDate\&quot; : \&quot;2000-01-23\&quot;, \&quot;error\&quot; : \&quot;error\&quot;, \&quot;type\&quot; : \&quot;CREATE\&quot;, \&quot;sent\&quot; : true, \&quot;lastname\&quot; : \&quot;lastname\&quot;, \&quot;paymentType\&quot; : \&quot;BANK_TRANSFER\&quot;, \&quot;returnCode\&quot; : 7, \&quot;nationalityCode\&quot; : 9, \&quot;unionDue\&quot; : true, \&quot;correlationId\&quot; : \&quot;correlationId\&quot;, \&quot;id\&quot; : 5.***************, \&quot;updated\&quot; : \&quot;2000-01-23T04:56:07.000+00:00\&quot;, \&quot;username\&quot; : \&quot;username\&quot; }&quot;;</b>
<b class="nc">&nbsp;                    ApiUtil.setExampleResponse(request, &quot;application/json&quot;, exampleString);</b>
&nbsp;                    break;
&nbsp;                }
&nbsp;            }
&nbsp;        });
<b class="nc">&nbsp;        return new ResponseEntity&lt;&gt;(HttpStatus.NOT_IMPLEMENTED);</b>
&nbsp;
&nbsp;    }
&nbsp;
&nbsp;
&nbsp;    /**
&nbsp;     * GET /citizen/requests
&nbsp;     * Search citizens requests based on a query
&nbsp;     *
&nbsp;     * @param query Uses RSQL syntax to search through citizens requests (See: https://molgenis.gitbooks.io/molgenis/content/guide-rsql.html\&amp;quot;) (optional)
&nbsp;     * @param pageNumber  (optional, default to 0)
&nbsp;     * @param pageSize  (optional, default to 10)
&nbsp;     * @param sort The format &amp;#39;property,property(,ASC|DESC)(,IgnoreCase)&amp;#39;.  Use multiple sort parameters if you want to switch direction or case sensitivity. Example: &amp;#39;sort&amp;#x3D;firstname&amp;amp;sort&amp;#x3D;lastname,asc&amp;amp;sort&amp;#x3D;city,ignorecase&amp;#39; (optional)
&nbsp;     * @return CitizenRequests (status code 200)
&nbsp;     */
&nbsp;    @Operation(
&nbsp;        operationId = &quot;searchCitizenRequests&quot;,
&nbsp;        description = &quot;Search citizens requests based on a query&quot;,
&nbsp;        tags = { &quot;CitizenRequests&quot; },
&nbsp;        responses = {
&nbsp;            @ApiResponse(responseCode = &quot;200&quot;, description = &quot;CitizenRequests&quot;, content = {
&nbsp;                @Content(mediaType = &quot;application/json&quot;, schema = @Schema(implementation = CitizenRequestPageDTO.class))
&nbsp;            })
&nbsp;        }
&nbsp;    )
&nbsp;    @RequestMapping(
&nbsp;        method = RequestMethod.GET,
&nbsp;        value = &quot;/citizen/requests&quot;,
&nbsp;        produces = { &quot;application/json&quot; }
&nbsp;    )
&nbsp;    
&nbsp;    default ResponseEntity&lt;CitizenRequestPageDTO&gt; searchCitizenRequests(
&nbsp;        @Parameter(name = &quot;query&quot;, description = &quot;Uses RSQL syntax to search through citizens requests (See: https://molgenis.gitbooks.io/molgenis/content/guide-rsql.html\&quot;)&quot;, in = ParameterIn.QUERY) @Valid @RequestParam(value = &quot;query&quot;, required = false) String query,
&nbsp;        @Min(0) @Parameter(name = &quot;pageNumber&quot;, description = &quot;&quot;, in = ParameterIn.QUERY) @Valid @RequestParam(value = &quot;pageNumber&quot;, required = false, defaultValue = &quot;0&quot;) Integer pageNumber,
&nbsp;        @Parameter(name = &quot;pageSize&quot;, description = &quot;&quot;, in = ParameterIn.QUERY) @Valid @RequestParam(value = &quot;pageSize&quot;, required = false, defaultValue = &quot;10&quot;) Integer pageSize,
&nbsp;        @Parameter(name = &quot;sort&quot;, description = &quot;The format &#39;property,property(,ASC|DESC)(,IgnoreCase)&#39;.  Use multiple sort parameters if you want to switch direction or case sensitivity. Example: &#39;sort=firstname&amp;sort=lastname,asc&amp;sort=city,ignorecase&#39;&quot;, in = ParameterIn.QUERY) @Valid @RequestParam(value = &quot;sort&quot;, required = false) String sort,
&nbsp;        @ParameterObject final Pageable pageable
&nbsp;    ) {
<b class="nc">&nbsp;        getRequest().ifPresent(request -&gt; {</b>
<b class="nc">&nbsp;            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader(&quot;Accept&quot;))) {</b>
<b class="nc">&nbsp;                if (mediaType.isCompatibleWith(MediaType.valueOf(&quot;application/json&quot;))) {</b>
<b class="nc">&nbsp;                    String exampleString = &quot;{ \&quot;isFirst\&quot; : true, \&quot;pageNumber\&quot; : 0, \&quot;isLast\&quot; : true, \&quot;totalPage\&quot; : 1, \&quot;pageSize\&quot; : 6, \&quot;content\&quot; : [ { \&quot;firstname\&quot; : \&quot;firstname\&quot;, \&quot;address\&quot; : { \&quot;zip\&quot; : \&quot;1000\&quot;, \&quot;number\&quot; : \&quot;number\&quot;, \&quot;city\&quot; : \&quot;city\&quot;, \&quot;street\&quot; : \&quot;street\&quot;, \&quot;countryCode\&quot; : 1, \&quot;box\&quot; : \&quot;box\&quot; }, \&quot;created\&quot; : \&quot;2000-01-23T04:56:07.000+00:00\&quot;, \&quot;retryCount\&quot; : 2, \&quot;niss\&quot; : \&quot;niss\&quot;, \&quot;valueDate\&quot; : \&quot;2000-01-23\&quot;, \&quot;error\&quot; : \&quot;error\&quot;, \&quot;type\&quot; : \&quot;CREATE\&quot;, \&quot;sent\&quot; : true, \&quot;lastname\&quot; : \&quot;lastname\&quot;, \&quot;paymentType\&quot; : \&quot;BANK_TRANSFER\&quot;, \&quot;returnCode\&quot; : 7, \&quot;nationalityCode\&quot; : 9, \&quot;unionDue\&quot; : true, \&quot;correlationId\&quot; : \&quot;correlationId\&quot;, \&quot;id\&quot; : 5.***************, \&quot;updated\&quot; : \&quot;2000-01-23T04:56:07.000+00:00\&quot;, \&quot;username\&quot; : \&quot;username\&quot; }, { \&quot;firstname\&quot; : \&quot;firstname\&quot;, \&quot;address\&quot; : { \&quot;zip\&quot; : \&quot;1000\&quot;, \&quot;number\&quot; : \&quot;number\&quot;, \&quot;city\&quot; : \&quot;city\&quot;, \&quot;street\&quot; : \&quot;street\&quot;, \&quot;countryCode\&quot; : 1, \&quot;box\&quot; : \&quot;box\&quot; }, \&quot;created\&quot; : \&quot;2000-01-23T04:56:07.000+00:00\&quot;, \&quot;retryCount\&quot; : 2, \&quot;niss\&quot; : \&quot;niss\&quot;, \&quot;valueDate\&quot; : \&quot;2000-01-23\&quot;, \&quot;error\&quot; : \&quot;error\&quot;, \&quot;type\&quot; : \&quot;CREATE\&quot;, \&quot;sent\&quot; : true, \&quot;lastname\&quot; : \&quot;lastname\&quot;, \&quot;paymentType\&quot; : \&quot;BANK_TRANSFER\&quot;, \&quot;returnCode\&quot; : 7, \&quot;nationalityCode\&quot; : 9, \&quot;unionDue\&quot; : true, \&quot;correlationId\&quot; : \&quot;correlationId\&quot;, \&quot;id\&quot; : 5.***************, \&quot;updated\&quot; : \&quot;2000-01-23T04:56:07.000+00:00\&quot;, \&quot;username\&quot; : \&quot;username\&quot; } ], \&quot;totalElements\&quot; : 5 }&quot;;</b>
<b class="nc">&nbsp;                    ApiUtil.setExampleResponse(request, &quot;application/json&quot;, exampleString);</b>
&nbsp;                    break;
&nbsp;                }
&nbsp;            }
&nbsp;        });
<b class="nc">&nbsp;        return new ResponseEntity&lt;&gt;(HttpStatus.NOT_IMPLEMENTED);</b>
&nbsp;
&nbsp;    }
&nbsp;
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
