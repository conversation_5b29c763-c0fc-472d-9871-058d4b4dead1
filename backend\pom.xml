<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>be.fgov.onerva.person</groupId>
        <artifactId>parent</artifactId>
        <version>1.0</version>
    </parent>

    <artifactId>backend</artifactId>
    <name>Person backend</name>
    <description>Service returning citizen informations</description>

    <properties>
        <mapstruct.version>1.6.3</mapstruct.version>
    </properties>

    <dependencies>

        <!--  PROVIDED  -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <!--  COMPILE  -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-logging</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-artemis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ibm.mq</groupId>
            <artifactId>mq-jms-spring-boot-starter</artifactId>
            <version>3.1.5</version>
        </dependency>
        <dependency>
            <groupId>org.openapitools</groupId>
            <artifactId>jackson-databind-nullable</artifactId>
            <version>0.2.6</version>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>2.8.6</version>
        </dependency>
        <dependency>
            <groupId>be.fgov.onerva</groupId>
            <artifactId>common</artifactId>
            <version>4.12.0</version>
        </dependency>

        <dependency>
            <groupId>com.flagsmith</groupId>
            <artifactId>flagsmith-java-client</artifactId>
            <version>7.4.3</version>
        </dependency>
        <dependency>
            <groupId>io.github.perplexhub</groupId>
            <artifactId>rsql-jpa-spring-boot-starter</artifactId>
            <version>6.0.26</version>
        </dependency>

        <!--  RUNTIME  -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>be.fgov.onerva.observability</groupId>
            <artifactId>observability-spring-boot-starter</artifactId>
            <version>5.1</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-layout-template-json</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-sqlserver</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.activemq</groupId>
            <artifactId>artemis-jakarta-server</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!--  TEST  -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.skyscreamer</groupId>
                    <artifactId>jsonassert</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>au.com.dius.pact.provider</groupId>
            <artifactId>junit5spring</artifactId>
            <version>4.6.17</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>mssqlserver</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.github.microcks</groupId>
            <artifactId>microcks-testcontainers</artifactId>
            <version>0.1.4</version>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>be.fgov.onerva.microcks</groupId>
            <artifactId>microcksutils</artifactId>
            <version>2.0</version>
            <scope>test</scope>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>build-info</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <version>3.4.2</version> <!-- newer versions give problems sometimes -->
                <configuration>
                    <from>
                        <image>docker-release.onemrva.priv/onemrva/eclipse-temurin:21-jdk-3.0.0</image>
                    </from>
                    <to>
                        <image>docker-alpha.onemrva.priv/person-backend</image>
                        <auth>
                            <username>${env.bamboo_nexus_user}</username>
                            <password>${env.bamboo_nexus_secret}</password>
                        </auth>
                    </to>
                    <container>
                        <creationTime>USE_CURRENT_TIMESTAMP</creationTime>
                        <ports>
                            <port>8080</port>
                        </ports>
                        <jvmFlags>
                            <jvmFlag>-XX:InitialRAMPercentage=50.0</jvmFlag>
                            <jvmFlag>-XX:MaxRAMPercentage=50.0</jvmFlag>
                        </jvmFlags>
                    </container>
                    <allowInsecureRegistries>true</allowInsecureRegistries>
                </configuration>
            </plugin>
            <plugin>
                <groupId>io.apicurio</groupId>
                <artifactId>apicurio-registry-maven-plugin</artifactId>
                <version>2.4.2.Final</version>
                <executions>
                    <execution>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>download</goal>
                        </goals>
                        <configuration>
                            <registryUrl>https://api-registry.test.paas.onemrva.priv/apis/registry/v2</registryUrl>
                            <artifacts>
                                <artifact>
                                    <groupId>be.fgov.onerva.woconfigurator</groupId>
                                    <artifactId>wo-facade-api</artifactId>
                                    <file>${project.basedir}/target/api/wave-api.json</file>
                                    <overwrite>true</overwrite>
                                </artifact>
                            </artifacts>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.openapitools</groupId>
                <artifactId>openapi-generator-maven-plugin</artifactId>
                <version>7.12.0</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <generatorName>spring</generatorName>
                            <configOptions>
                                <useSpringBoot3>true</useSpringBoot3>
                                <basePackage>backend</basePackage>
                                <singleContentTypes>false</singleContentTypes>
                                <interfaceOnly>true</interfaceOnly>
                                <useTags>true</useTags>
                                <library>spring-boot</library>
                                <openApiNullable>false</openApiNullable>
                                <performBeanValidation>true</performBeanValidation>
                            </configOptions>
                            <modelPackage>backend.rest.model</modelPackage>
                            <invokerPackage>backend.invoker</invokerPackage>
                            <addCompileSourceRoot>true</addCompileSourceRoot>
                            <apiPackage>backend.api</apiPackage>
                            <inputSpec>${project.basedir}/../api/person_api.yaml</inputSpec>
                            <modelNameSuffix>DTO</modelNameSuffix>
                            <artifactId>backend</artifactId>
                            <packageName>backend</packageName>
                            <typeMappings>
                                <mapping>number+int64=long</mapping>
                            </typeMappings>
                        </configuration>
                    </execution>
                    <execution>
                        <id>wo-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <generatorName>java</generatorName>
                            <inputSpec>
                                ${project.basedir}/target/api/wave-api.json
                            </inputSpec>
                            <apisToGenerate>User</apisToGenerate>
                            <modelsToGenerate>User,UserCriteria</modelsToGenerate>
                            <apiPackage>be.fgov.onerva.wave.api</apiPackage>
                            <modelPackage>be.fgov.onerva.wave.model</modelPackage>
                            <invokerPackage>be.fgov.onerva.wave</invokerPackage>
                            <addCompileSourceRoot>true</addCompileSourceRoot>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <configOptions>
                                <library>restclient</library>
                                <useJakartaEe>true</useJakartaEe>
                                <openApiNullable>false</openApiNullable>
                            </configOptions>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.github.eirslett</groupId>
                <artifactId>frontend-maven-plugin</artifactId>
                <version>1.15.1</version>
                <configuration>
                    <nodeVersion>v20.10.0</nodeVersion>
                    <npmVersion>10.2.3</npmVersion>
                    <environmentVariables>
                        <NODE_EXTRA_CA_CERTS>${project.basedir}/rva.crt</NODE_EXTRA_CA_CERTS>
                    </environmentVariables>
                </configuration>
                <executions>
                    <execution>
                        <phase>generate-sources</phase>
                        <id>npm install</id>
                        <goals>
                            <goal>install-node-and-npm</goal>
                            <goal>npm</goal>
                        </goals>
                        <configuration>
                            <arguments>install</arguments>
                        </configuration>
                    </execution>
                    <execution>
                        <id>npm generate</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>npm</goal>
                        </goals>
                        <configuration>
                            <arguments>run generate-asyncapi</arguments>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>add-source</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>${project.build.directory}/generated-sources/modelina</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <release>${java.version}</release>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>0.2.0</version>
                        </path>
                    </annotationProcessorPaths>

                    <showWarnings>true</showWarnings>
                    <compilerArgs>
                        <arg>
                            -Amapstruct.defaultInjectionStrategy=constructor
                        </arg>
                        <arg>
                            -Amapstruct.unmappedTargetPolicy=ERROR
                        </arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>backend/**</exclude>
                        <exclude>be/fgov/onerva/wave/**</exclude>
                        <exclude>be/fgov/onerva/person/backend/config/**</exclude>
                        <exclude>be/fgov/onerva/person/backend/e2e/**</exclude>
                        <exclude>be/fgov/onerva/person/backend/**/*Mapper*Impl.*</exclude>
                        <exclude>be/fgov/onerva/person/backend/BackendApplication.*</exclude>
                        <exclude>be/fgov/onerva/person/backend/web/**</exclude>
                        <exclude>be/fgov/onerva/person/msg/**</exclude>
<!--                        Class below commented to refactoring tests-->
                        <exclude>be/fgov/onerva/person/backend/citizeninfo/v2/mapper/CitizenInfoMapperV2*</exclude>
                        <exclude>be/fgov/onerva/person/backend/citizeninfo/v2/controller/CitizenInfoV2Controller.*</exclude>
                        <exclude>be/fgov/onerva/person/backend/request/broker/**</exclude>
                        <exclude>be/fgov/onerva/person/backend/request/service/LocalPersonResponseSimulator.java</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-toolchains-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>toolchain</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <toolchains>
                        <jdk>
                            <version>${java.version}</version>
                        </jdk>
                    </toolchains>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>sync</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-devtools</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>pactPublishStable</id>
            <properties>
                <pact.verifier.publishResults>true</pact.verifier.publishResults>
                <pact.provider.version>${env.APP_VERSION}</pact.provider.version>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <systemPropertyVariables>
                                <pact.verifier.publishResults>true</pact.verifier.publishResults>
                                <pact.provider.version>${env.APP_VERSION}</pact.provider.version>
                            </systemPropertyVariables>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>local</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <includes>
                                <include>**/*Test*.java</include>
                            </includes>
                            <environmentVariables>
                                <DOCKER_HOST>tcp://localhost:2375</DOCKER_HOST>
                                <DOCKER_TLS_VERIFY>0</DOCKER_TLS_VERIFY>
                            </environmentVariables>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
