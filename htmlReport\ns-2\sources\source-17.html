


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > SignaleticUpdateRequestDTO</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">backend.rest.model</a>
</div>

<h1>Coverage Summary for Class: SignaleticUpdateRequestDTO (backend.rest.model)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">SignaleticUpdateRequestDTO</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/20)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/16)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/44)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package backend.rest.model;
&nbsp;
&nbsp;import java.net.URI;
&nbsp;import java.util.Objects;
&nbsp;import backend.rest.model.AddressDTO;
&nbsp;import com.fasterxml.jackson.annotation.JsonProperty;
&nbsp;import com.fasterxml.jackson.annotation.JsonCreator;
&nbsp;import com.fasterxml.jackson.annotation.JsonTypeName;
&nbsp;import java.time.LocalDate;
&nbsp;import org.springframework.format.annotation.DateTimeFormat;
&nbsp;import org.springframework.lang.Nullable;
&nbsp;import java.time.OffsetDateTime;
&nbsp;import jakarta.validation.Valid;
&nbsp;import jakarta.validation.constraints.*;
&nbsp;import org.hibernate.validator.constraints.*;
&nbsp;import io.swagger.v3.oas.annotations.media.Schema;
&nbsp;
&nbsp;
&nbsp;import java.util.*;
&nbsp;import jakarta.annotation.Generated;
&nbsp;
&nbsp;/**
&nbsp; * Signaletic data update request (address and personal information)
&nbsp; */
&nbsp;
&nbsp;@Schema(name = &quot;SignaleticUpdateRequest&quot;, description = &quot;Signaletic data update request (address and personal information)&quot;)
&nbsp;@JsonTypeName(&quot;SignaleticUpdateRequest&quot;)
&nbsp;@Generated(value = &quot;org.openapitools.codegen.languages.SpringCodegen&quot;, date = &quot;2025-07-16T14:11:30.627767400+02:00[Europe/Brussels]&quot;, comments = &quot;Generator version: 7.12.0&quot;)
<b class="nc">&nbsp;public class SignaleticUpdateRequestDTO {</b>
&nbsp;
&nbsp;  private @Nullable AddressDTO address;
&nbsp;
&nbsp;  private @Nullable String birthDate;
&nbsp;
&nbsp;  private @Nullable Integer languageCode;
&nbsp;
&nbsp;  private @Nullable Integer nationalityCode;
&nbsp;
&nbsp;  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
&nbsp;  private @Nullable LocalDate valueDate;
&nbsp;
&nbsp;  public SignaleticUpdateRequestDTO address(AddressDTO address) {
<b class="nc">&nbsp;    this.address = address;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get address
&nbsp;   * @return address
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;address&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;address&quot;)
&nbsp;  public AddressDTO getAddress() {
<b class="nc">&nbsp;    return address;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setAddress(AddressDTO address) {
<b class="nc">&nbsp;    this.address = address;</b>
&nbsp;  }
&nbsp;
&nbsp;  public SignaleticUpdateRequestDTO birthDate(String birthDate) {
<b class="nc">&nbsp;    this.birthDate = birthDate;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Birth date in YYYYMMDD format
&nbsp;   * @return birthDate
&nbsp;   */
&nbsp;  @Pattern(regexp = &quot;^\\d{8}$&quot;) 
&nbsp;  @Schema(name = &quot;birthDate&quot;, example = &quot;19910626&quot;, description = &quot;Birth date in YYYYMMDD format&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;birthDate&quot;)
&nbsp;  public String getBirthDate() {
<b class="nc">&nbsp;    return birthDate;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setBirthDate(String birthDate) {
<b class="nc">&nbsp;    this.birthDate = birthDate;</b>
&nbsp;  }
&nbsp;
&nbsp;  public SignaleticUpdateRequestDTO languageCode(Integer languageCode) {
<b class="nc">&nbsp;    this.languageCode = languageCode;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Language code: 1=fr, 2=nl, 3=de
&nbsp;   * minimum: 1
&nbsp;   * maximum: 3
&nbsp;   * @return languageCode
&nbsp;   */
&nbsp;  @Min(1) @Max(3) 
&nbsp;  @Schema(name = &quot;languageCode&quot;, description = &quot;Language code: 1=fr, 2=nl, 3=de&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;languageCode&quot;)
&nbsp;  public Integer getLanguageCode() {
<b class="nc">&nbsp;    return languageCode;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setLanguageCode(Integer languageCode) {
<b class="nc">&nbsp;    this.languageCode = languageCode;</b>
&nbsp;  }
&nbsp;
&nbsp;  public SignaleticUpdateRequestDTO nationalityCode(Integer nationalityCode) {
<b class="nc">&nbsp;    this.nationalityCode = nationalityCode;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * CBSS Nationality code
&nbsp;   * minimum: 100
&nbsp;   * maximum: 999
&nbsp;   * @return nationalityCode
&nbsp;   */
&nbsp;  @Min(100) @Max(999) 
&nbsp;  @Schema(name = &quot;nationalityCode&quot;, description = &quot;CBSS Nationality code&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;nationalityCode&quot;)
&nbsp;  public Integer getNationalityCode() {
<b class="nc">&nbsp;    return nationalityCode;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setNationalityCode(Integer nationalityCode) {
<b class="nc">&nbsp;    this.nationalityCode = nationalityCode;</b>
&nbsp;  }
&nbsp;
&nbsp;  public SignaleticUpdateRequestDTO valueDate(LocalDate valueDate) {
<b class="nc">&nbsp;    this.valueDate = valueDate;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Value date for signaletic data (optional, uses main valueDate if not provided)
&nbsp;   * @return valueDate
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;valueDate&quot;, description = &quot;Value date for signaletic data (optional, uses main valueDate if not provided)&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;valueDate&quot;)
&nbsp;  public LocalDate getValueDate() {
<b class="nc">&nbsp;    return valueDate;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setValueDate(LocalDate valueDate) {
<b class="nc">&nbsp;    this.valueDate = valueDate;</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public boolean equals(Object o) {
<b class="nc">&nbsp;    if (this == o) {</b>
<b class="nc">&nbsp;      return true;</b>
&nbsp;    }
<b class="nc">&nbsp;    if (o == null || getClass() != o.getClass()) {</b>
<b class="nc">&nbsp;      return false;</b>
&nbsp;    }
<b class="nc">&nbsp;    SignaleticUpdateRequestDTO signaleticUpdateRequest = (SignaleticUpdateRequestDTO) o;</b>
<b class="nc">&nbsp;    return Objects.equals(this.address, signaleticUpdateRequest.address) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.birthDate, signaleticUpdateRequest.birthDate) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.languageCode, signaleticUpdateRequest.languageCode) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.nationalityCode, signaleticUpdateRequest.nationalityCode) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.valueDate, signaleticUpdateRequest.valueDate);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public int hashCode() {
<b class="nc">&nbsp;    return Objects.hash(address, birthDate, languageCode, nationalityCode, valueDate);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public String toString() {
<b class="nc">&nbsp;    StringBuilder sb = new StringBuilder();</b>
<b class="nc">&nbsp;    sb.append(&quot;class SignaleticUpdateRequestDTO {\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    address: &quot;).append(toIndentedString(address)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    birthDate: &quot;).append(toIndentedString(birthDate)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    languageCode: &quot;).append(toIndentedString(languageCode)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    nationalityCode: &quot;).append(toIndentedString(nationalityCode)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    valueDate: &quot;).append(toIndentedString(valueDate)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;}&quot;);</b>
<b class="nc">&nbsp;    return sb.toString();</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Convert the given object to string with each line indented by 4 spaces
&nbsp;   * (except the first line).
&nbsp;   */
&nbsp;  private String toIndentedString(Object o) {
<b class="nc">&nbsp;    if (o == null) {</b>
<b class="nc">&nbsp;      return &quot;null&quot;;</b>
&nbsp;    }
<b class="nc">&nbsp;    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</b>
&nbsp;  }
&nbsp;}
&nbsp;
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
