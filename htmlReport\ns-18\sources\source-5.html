


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > BankUpdateInfo</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.request.model</a>
</div>

<h1>Coverage Summary for Class: BankUpdateInfo (be.fgov.onerva.person.backend.request.model)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
</tr>
<tr>
  <td class="name">BankUpdateInfo</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.request.model;
&nbsp;
&nbsp;import jakarta.validation.constraints.NotNull;
&nbsp;import jakarta.validation.constraints.Pattern;
&nbsp;import jakarta.validation.constraints.Size;
&nbsp;import lombok.Builder;
&nbsp;import lombok.Getter;
&nbsp;import lombok.ToString;
&nbsp;
&nbsp;import java.time.LocalDate;
&nbsp;
&nbsp;/**
&nbsp; * DTO for bank information updates matching BankUpdateRequest schema from API specification.
&nbsp; * Contains IBAN, BIC, account holder, payment type, and value date information.
&nbsp; */
&nbsp;@Builder
&nbsp;@Getter
&nbsp;@ToString
&nbsp;public class BankUpdateInfo {
&nbsp;    
&nbsp;    /**
&nbsp;     * International Bank Account Number (IBAN) - max 34 characters
&nbsp;     * Pattern validates basic IBAN format (2 letters + 2 digits + up to 30 alphanumeric)
&nbsp;     */
&nbsp;    @Pattern(regexp = &quot;^[A-Z]{2}[0-9]{2}[A-Z0-9]{1,30}$&quot;, message = &quot;Invalid IBAN format&quot;)
&nbsp;    @Size(max = 34, message = &quot;IBAN cannot exceed 34 characters&quot;)
&nbsp;    private String iban;
&nbsp;    
&nbsp;    /**
&nbsp;     * Bank Identifier Code (BIC/SWIFT) - max 11 characters
&nbsp;     * Pattern validates BIC format (4 letters + 2 letters + 2 alphanumeric + optional 3 alphanumeric)
&nbsp;     */
&nbsp;    @Pattern(regexp = &quot;^[A-Z]{4}[A-Z]{2}[A-Z0-9]{2}([A-Z0-9]{3})?$&quot;, message = &quot;Invalid BIC format&quot;)
&nbsp;    @Size(max = 11, message = &quot;BIC cannot exceed 11 characters&quot;)
&nbsp;    private String bic;
&nbsp;    
&nbsp;    /**
&nbsp;     * Account holder name - max 30 characters
&nbsp;     */
&nbsp;    @Size(max = 30, message = &quot;Account holder name cannot exceed 30 characters&quot;)
&nbsp;    private String accountHolder;
&nbsp;    
&nbsp;    /**
&nbsp;     * Payment type for the bank account
&nbsp;     */
&nbsp;    private PaymentType paymentType;
&nbsp;    
&nbsp;    /**
&nbsp;     * Value date for when the bank information becomes effective
&nbsp;     */
&nbsp;    @NotNull(message = &quot;Valid from date is required&quot;)
&nbsp;    private LocalDate validFrom;
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
