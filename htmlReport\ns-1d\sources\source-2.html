


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > PersonCreatedPayload</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.msg.v1</a>
</div>

<h1>Coverage Summary for Class: PersonCreatedPayload (be.fgov.onerva.person.msg.v1)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">PersonCreatedPayload</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    78.3%
  </span>
  <span class="absValue">
    (18/23)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/24)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    47.4%
  </span>
  <span class="absValue">
    (18/38)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.msg.v1;
&nbsp;import be.fgov.onerva.person.msg.v1.PersonCreated;
&nbsp;import java.util.Objects;
&nbsp;import java.util.Map;
<b class="fc">&nbsp;public class PersonCreatedPayload {</b>
&nbsp;  private PersonCreated data;
&nbsp;  private String id;
&nbsp;  private String source;
&nbsp;  private String specversion;
&nbsp;  private String type;
&nbsp;  private String datacontenttype;
&nbsp;  private String dataschema;
&nbsp;  private String subject;
&nbsp;  private String time;
&nbsp;  private Map&lt;String, Object&gt; additionalProperties;
&nbsp;
<b class="fc">&nbsp;  public PersonCreated getData() { return this.data; }</b>
<b class="fc">&nbsp;  public void setData(PersonCreated data) { this.data = data; }</b>
&nbsp;
<b class="fc">&nbsp;  public String getId() { return this.id; }</b>
<b class="fc">&nbsp;  public void setId(String id) { this.id = id; }</b>
&nbsp;
<b class="fc">&nbsp;  public String getSource() { return this.source; }</b>
<b class="fc">&nbsp;  public void setSource(String source) { this.source = source; }</b>
&nbsp;
<b class="fc">&nbsp;  public String getSpecversion() { return this.specversion; }</b>
<b class="fc">&nbsp;  public void setSpecversion(String specversion) { this.specversion = specversion; }</b>
&nbsp;
<b class="fc">&nbsp;  public String getType() { return this.type; }</b>
<b class="fc">&nbsp;  public void setType(String type) { this.type = type; }</b>
&nbsp;
<b class="fc">&nbsp;  public String getDatacontenttype() { return this.datacontenttype; }</b>
<b class="fc">&nbsp;  public void setDatacontenttype(String datacontenttype) { this.datacontenttype = datacontenttype; }</b>
&nbsp;
<b class="fc">&nbsp;  public String getDataschema() { return this.dataschema; }</b>
<b class="nc">&nbsp;  public void setDataschema(String dataschema) { this.dataschema = dataschema; }</b>
&nbsp;
<b class="fc">&nbsp;  public String getSubject() { return this.subject; }</b>
<b class="nc">&nbsp;  public void setSubject(String subject) { this.subject = subject; }</b>
&nbsp;
<b class="fc">&nbsp;  public String getTime() { return this.time; }</b>
<b class="fc">&nbsp;  public void setTime(String time) { this.time = time; }</b>
&nbsp;
<b class="fc">&nbsp;  public Map&lt;String, Object&gt; getAdditionalProperties() { return this.additionalProperties; }</b>
<b class="nc">&nbsp;  public void setAdditionalProperties(Map&lt;String, Object&gt; additionalProperties) { this.additionalProperties = additionalProperties; }</b>
&nbsp;
&nbsp;  @Override
&nbsp;  public boolean equals(Object o) {
<b class="nc">&nbsp;    if (this == o) {</b>
<b class="nc">&nbsp;      return true;</b>
&nbsp;    }
<b class="nc">&nbsp;    if (o == null || getClass() != o.getClass()) {</b>
<b class="nc">&nbsp;      return false;</b>
&nbsp;    }
<b class="nc">&nbsp;    PersonCreatedPayload self = (PersonCreatedPayload) o;</b>
<b class="nc">&nbsp;      return </b>
<b class="nc">&nbsp;        Objects.equals(this.data, self.data) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.id, self.id) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.source, self.source) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.specversion, self.specversion) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.type, self.type) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.datacontenttype, self.datacontenttype) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.dataschema, self.dataschema) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.subject, self.subject) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.time, self.time) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.additionalProperties, self.additionalProperties);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public int hashCode() {
<b class="nc">&nbsp;    return Objects.hash((Object)data, (Object)id, (Object)source, (Object)specversion, (Object)type, (Object)datacontenttype, (Object)dataschema, (Object)subject, (Object)time, (Object)additionalProperties);</b>
&nbsp;  }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
