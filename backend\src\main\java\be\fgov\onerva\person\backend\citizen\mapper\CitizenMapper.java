package be.fgov.onerva.person.backend.citizen.mapper;

import backend.rest.model.CitizenCreationRequestDTO;
import backend.rest.model.CitizenDTO;
import backend.rest.model.CitizenPageDTO;
import backend.rest.model.CitizenUpdateRequestDTO;
import be.fgov.onerva.common.utils.PensionNumberUtils;
import be.fgov.onerva.person.backend.citizen.model.AddressUpdateRequest;
import be.fgov.onerva.person.backend.citizen.model.CitizenCreationRequest;
import be.fgov.onerva.person.backend.citizen.model.CitizenEntity;
import be.fgov.onerva.person.backend.citizen.model.CitizenUpdateRequest;
import be.fgov.onerva.person.backend.request.model.Address;
import be.fgov.onerva.person.backend.request.model.BankUpdateInfo;
import be.fgov.onerva.person.backend.request.model.PaymentType;
import be.fgov.onerva.person.backend.citizen.model.UnionDueUpdateInfo;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.data.domain.Page;

import java.time.LocalDate;

@Mapper
public interface CitizenMapper {

    @Mapping(target = "pensionNumber", source = "entity.id")
    @Mapping(target = "zipCode", source = "entity.zipCode")
    @Mapping(target = "numbox", source = "entity.numBox")
    @Mapping(target = "niss", expression = "java(niss(entity))")
    @Mapping(target = "lastname", expression = "java(lastName(entity))")
    @Mapping(target = "firstname", expression = "java(firstName(entity))")
    @Mapping(target = "agent", source = "entity.flagPersonnel")
    CitizenDTO map(CitizenEntity entity);

    @Mapping(source = "number", target = "pageNumber")
    @Mapping(source = "size", target = "pageSize")
    @Mapping(source = "totalPages", target = "totalPage")
    @Mapping(source = "totalElements", target = "totalElements")
    @Mapping(source = "first", target = "isFirst")
    @Mapping(source = "last", target = "isLast")
    CitizenPageDTO mapPageToDto(Page<CitizenEntity> source);

    @Mapping(source = "businessDomain", target = "domain")
    CitizenCreationRequest map(CitizenCreationRequestDTO source, String businessDomain, boolean allowance);

    @Mapping(target = "address", expression = "java(mapAddress(source.getAddress()))")
    @Mapping(target = "bankInfo", expression = "java(mapBankInfo(source))")
    @Mapping(target = "unionDueInfo", expression = "java(mapUnionDueInfo(source))")
    @Mapping(target = "birthDate", expression = "java(mapBirthDate(source))")
    @Mapping(target = "languageCode", expression = "java(mapLanguageCode(source))")
    @Mapping(target = "unemploymentOffice", expression = "java(mapUnemploymentOffice(source))")
    CitizenUpdateRequest map(CitizenUpdateRequestDTO source, String niss, String username);

    default AddressUpdateRequest mapAddress(backend.rest.model.ForeignAddressDTO addressDTO) {
        if (addressDTO == null) {
            return null;
        }
        return AddressUpdateRequest.builder()
                .street(addressDTO.getStreet())
                .number(addressDTO.getNumber())
                .box(addressDTO.getBox())
                .zip(addressDTO.getZip() != null ? addressDTO.getZip().toString() : null)
                .city(addressDTO.getCity())
                .countryCode(addressDTO.getCountryCode())
                .build();
    }

    /**
     * Maps bank information from DTO to internal model.
     * Handles both new nested structure and backward compatibility with deprecated
     * fields.
     */
    default BankUpdateInfo mapBankInfo(CitizenUpdateRequestDTO source) {
        if (source.getBankInfo() != null) {
            return BankUpdateInfo.builder()
                    .paymentType(mapPaymentType(source.getBankInfo().getPaymentType()))
                    .validFrom(source.getBankInfo().getValidFrom())
                    .iban(source.getBankInfo().getIban())
                    .bic(source.getBankInfo().getBic())
                    .accountHolder(source.getBankInfo().getAccountHolder())
                    .build();
        }
        return null;
    }

    /**
     * Maps union due information from DTO to internal model.
     * Handles both new nested structure and backward compatibility with deprecated
     * fields.
     */
    default UnionDueUpdateInfo mapUnionDueInfo(CitizenUpdateRequestDTO source) {
        // For now, handle backward compatibility with deprecated unionDue field
        // When API is updated with nested unionDueInfo, this will be updated
        // accordingly
        if (source.getUnionDueInfo() != null) {
            return UnionDueUpdateInfo.builder()
                    .unionDue(source.getUnionDueInfo().getUnionDue())
                    .validFrom(source.getUnionDueInfo().getValidFrom())
                    .build();
        }
        return null;
    }

    /**
     * Maps birth date from DTO. Will be updated when API includes birthDate field.
     */
    default LocalDate mapBirthDate(CitizenUpdateRequestDTO source) {
        return source.getBirthDate();
    }

    /**
     * Maps language code from DTO. Will be updated when API includes languageCode
     * field.
     */
    default Integer mapLanguageCode(CitizenUpdateRequestDTO source) {
        return source.getLanguageCode();
    }

    /**
     * Maps unemployment office from DTO. Will be updated when API includes
     * unemploymentOffice field.
     */
    default Integer mapUnemploymentOffice(CitizenUpdateRequestDTO source) {
        return source.getUnemploymentOffice();
    }

    /**
     * Maps PaymentTypeDTO to internal PaymentType enum
     */
    default PaymentType mapPaymentType(backend.rest.model.PaymentTypeDTO paymentTypeDTO) {
        if (paymentTypeDTO == null) {
            return null;
        }
        return switch (paymentTypeDTO) {
            case BANK_TRANSFER -> PaymentType.BANK_TRANSFER;
            case CIRCULAR_CHEQUE -> PaymentType.CIRCULAR_CHEQUE;
            case OTHER_BANK_TRANSFER -> PaymentType.OTHER_BANK_TRANSFER;
        };
    }

    default String firstName(CitizenEntity entity) {
        if (entity == null) {
            return null;
        }
        return entity.getFullName().split(",")[1].trim();
    }

    default String lastName(CitizenEntity entity) {
        if (entity == null) {
            return null;
        }
        return entity.getFullName().split(",")[0].trim();
    }

    default String niss(CitizenEntity entity) {
        if (entity == null) {
            return null;
        }

        return StringUtils.leftPad(PensionNumberUtils.convertToInssWithDefault(entity.getId()).toString(), 11, '0');
    }
}
