


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > BankAccountDTO</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">backend.rest.model</a>
</div>

<h1>Coverage Summary for Class: BankAccountDTO (backend.rest.model)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">BankAccountDTO</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    82.4%
  </span>
  <span class="absValue">
    (14/17)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    42.9%
  </span>
  <span class="absValue">
    (6/14)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    65%
  </span>
  <span class="absValue">
    (26/40)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package backend.rest.model;
&nbsp;
&nbsp;import java.net.URI;
&nbsp;import java.util.Objects;
&nbsp;import com.fasterxml.jackson.annotation.JsonProperty;
&nbsp;import com.fasterxml.jackson.annotation.JsonCreator;
&nbsp;import com.fasterxml.jackson.annotation.JsonTypeName;
&nbsp;import java.time.LocalDate;
&nbsp;import org.springframework.format.annotation.DateTimeFormat;
&nbsp;import org.springframework.lang.Nullable;
&nbsp;import java.time.OffsetDateTime;
&nbsp;import jakarta.validation.Valid;
&nbsp;import jakarta.validation.constraints.*;
&nbsp;import org.hibernate.validator.constraints.*;
&nbsp;import io.swagger.v3.oas.annotations.media.Schema;
&nbsp;
&nbsp;
&nbsp;import java.util.*;
&nbsp;import jakarta.annotation.Generated;
&nbsp;
&nbsp;/**
&nbsp; * BankAccountDTO
&nbsp; */
&nbsp;
&nbsp;@JsonTypeName(&quot;BankAccount&quot;)
&nbsp;@Generated(value = &quot;org.openapitools.codegen.languages.SpringCodegen&quot;, date = &quot;2025-07-16T14:11:30.*********+02:00[Europe/Brussels]&quot;, comments = &quot;Generator version: 7.12.0&quot;)
<b class="fc">&nbsp;public class BankAccountDTO {</b>
&nbsp;
&nbsp;  private @Nullable String iban;
&nbsp;
<b class="fc">&nbsp;  private @Nullable String bic = null;</b>
&nbsp;
<b class="fc">&nbsp;  private @Nullable String holder = null;</b>
&nbsp;
&nbsp;  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
&nbsp;  private @Nullable LocalDate validFrom;
&nbsp;
&nbsp;  public BankAccountDTO iban(String iban) {
<b class="fc">&nbsp;    this.iban = iban;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get iban
&nbsp;   * @return iban
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;iban&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;iban&quot;)
&nbsp;  public String getIban() {
<b class="fc">&nbsp;    return iban;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setIban(String iban) {
<b class="fc">&nbsp;    this.iban = iban;</b>
&nbsp;  }
&nbsp;
&nbsp;  public BankAccountDTO bic(String bic) {
<b class="fc">&nbsp;    this.bic = bic;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get bic
&nbsp;   * @return bic
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;bic&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;bic&quot;)
&nbsp;  public String getBic() {
<b class="fc">&nbsp;    return bic;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setBic(String bic) {
<b class="fc">&nbsp;    this.bic = bic;</b>
&nbsp;  }
&nbsp;
&nbsp;  public BankAccountDTO holder(String holder) {
<b class="fc">&nbsp;    this.holder = holder;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * The name of the bank account holder
&nbsp;   * @return holder
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;holder&quot;, description = &quot;The name of the bank account holder&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;holder&quot;)
&nbsp;  public String getHolder() {
<b class="fc">&nbsp;    return holder;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setHolder(String holder) {
<b class="fc">&nbsp;    this.holder = holder;</b>
&nbsp;  }
&nbsp;
&nbsp;  public BankAccountDTO validFrom(LocalDate validFrom) {
<b class="fc">&nbsp;    this.validFrom = validFrom;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get validFrom
&nbsp;   * @return validFrom
&nbsp;   */
&nbsp;  @Valid 
&nbsp;  @Schema(name = &quot;validFrom&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;validFrom&quot;)
&nbsp;  public LocalDate getValidFrom() {
<b class="fc">&nbsp;    return validFrom;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setValidFrom(LocalDate validFrom) {
<b class="fc">&nbsp;    this.validFrom = validFrom;</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public boolean equals(Object o) {
<b class="pc">&nbsp;    if (this == o) {</b>
<b class="nc">&nbsp;      return true;</b>
&nbsp;    }
<b class="pc">&nbsp;    if (o == null || getClass() != o.getClass()) {</b>
<b class="nc">&nbsp;      return false;</b>
&nbsp;    }
<b class="fc">&nbsp;    BankAccountDTO bankAccount = (BankAccountDTO) o;</b>
<b class="pc">&nbsp;    return Objects.equals(this.iban, bankAccount.iban) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.bic, bankAccount.bic) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.holder, bankAccount.holder) &amp;&amp;</b>
<b class="fc">&nbsp;        Objects.equals(this.validFrom, bankAccount.validFrom);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public int hashCode() {
<b class="nc">&nbsp;    return Objects.hash(iban, bic, holder, validFrom);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public String toString() {
<b class="nc">&nbsp;    StringBuilder sb = new StringBuilder();</b>
<b class="nc">&nbsp;    sb.append(&quot;class BankAccountDTO {\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    iban: &quot;).append(toIndentedString(iban)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    bic: &quot;).append(toIndentedString(bic)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    holder: &quot;).append(toIndentedString(holder)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    validFrom: &quot;).append(toIndentedString(validFrom)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;}&quot;);</b>
<b class="nc">&nbsp;    return sb.toString();</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Convert the given object to string with each line indented by 4 spaces
&nbsp;   * (except the first line).
&nbsp;   */
&nbsp;  private String toIndentedString(Object o) {
<b class="nc">&nbsp;    if (o == null) {</b>
<b class="nc">&nbsp;      return &quot;null&quot;;</b>
&nbsp;    }
<b class="nc">&nbsp;    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</b>
&nbsp;  }
&nbsp;}
&nbsp;
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
