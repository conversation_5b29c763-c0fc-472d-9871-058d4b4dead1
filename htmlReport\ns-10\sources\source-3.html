


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > HttpClientConfig</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.config</a>
</div>

<h1>Coverage Summary for Class: HttpClientConfig (be.fgov.onerva.person.backend.config)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">HttpClientConfig</td>
<td class="coverageStat">
  <span class="percent">
    40%
  </span>
  <span class="absValue">
    (2/5)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    44.4%
  </span>
  <span class="absValue">
    (4/9)
  </span>
</td>
</tr>
  <tr>
    <td class="name">HttpClientConfig$$SpringCGLIB$$0</td>
  </tr>
  <tr>
    <td class="name">HttpClientConfig$$SpringCGLIB$$FastClass$$0</td>
  </tr>
  <tr>
    <td class="name">HttpClientConfig$$SpringCGLIB$$FastClass$$1</td>
  </tr>
  <tr>
    <td class="name">HttpClientConfig$1</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/7)
  </span>
</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    28.6%
  </span>
  <span class="absValue">
    (2/7)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    25%
  </span>
  <span class="absValue">
    (4/16)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.config;
&nbsp;
&nbsp;import be.fgov.onerva.wave.ApiClient;
&nbsp;import be.fgov.onerva.wave.api.UserApi;
&nbsp;import be.fgov.onerva.wave.model.User;
&nbsp;import be.fgov.onerva.wave.model.UserCriteria;
&nbsp;import org.springframework.beans.factory.annotation.Value;
&nbsp;import org.springframework.boot.web.client.RestClientCustomizer;
&nbsp;import org.springframework.context.annotation.Bean;
&nbsp;import org.springframework.context.annotation.Configuration;
&nbsp;import org.springframework.context.annotation.Profile;
&nbsp;import org.springframework.security.oauth2.client.AuthorizedClientServiceOAuth2AuthorizedClientManager;
&nbsp;import org.springframework.security.oauth2.client.OAuth2AuthorizedClientManager;
&nbsp;import org.springframework.security.oauth2.client.OAuth2AuthorizedClientService;
&nbsp;import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
&nbsp;import org.springframework.security.oauth2.client.web.client.OAuth2ClientHttpRequestInterceptor;
&nbsp;import org.springframework.web.client.RestClient;
&nbsp;import org.springframework.web.client.RestClientResponseException;
&nbsp;
&nbsp;@Configuration
<b class="fc">&nbsp;public class HttpClientConfig {</b>
&nbsp;
&nbsp;    public static final String CLIENT_REGISTRATION_ID = &quot;keycloak&quot;;
&nbsp;    public static final String DEV_PROFILE = &quot;(dev | ci | local | default)&quot;;
&nbsp;
&nbsp;    @Bean @Profile(&quot;!&quot; + DEV_PROFILE)
&nbsp;    UserApi userApi(RestClient.Builder builder, @Value(&quot;${wave.api}&quot;) String waveUrl) {
<b class="nc">&nbsp;        ApiClient apiClient = new ApiClient(builder.build());</b>
<b class="nc">&nbsp;        apiClient.setBasePath(waveUrl);</b>
&nbsp;
<b class="nc">&nbsp;        return new UserApi(apiClient);</b>
&nbsp;    }
&nbsp;
&nbsp;    @Bean @Profile(&quot;!&quot; + DEV_PROFILE)
&nbsp;    OAuth2AuthorizedClientManager authorizedClientManager(
&nbsp;            OAuth2AuthorizedClientService oAuth2AuthorizedClientService,
&nbsp;            ClientRegistrationRepository clientRegistrationRepository
&nbsp;    ) {
<b class="nc">&nbsp;        return new AuthorizedClientServiceOAuth2AuthorizedClientManager(clientRegistrationRepository, oAuth2AuthorizedClientService);</b>
&nbsp;    }
&nbsp;
&nbsp;    @Bean @Profile({&quot;!&quot; + DEV_PROFILE})
&nbsp;    RestClientCustomizer globalOAuthClientInterceptor(OAuth2AuthorizedClientManager authorizedClientManager) {
<b class="fc">&nbsp;        var interceptor = new OAuth2ClientHttpRequestInterceptor(authorizedClientManager);</b>
<b class="fc">&nbsp;        interceptor.setClientRegistrationIdResolver(req -&gt; CLIENT_REGISTRATION_ID);</b>
&nbsp;
<b class="fc">&nbsp;        return  builder -&gt; builder.requestInterceptor(interceptor);</b>
&nbsp;    }
&nbsp;
&nbsp;    @Bean @Profile(DEV_PROFILE)
&nbsp;    UserApi mockUserApi() {
<b class="nc">&nbsp;        return new UserApi() {</b>
&nbsp;            @Override
&nbsp;            public User searchUsers(UserCriteria criteria) throws RestClientResponseException {
<b class="nc">&nbsp;                return new User()</b>
<b class="nc">&nbsp;                        .firstname(&quot;James&quot;)</b>
<b class="nc">&nbsp;                        .lastname(&quot;Bond&quot;)</b>
<b class="nc">&nbsp;                        .addOperatorCodesItem(&quot;007&quot;)</b>
<b class="nc">&nbsp;                        .username(criteria.getUsername() != null? criteria.getUsername() : &quot;jbond&quot;)</b>
<b class="nc">&nbsp;                        .inss(criteria.getInss() != null? criteria.getInss() : &quot;77070700791&quot;);</b>
&nbsp;            }
&nbsp;        };
&nbsp;    }
&nbsp;
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
