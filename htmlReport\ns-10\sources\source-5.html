


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > OpenApiConfig</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.config</a>
</div>

<h1>Coverage Summary for Class: OpenApiConfig (be.fgov.onerva.person.backend.config)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">OpenApiConfig</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (2/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    50%
  </span>
  <span class="absValue">
    (1/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (12/12)
  </span>
</td>
</tr>
  <tr>
    <td class="name">OpenApiConfig$$SpringCGLIB$$0</td>
  </tr>
  <tr>
    <td class="name">OpenApiConfig$$SpringCGLIB$$FastClass$$0</td>
  </tr>
  <tr>
    <td class="name">OpenApiConfig$$SpringCGLIB$$FastClass$$1</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (2/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    50%
  </span>
  <span class="absValue">
    (1/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (12/12)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.config;
&nbsp;
&nbsp;import io.swagger.v3.oas.models.Components;
&nbsp;import io.swagger.v3.oas.models.OpenAPI;
&nbsp;import io.swagger.v3.oas.models.security.*;
&nbsp;import org.springframework.boot.autoconfigure.security.oauth2.resource.OAuth2ResourceServerProperties;
&nbsp;import org.springframework.context.annotation.Bean;
&nbsp;import org.springframework.context.annotation.Configuration;
&nbsp;import org.springframework.core.env.Environment;
&nbsp;
&nbsp;@Configuration
<b class="fc">&nbsp;public class OpenApiConfig {</b>
&nbsp;    @Bean
&nbsp;    OpenAPI customOpenAPI(OAuth2ResourceServerProperties props, Environment env) {
<b class="fc">&nbsp;        var schemeName = &quot;openId&quot;;</b>
<b class="pc">&nbsp;        String issuerUri = env.matchesProfiles(&quot;dev&quot;)? &quot;http://localhost:8082/realms/onemrva-agents&quot; : props.getJwt().getIssuerUri();</b>
<b class="fc">&nbsp;        return new OpenAPI()</b>
<b class="fc">&nbsp;                .components(new Components()</b>
<b class="fc">&nbsp;                        .addSecuritySchemes(</b>
&nbsp;                                schemeName,
&nbsp;                                new SecurityScheme()
<b class="fc">&nbsp;                                        .type(SecurityScheme.Type.OAUTH2)</b>
<b class="fc">&nbsp;                                        .flows(new OAuthFlows().authorizationCode(new OAuthFlow()</b>
<b class="fc">&nbsp;                                                .authorizationUrl(issuerUri + &quot;/protocol/openid-connect/auth&quot;)</b>
<b class="fc">&nbsp;                                                .tokenUrl(issuerUri + &quot;/protocol/openid-connect/token&quot;)</b>
<b class="fc">&nbsp;                                                .scopes(new Scopes().addString(&quot;openid&quot;, &quot;&quot;))</b>
&nbsp;                                        ))
&nbsp;                        )
&nbsp;                )
<b class="fc">&nbsp;                .addSecurityItem(new SecurityRequirement().addList(schemeName))</b>
&nbsp;        ;
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
