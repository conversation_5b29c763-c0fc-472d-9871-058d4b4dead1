


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenInfoContratDsRepository</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.citizeninfo.persistence</a>
</div>

<h1>Coverage Summary for Class: CitizenInfoContratDsRepository (be.fgov.onerva.person.backend.citizeninfo.persistence)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
</tr>
<tr>
  <td class="name">CitizenInfoContratDsRepository</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.citizeninfo.persistence;
&nbsp;
&nbsp;import be.fgov.onerva.person.backend.citizeninfo.model.CitizenInfoContratDs;
&nbsp;import org.springframework.data.jpa.repository.JpaRepository;
&nbsp;import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
&nbsp;
&nbsp;import java.util.List;
&nbsp;
&nbsp;public interface CitizenInfoContratDsRepository extends JpaRepository&lt;CitizenInfoContratDs, Integer&gt;, JpaSpecificationExecutor&lt;CitizenInfoContratDs&gt; {
&nbsp;
&nbsp;    List&lt;CitizenInfoContratDs&gt; findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(List&lt;Integer&gt; numbox, int flagValid, int dateValid);
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
