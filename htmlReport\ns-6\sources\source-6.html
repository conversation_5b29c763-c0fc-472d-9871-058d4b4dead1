


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenCreationRequest</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.citizen.model</a>
</div>

<h1>Coverage Summary for Class: CitizenCreationRequest (be.fgov.onerva.person.backend.citizen.model)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
</tr>
<tr>
  <td class="name">CitizenCreationRequest</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.citizen.model;
&nbsp;
&nbsp;import jakarta.validation.constraints.NotBlank;
&nbsp;import jakarta.validation.constraints.NotNull;
&nbsp;import jakarta.validation.constraints.Pattern;
&nbsp;import lombok.Builder;
&nbsp;import lombok.Getter;
&nbsp;import lombok.ToString;
&nbsp;
&nbsp;@Builder
&nbsp;@Getter
&nbsp;@ToString
&nbsp;public class CitizenCreationRequest {
&nbsp;    private boolean allowance;
&nbsp;    @NotNull
&nbsp;    private BusinessDomain domain;
&nbsp;    @NotNull
&nbsp;    @Pattern(regexp = &quot;\\d{11}&quot;)
&nbsp;    private String niss;
&nbsp;    @NotBlank
&nbsp;    private String firstname;
&nbsp;    @NotBlank
&nbsp;    private String lastname;
&nbsp;
&nbsp;    private String correlationId;
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
