


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > PersonRequestController</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.request</a>
</div>

<h1>Coverage Summary for Class: PersonRequestController (be.fgov.onerva.person.backend.request)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">PersonRequestController</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (2/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (3/3)
  </span>
</td>
</tr>
  <tr>
    <td class="name">PersonRequestController$$SpringCGLIB$$0</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (2/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (3/3)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.request;
&nbsp;
&nbsp;import backend.api.CitizenRequestsApi;
&nbsp;import backend.rest.model.CitizenRequestDTO;
&nbsp;import backend.rest.model.CitizenRequestPageDTO;
&nbsp;import be.fgov.onerva.person.backend.request.mapper.PersonRequestMapper;
&nbsp;import be.fgov.onerva.person.backend.request.model.PersonRequest;
&nbsp;import be.fgov.onerva.person.backend.request.persistence.PersonRequestRepository;
&nbsp;import io.github.perplexhub.rsql.RSQLJPASupport;
&nbsp;import lombok.RequiredArgsConstructor;
&nbsp;import org.mapstruct.factory.Mappers;
&nbsp;import org.springframework.data.domain.Page;
&nbsp;import org.springframework.data.domain.Pageable;
&nbsp;import org.springframework.http.ResponseEntity;
&nbsp;import org.springframework.web.bind.annotation.RequestMapping;
&nbsp;import org.springframework.web.bind.annotation.RestController;
&nbsp;
&nbsp;@RequiredArgsConstructor
&nbsp;@RequestMapping(&quot;/api&quot;)
&nbsp;@RestController
&nbsp;public class PersonRequestController implements CitizenRequestsApi {
&nbsp;
&nbsp;    private final PersonRequestRepository repository;
&nbsp;    private PersonRequestMapper mapper = Mappers.getMapper(PersonRequestMapper.class);
&nbsp;
&nbsp;    @Override
&nbsp;    public ResponseEntity&lt;CitizenRequestDTO&gt; getById(Long id) {
<b class="fc">&nbsp;        return ResponseEntity.of(repository.findById(id).map(mapper::toDto));</b>
&nbsp;    }
&nbsp;
&nbsp;    @Override
&nbsp;    public ResponseEntity&lt;CitizenRequestPageDTO&gt; searchCitizenRequests(String query, Integer pageNumber, Integer pageSize, String sort, Pageable pageable) {
<b class="fc">&nbsp;        Page&lt;PersonRequest&gt; page = repository.findAll(RSQLJPASupport.rsql(query), pageable);</b>
<b class="fc">&nbsp;        return ResponseEntity.ok(mapper.toDto(page));</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
