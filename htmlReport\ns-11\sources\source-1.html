


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > RabbitmqOauthProperties</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.config.props</a>
</div>

<h1>Coverage Summary for Class: RabbitmqOauthProperties (be.fgov.onerva.person.backend.config.props)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">RabbitmqOauthProperties</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/8)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.config.props;
&nbsp;
&nbsp;import com.rabbitmq.client.impl.OAuth2ClientCredentialsGrantCredentialsProvider;
&nbsp;import jakarta.validation.constraints.NotBlank;
&nbsp;import lombok.Getter;
&nbsp;import lombok.Setter;
&nbsp;import org.springframework.boot.context.properties.ConfigurationProperties;
&nbsp;import org.springframework.validation.annotation.Validated;
&nbsp;
&nbsp;@ConfigurationProperties(prefix = RabbitmqOauthProperties.PREFIX)
&nbsp;@Validated
&nbsp;@Getter
&nbsp;@Setter
<b class="nc">&nbsp;public class RabbitmqOauthProperties {</b>
&nbsp;
&nbsp;    public static final String PREFIX = &quot;rabbitmq.oauth&quot;;
&nbsp;
&nbsp;    @NotBlank
&nbsp;    private String tokenEndpointUri;
&nbsp;    @NotBlank
&nbsp;    private String clientId;
&nbsp;    @NotBlank
&nbsp;    private String clientSecret;
<b class="nc">&nbsp;    @NotBlank</b>
&nbsp;    private String grantType = &quot;client_credentials&quot;;
&nbsp;    private boolean enabled;
&nbsp;
&nbsp;    public OAuth2ClientCredentialsGrantCredentialsProvider toCredentialsProvider() {
<b class="nc">&nbsp;        return new OAuth2ClientCredentialsGrantCredentialsProvider.OAuth2ClientCredentialsGrantCredentialsProviderBuilder()</b>
<b class="nc">&nbsp;                .tokenEndpointUri(tokenEndpointUri)</b>
<b class="nc">&nbsp;                .clientId(clientId)</b>
<b class="nc">&nbsp;                .clientSecret(clientSecret)</b>
<b class="nc">&nbsp;                .grantType(grantType)</b>
<b class="nc">&nbsp;                .build();</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
