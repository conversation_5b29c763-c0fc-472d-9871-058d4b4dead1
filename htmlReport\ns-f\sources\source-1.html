


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenInfoServiceV2</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.citizeninfo.v2.service</a>
</div>

<h1>Coverage Summary for Class: CitizenInfoServiceV2 (be.fgov.onerva.person.backend.citizeninfo.v2.service)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">CitizenInfoServiceV2</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (20/20)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    78.3%
  </span>
  <span class="absValue">
    (47/60)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    98.6%
  </span>
  <span class="absValue">
    (141/143)
  </span>
</td>
</tr>
  <tr>
    <td class="name">CitizenInfoServiceV2$$SpringCGLIB$$0</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (20/20)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    78.3%
  </span>
  <span class="absValue">
    (47/60)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    98.6%
  </span>
  <span class="absValue">
    (141/143)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.citizeninfo.v2.service;
&nbsp;
&nbsp;import backend.rest.model.CitizenInfoPageV2DTO;
&nbsp;import backend.rest.model.CitizenInfoV2DTO;
&nbsp;import backend.rest.model.LanguageDTO;
&nbsp;import backend.rest.model.SexDTO;
&nbsp;import be.fgov.onerva.common.utils.PensionNumberUtils;
&nbsp;import be.fgov.onerva.person.backend.citizeninfo.mapper.DateUtils;
&nbsp;import be.fgov.onerva.person.backend.citizeninfo.model.*;
&nbsp;import be.fgov.onerva.person.backend.citizeninfo.persistence.*;
&nbsp;import be.fgov.onerva.person.backend.citizeninfo.v2.exception.CitizenInfoException;
&nbsp;import be.fgov.onerva.person.backend.citizeninfo.v2.mapper.CitizenInfoMapperV2;
&nbsp;import jakarta.transaction.Transactional;
&nbsp;import lombok.RequiredArgsConstructor;
&nbsp;import lombok.extern.slf4j.Slf4j;
&nbsp;import org.apache.commons.lang3.StringUtils;
&nbsp;import org.springframework.beans.factory.annotation.Value;
&nbsp;import org.springframework.data.domain.Page;
&nbsp;import org.springframework.data.domain.Pageable;
&nbsp;import org.springframework.stereotype.Service;
&nbsp;
&nbsp;import java.math.BigDecimal;
&nbsp;import java.util.Comparator;
&nbsp;import java.util.List;
&nbsp;import java.util.Map;
&nbsp;import java.util.Objects;
&nbsp;import java.util.stream.Collectors;
&nbsp;
&nbsp;@RequiredArgsConstructor
<b class="fc">&nbsp;@Slf4j</b>
&nbsp;@Service
&nbsp;public class CitizenInfoServiceV2 {
&nbsp;
&nbsp;    private final CitizenInfoRepository infoRepository;
&nbsp;    private final CitizenInfoCompteRepository compteRepository;
&nbsp;    private final CitizenInfoGeneralRepository generalRepository;
&nbsp;    private final CitizenInfoNationRepository nationRepository;
&nbsp;    private final CitizenInfoCommunicRepository communicRepository;
&nbsp;    private final CitizenInfoMapperV2 citizenInfoMapperV2;
&nbsp;    private final CitizenInfoComuneDsRepository comuneDsRepository;
&nbsp;
&nbsp;    @Value(&quot;${application.national-citizen-default}&quot;)
&nbsp;    private Integer nationalCitizenDefault;
&nbsp;
&nbsp;    @Transactional
&nbsp;    public CitizenInfoPageV2DTO getCitizenInfoBySsinListV2(List&lt;String&gt; ssin, Pageable pageable) throws CitizenInfoException {
&nbsp;
<b class="fc">&nbsp;        log.debug(&quot;Getting citizen info by ssin list&quot;);</b>
&nbsp;
<b class="fc">&nbsp;        if(Objects.isNull(ssin)){</b>
<b class="fc">&nbsp;            log.warn(&quot;Ssin is required&quot;);</b>
<b class="fc">&nbsp;            throw new CitizenInfoException(&quot;Ssin is required&quot;);</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        var citizenInfoPageV2 = new CitizenInfoPageV2DTO();</b>
<b class="fc">&nbsp;        var citizenInfo = getCitizenInfoOld(ssin, pageable);</b>
&nbsp;
<b class="fc">&nbsp;        log.debug(&quot;Citizen info found {}&quot;,citizenInfo != null ? citizenInfo.stream().toList().size() : 0);</b>
&nbsp;
<b class="fc">&nbsp;        var citizenInfoPageV2DTO = citizenInfoMapperV2.mapPageToDto(citizenInfo);</b>
&nbsp;
<b class="fc">&nbsp;        if (Objects.isNull(citizenInfoPageV2DTO) || Objects.isNull(citizenInfoPageV2DTO.getContent())){</b>
<b class="fc">&nbsp;            log.warn(&quot;No citizen info found for ssin to mapper&quot;);</b>
<b class="fc">&nbsp;            return citizenInfoPageV2;</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        var ssinFormated= ssin.stream()</b>
<b class="fc">&nbsp;                .map(data -&gt; StringUtils.leftPad(data,11,&quot;0&quot;)).toList();</b>
<b class="fc">&nbsp;        log.debug(&quot;Ssin formated setted&quot;);</b>
&nbsp;
<b class="fc">&nbsp;        var numboxList = citizenInfo.getContent()</b>
<b class="fc">&nbsp;                .stream()</b>
<b class="fc">&nbsp;                .map(CitizenInfoEntity::getNumBox)</b>
<b class="fc">&nbsp;                .toList();</b>
&nbsp;
<b class="pc">&nbsp;        log.debug(&quot;Numbox list found {}&quot;,numboxList != null ? numboxList.size() : 0);</b>
&nbsp;
<b class="fc">&nbsp;        var citizenInfoGeneralList = generalRepository.getCitizenInfoGeneralByNumBoxIn(numboxList);</b>
&nbsp;
<b class="fc">&nbsp;        var bisNumbers = getBisNumbers(numboxList);</b>
&nbsp;
<b class="fc">&nbsp;        var citizenInfoNation = nationRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(numboxList, 9, DateUtils.currentDate());</b>
<b class="fc">&nbsp;        var citizenInfoCommunics = communicRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(numboxList, 9, DateUtils.currentDate());</b>
<b class="fc">&nbsp;        var citizenInfoComuneDs = comuneDsRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(numboxList, 9, DateUtils.currentDate());</b>
&nbsp;
<b class="fc">&nbsp;        citizenInfoPageV2DTO.getContent().forEach(citizenInfoV2DTO -&gt; {</b>
<b class="fc">&nbsp;            citizenInfoV2DTO.setSsin(</b>
<b class="fc">&nbsp;                    StringUtils.leftPad(</b>
<b class="fc">&nbsp;                            PensionNumberUtils.convertToInssWithDefault(citizenInfoV2DTO.getNumPens().intValue()).toString(),</b>
&nbsp;                            11,
&nbsp;                            &#39;0&#39;)
&nbsp;            );
<b class="fc">&nbsp;            log.debug(&quot;Setting citizen ssin&quot;);</b>
&nbsp;
<b class="fc">&nbsp;            var citizenInfoGeneralFound = citizenInfoGeneralList</b>
<b class="fc">&nbsp;                    .stream()</b>
<b class="fc">&nbsp;                    .filter(i -&gt; Objects.equals(i.getNumBox(), citizenInfoV2DTO.getNumBox().intValue()))</b>
<b class="fc">&nbsp;                    .findFirst();</b>
&nbsp;
<b class="fc">&nbsp;            citizenInfoGeneralFound.ifPresentOrElse(</b>
<b class="fc">&nbsp;                    infoGeneral -&gt; this.updateCitizenInfo(</b>
&nbsp;                            citizenInfoV2DTO,
&nbsp;                            infoGeneral,
&nbsp;                            citizenInfoNation,
&nbsp;                            citizenInfoCommunics,
&nbsp;                            bisNumbers,
&nbsp;                            citizenInfoComuneDs
&nbsp;                    ),
<b class="fc">&nbsp;                    () -&gt; this.clearCitizenInfo(citizenInfoV2DTO)</b>
&nbsp;            );
&nbsp;        });
&nbsp;
<b class="fc">&nbsp;        log.debug(&quot;Citizen info updated&quot;);</b>
&nbsp;
<b class="fc">&nbsp;        return citizenInfoPageV2DTO;</b>
&nbsp;    }
&nbsp;
&nbsp;    private void updateCitizenInfo(CitizenInfoV2DTO citizenInfoDTO,
&nbsp;                                   CitizenInfoGeneral infoGeneral,
&nbsp;                                   List&lt;CitizenInfoNation&gt; citizenInfoNation,
&nbsp;                                   List&lt;CitizenInfoCommunic&gt; citizenInfoCommunics,
&nbsp;                                   Map&lt;Integer,List&lt;String&gt;&gt; bisNumbers,
&nbsp;                                   List&lt;CitizenInfoComuneDs&gt; citizenInfoComuneDs
&nbsp;    ) {
&nbsp;
<b class="fc">&nbsp;        log.debug(&quot;Updating citizen info&quot;);</b>
<b class="fc">&nbsp;        setBasicInfo(citizenInfoDTO, infoGeneral);</b>
<b class="fc">&nbsp;        setIbanInfo(citizenInfoDTO, infoGeneral);</b>
<b class="fc">&nbsp;        setSexAndLanguageInfo(citizenInfoDTO, infoGeneral);</b>
<b class="fc">&nbsp;        setNationInfo(citizenInfoDTO, infoGeneral, citizenInfoNation);</b>
<b class="fc">&nbsp;        setCommunicationInfo(citizenInfoDTO, infoGeneral, citizenInfoCommunics);</b>
<b class="fc">&nbsp;        setBisNumbers(citizenInfoDTO, bisNumbers);</b>
<b class="fc">&nbsp;        setDeceasedDate(citizenInfoDTO, infoGeneral.getDeceasedDate());</b>
<b class="fc">&nbsp;        setMcpteDate(citizenInfoDTO, infoGeneral.getDateMCpte());</b>
<b class="fc">&nbsp;        setCitizenInfoComuneDs(citizenInfoDTO, infoGeneral,citizenInfoComuneDs);</b>
&nbsp;
<b class="fc">&nbsp;        citizenInfoDTO.setUnemploymentOffice(BigDecimal.valueOf(infoGeneral.getUnemploymentOffice()));</b>
<b class="fc">&nbsp;        citizenInfoDTO.setOP(BigDecimal.valueOf(infoGeneral.getOP()));</b>
&nbsp;    }
&nbsp;
&nbsp;    private void setCitizenInfoComuneDs(CitizenInfoV2DTO citizenInfoDTO,
&nbsp;                                        CitizenInfoGeneral infoGeneral,
&nbsp;                                        List&lt;CitizenInfoComuneDs&gt; citizenInfoComuneDs) {
<b class="fc">&nbsp;        if (!citizenInfoComuneDs.isEmpty()) {</b>
<b class="fc">&nbsp;            log.debug(&quot;Citizen Info comuneDs exists&quot;);</b>
<b class="fc">&nbsp;            var comuneDs = citizenInfoComuneDs.stream()</b>
<b class="fc">&nbsp;                    .filter(comune -&gt; comune.getNumBox() == infoGeneral.getNumBox())</b>
<b class="fc">&nbsp;                    .findFirst();</b>
<b class="fc">&nbsp;            log.debug(&quot;Setting citizen commune bcss&quot;);</b>
<b class="fc">&nbsp;            comuneDs.ifPresent(citizenInfoComuneDs1 -&gt; {</b>
<b class="fc">&nbsp;                citizenInfoDTO.setCommuneDateValid(DateUtils.convertToDate(citizenInfoComuneDs1.getDateValid()));</b>
<b class="pc">&nbsp;                citizenInfoDTO.setRvaCountryCode(citizenInfoComuneDs1.getRvaCountryCode() &gt; 9999 ? 1 : citizenInfoComuneDs1.getRvaCountryCode());</b>
&nbsp;            });
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    private void setMcpteDate(CitizenInfoV2DTO citizenInfoDTO, Integer dateMCpte) {
<b class="fc">&nbsp;        if (dateMCpte != null &amp;&amp; dateMCpte != 99999999) {</b>
<b class="fc">&nbsp;            log.debug(&quot;Citizen dateMCpte exists&quot;);</b>
<b class="fc">&nbsp;            citizenInfoDTO.setDateMcpte(DateUtils.convertToDate(dateMCpte));</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    private void setBasicInfo(CitizenInfoV2DTO citizenInfoDTO, CitizenInfoGeneral infoGeneral) {
<b class="fc">&nbsp;        log.debug(&quot;Setting citizen basic info&quot;);</b>
<b class="fc">&nbsp;        citizenInfoDTO.setAddress(infoGeneral.getAddress().trim());</b>
<b class="pc">&nbsp;        citizenInfoDTO.setPostalCode(infoGeneral.getZipCode() == null? null : Integer.toString(infoGeneral.getZipCode()));</b>
<b class="fc">&nbsp;        var nameParts = infoGeneral.getFullName().split(&quot;,&quot;);</b>
<b class="fc">&nbsp;        citizenInfoDTO.setLastName(nameParts[0].trim());</b>
<b class="fc">&nbsp;        citizenInfoDTO.setFirstName(nameParts[1].trim());</b>
<b class="fc">&nbsp;        log.debug(&quot;Citizen basic info setted&quot;);</b>
&nbsp;    }
&nbsp;
&nbsp;    private void setIbanInfo(CitizenInfoV2DTO citizenInfoDTO, CitizenInfoGeneral infoGeneral) {
<b class="fc">&nbsp;        citizenInfoDTO.setIban(infoGeneral.getIban() != null ? infoGeneral.getIban().trim() : &quot;&quot;);</b>
<b class="fc">&nbsp;        if (!infoGeneral.isFlagVCpte()) {</b>
<b class="fc">&nbsp;            log.debug(&quot;Citizen flagVCpte is 0&quot;);</b>
<b class="fc">&nbsp;            var iban = compteRepository.findFirstByParentIdAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(infoGeneral.getId(), 9, DateUtils.currentDate())</b>
<b class="fc">&nbsp;                    .map(CitizenInfoCompte::getIban)</b>
<b class="fc">&nbsp;                    .orElse(&quot;&quot;)</b>
<b class="fc">&nbsp;                    .trim();</b>
<b class="fc">&nbsp;            citizenInfoDTO.setIban(iban);</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    private void setSexAndLanguageInfo(CitizenInfoV2DTO citizenInfoDTO, CitizenInfoGeneral infoGeneral) {
<b class="fc">&nbsp;        citizenInfoDTO.setSex(SexDTO.fromValue(infoGeneral.getSex()).name());</b>
<b class="fc">&nbsp;        if (infoGeneral.getLanguage() != null) {</b>
<b class="fc">&nbsp;            citizenInfoDTO.setLanguage(LanguageDTO.fromValue(infoGeneral.getLanguage()).name());</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    private void setNationInfo(CitizenInfoV2DTO citizenInfoDTO, CitizenInfoGeneral infoGeneral, List&lt;CitizenInfoNation&gt; citizenInfoNation) {
&nbsp;
<b class="fc">&nbsp;        if (!citizenInfoNation.isEmpty()) {</b>
<b class="fc">&nbsp;            log.debug(&quot;Citizen nation info exists&quot;);</b>
&nbsp;
<b class="fc">&nbsp;            var citizenNation = citizenInfoNation.stream()</b>
<b class="fc">&nbsp;                    .filter(nationInfo -&gt; Objects.equals(nationInfo.getNumBox(), infoGeneral.getNumBox()))</b>
<b class="fc">&nbsp;                    .max(Comparator.comparingInt(CitizenInfoNation::getDateValid));</b>
&nbsp;
<b class="pc">&nbsp;            if (citizenNation.isPresent()) {</b>
<b class="fc">&nbsp;                log.debug(&quot;Setting citizen nation info from nation_ds&quot;);</b>
&nbsp;
<b class="fc">&nbsp;                var nationBcss = BigDecimal.valueOf(</b>
<b class="pc">&nbsp;                        citizenNation.get().getNation() == null ? nationalCitizenDefault : citizenNation.get().getNation()</b>
&nbsp;                );
&nbsp;
<b class="fc">&nbsp;                citizenInfoDTO.setNationBcss(nationBcss);</b>
<b class="fc">&nbsp;                citizenInfoDTO.setFlagNation(nationBcss);</b>
&nbsp;
<b class="fc">&nbsp;                citizenInfoDTO.setNationDateValid(</b>
<b class="fc">&nbsp;                        DateUtils.convertToDate(citizenNation.get().getDateValid())</b>
&nbsp;                );
&nbsp;                return;
&nbsp;            }
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        if (!infoGeneral.isFlagNation()) {</b>
<b class="fc">&nbsp;            log.debug(&quot;Citizen is Belgian based on flag_nation in general_ds&quot;);</b>
<b class="fc">&nbsp;            citizenInfoDTO.setNationBcss(null);</b>
<b class="fc">&nbsp;            citizenInfoDTO.setFlagNation(BigDecimal.valueOf(nationalCitizenDefault));</b>
&nbsp;        }
&nbsp;
&nbsp;    }
&nbsp;
&nbsp;    private void setCommunicationInfo(CitizenInfoV2DTO citizenInfoDTO, CitizenInfoGeneral infoGeneral, List&lt;CitizenInfoCommunic&gt; citizenInfoCommunics) {
<b class="fc">&nbsp;        log.debug(&quot;Citizen Info communication info exists&quot;);</b>
<b class="fc">&nbsp;        citizenInfoCommunics.stream()</b>
<b class="fc">&nbsp;                .filter(communic -&gt; Objects.equals(communic.getNumBox(), infoGeneral.getNumBox()))</b>
<b class="fc">&nbsp;                .findFirst()</b>
<b class="fc">&nbsp;                .ifPresent(citizenInfoCommunic -&gt; setCommunicInfoOld(citizenInfoDTO, citizenInfoCommunic));</b>
&nbsp;    }
&nbsp;
&nbsp;    private void setBisNumbers(CitizenInfoV2DTO citizenInfoDTO, Map&lt;Integer,List&lt;String&gt;&gt; bisNumbers) {
<b class="fc">&nbsp;        var inssHistory = bisNumbers.get(citizenInfoDTO.getNumBox().intValue());</b>
<b class="pc">&nbsp;        if (inssHistory != null &amp;&amp; !inssHistory.isEmpty()) {</b>
<b class="nc">&nbsp;            log.debug(&quot;Citizen Info ssin history found&quot;);</b>
<b class="nc">&nbsp;            citizenInfoDTO.setBisNumber(inssHistory);</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    private void setDeceasedDate(CitizenInfoV2DTO citizenInfoDTO, Integer deceasedDate) {
<b class="fc">&nbsp;        citizenInfoDTO.setDeceasedDate(null);</b>
<b class="pc">&nbsp;        if (deceasedDate != null &amp;&amp; deceasedDate != 99999999) {</b>
<b class="fc">&nbsp;            log.debug(&quot;Citizen Info deceased date exists&quot;);</b>
<b class="fc">&nbsp;            citizenInfoDTO.setDeceasedDate(DateUtils.convertToDate(deceasedDate));</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    private void clearCitizenInfo(CitizenInfoV2DTO citizenInfoDTO) {
<b class="fc">&nbsp;        log.info(&quot;No citizen info found for ssin. Building empty citizen info&quot;);</b>
<b class="fc">&nbsp;        citizenInfoDTO.setAddress(null);</b>
<b class="fc">&nbsp;        citizenInfoDTO.setLastName(null);</b>
<b class="fc">&nbsp;        citizenInfoDTO.setFirstName(null);</b>
<b class="fc">&nbsp;        citizenInfoDTO.setDeceasedDate(null);</b>
<b class="fc">&nbsp;        citizenInfoDTO.setIban(null);</b>
<b class="fc">&nbsp;        citizenInfoDTO.setUnemploymentOffice(null);</b>
<b class="fc">&nbsp;        citizenInfoDTO.setOP(null);</b>
&nbsp;    }
&nbsp;
&nbsp;    private static void setCommunicInfoOld(CitizenInfoV2DTO el, CitizenInfoCommunic citizenInfoCommunic) {
<b class="fc">&nbsp;        if(citizenInfoCommunic.getEmail()!= null){</b>
<b class="fc">&nbsp;            log.debug(&quot;Citizen Info email exists&quot;);</b>
<b class="fc">&nbsp;            el.setEmail(citizenInfoCommunic.getEmail().trim());</b>
&nbsp;        }
<b class="fc">&nbsp;        if(citizenInfoCommunic.getTelephoneOnem()!= null){</b>
<b class="fc">&nbsp;            log.debug(&quot;Citizen Info telephoneOnem exists&quot;);</b>
<b class="fc">&nbsp;            el.setTelephoneOnem(citizenInfoCommunic.getTelephoneOnem().trim());</b>
&nbsp;        }
<b class="fc">&nbsp;        if(citizenInfoCommunic.getTelephoneReg()!= null){</b>
<b class="fc">&nbsp;            log.debug(&quot;Citizen Info telephoneReg exists&quot;);</b>
<b class="fc">&nbsp;            el.setTelephoneReg(citizenInfoCommunic.getTelephoneReg().trim());</b>
&nbsp;        }
<b class="fc">&nbsp;        if(citizenInfoCommunic.getGsmOnem()!= null){</b>
<b class="fc">&nbsp;            log.debug(&quot;Citizen Info gsmOnem exists&quot;);</b>
<b class="fc">&nbsp;            el.setGsmOnem(citizenInfoCommunic.getGsmOnem().trim());</b>
&nbsp;        }
<b class="fc">&nbsp;        if(citizenInfoCommunic.getGsmReg()!= null){</b>
<b class="fc">&nbsp;            log.debug(&quot;Citizen Info gsmReg exists&quot;);</b>
<b class="fc">&nbsp;            el.setGsmReg(citizenInfoCommunic.getGsmReg().trim());</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    private Page&lt;CitizenInfoEntity&gt; getCitizenInfoOld(List&lt;String&gt; ssin, Pageable pageable) {
<b class="fc">&nbsp;        var numpens = ssin.stream().map(el -&gt; PensionNumberUtils.convertFromInss(Long.valueOf(el))).toList();</b>
<b class="fc">&nbsp;        log.debug(&quot;NumPens V2 {}&quot;,numpens);</b>
<b class="fc">&nbsp;        var infos =  infoRepository.getCitizenInfoEntitiesByNumPensInAndLastIdEquals(numpens, 9, pageable);</b>
<b class="pc">&nbsp;        log.debug(&quot;Get citizen info by numpens found: {} quantity&quot;, (infos != null &amp;&amp; infos.get() != null) ? infos.get().collect(Collectors.toList()).size() : 0);</b>
<b class="fc">&nbsp;        return infos;</b>
&nbsp;    }
&nbsp;
&nbsp;    private Map&lt;Integer,List&lt;String&gt;&gt; getBisNumbers(List&lt;Integer&gt; box) {
<b class="fc">&nbsp;        return infoRepository.findByNumBoxIn(box).stream()</b>
<b class="pc">&nbsp;                .filter(info -&gt; info.getLastId() != 9)</b>
<b class="fc">&nbsp;                .collect(Collectors.groupingBy(</b>
&nbsp;                        CitizenInfoEntity::getNumBox,
<b class="fc">&nbsp;                        Collectors.mapping(info -&gt; String.format(&quot;%011d&quot;, PensionNumberUtils.convertToInssWithDefault(info.getNumPens())), Collectors.toList())</b>
&nbsp;                ));
&nbsp;    }
&nbsp;
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
