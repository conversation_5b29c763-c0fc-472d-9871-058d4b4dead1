


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > StringUtils</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.util</a>
</div>

<h1>Coverage Summary for Class: StringUtils (be.fgov.onerva.person.backend.util)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">StringUtils</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (4/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    90%
  </span>
  <span class="absValue">
    (9/10)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (15/15)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.util;
&nbsp;
&nbsp;import static org.apache.commons.lang3.StringUtils.*;
&nbsp;
&nbsp;import java.util.Locale;
&nbsp;
&nbsp;/**
&nbsp; * Utility class for string manipulation specific to mainframe formatting needs.
&nbsp; */
&nbsp;public class StringUtils {
&nbsp;    /**
&nbsp;     * Private constructor to prevent instantiation.
&nbsp;     */
&nbsp;    private StringUtils() {
&nbsp;        super();
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Truncate the input string to the specified length and pad it with spaces on
&nbsp;     * the right.
&nbsp;     * This creates a left-aligned field of exact length.
&nbsp;     *
&nbsp;     * @param input  the input string, coerced to an empty string if null
&nbsp;     * @param length the length to truncate/pad to
&nbsp;     * @return the truncated and right-padded string of exact length
&nbsp;     */
&nbsp;    public static String truncateAndPadRight(String input, int length) {
<b class="fc">&nbsp;        return rightPad(truncate(input != null ? input : &quot;&quot;, length), length);</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Truncate the input string to the specified length and pad it with spaces on
&nbsp;     * the left.
&nbsp;     * This creates a right-aligned field of exact length.
&nbsp;     *
&nbsp;     * @param input  the input string, coerced to an empty string if null
&nbsp;     * @param length the length to truncate/pad to
&nbsp;     * @return the truncated and left-padded string of exact length
&nbsp;     */
&nbsp;    public static String truncateAndPadLeft(String input, int length) {
<b class="pc">&nbsp;        return leftPad(truncate(input != null ? input : &quot;&quot;, length), length);</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Truncate the input string to the specified length and pad it with the
&nbsp;     * specified character on the left.
&nbsp;     * This creates a right-aligned field of exact length with custom padding
&nbsp;     * character.
&nbsp;     *
&nbsp;     * @param input     the input string, coerced to an empty string if null
&nbsp;     * @param length    the length to truncate/pad to
&nbsp;     * @param character the character to use for padding
&nbsp;     * @return the truncated and left-padded string of exact length
&nbsp;     */
&nbsp;    public static String truncateAndPadLeft(String input, int length, char character) {
<b class="fc">&nbsp;        if (input == null) {</b>
<b class="fc">&nbsp;            return leftPad(&quot;&quot;, length, character);</b>
&nbsp;        }
<b class="fc">&nbsp;        return leftPad(truncate(input, length), length, character);</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Converts text to uppercase with accent stripping and length management.
&nbsp;     * This is useful for mainframe formatting where accented characters are not
&nbsp;     * supported.
&nbsp;     *
&nbsp;     * @param text      the text to convert, coerced to an empty string if null
&nbsp;     * @param maxLength the maximum length to truncate to
&nbsp;     * @return the uppercase, accent-stripped, and truncated string
&nbsp;     */
&nbsp;    public static String toUpperClean(String text, int maxLength) {
<b class="fc">&nbsp;        if (text == null) {</b>
<b class="fc">&nbsp;            return &quot;&quot;;</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        String trimmed = text.trim();</b>
<b class="fc">&nbsp;        String formatted = trimmed.length() &gt; maxLength ? trimmed.substring(0, maxLength) : trimmed;</b>
&nbsp;
&nbsp;        // First convert to uppercase
<b class="fc">&nbsp;        String upperCase = formatted.toUpperCase(Locale.ROOT);</b>
&nbsp;
&nbsp;        // Handle special cases for French/Dutch characters
<b class="fc">&nbsp;        upperCase = upperCase</b>
<b class="fc">&nbsp;                .replace(&quot;BOÎTE&quot;, &quot;BOITE&quot;)</b>
<b class="fc">&nbsp;                .replace(&quot;BOITE&quot;, &quot;BTE&quot;);</b>
&nbsp;
&nbsp;        // Use StringUtils.stripAccents for general accent stripping
<b class="fc">&nbsp;        String result = org.apache.commons.lang3.StringUtils.stripAccents(upperCase);</b>
&nbsp;
&nbsp;        // Replace any remaining non-ASCII characters with their ASCII equivalents
<b class="fc">&nbsp;        return result.replaceAll(&quot;[^\\p{ASCII}]&quot;, &quot;&quot;);</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
