


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > QueueMainframeE2EController</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.e2e</a>
</div>

<h1>Coverage Summary for Class: QueueMainframeE2EController (be.fgov.onerva.person.backend.e2e)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">QueueMainframeE2EController</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/19)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.e2e;
&nbsp;
&nbsp;import be.fgov.onerva.person.backend.config.HttpClientConfig;
&nbsp;import be.fgov.onerva.person.backend.request.model.PersonRequest;
&nbsp;import be.fgov.onerva.person.backend.request.model.PersonRequestType;
&nbsp;import be.fgov.onerva.person.backend.request.persistence.PersonRequestRepository;
&nbsp;import org.springframework.beans.factory.annotation.Autowired;
&nbsp;import org.springframework.context.annotation.Profile;
&nbsp;import org.springframework.http.ResponseEntity;
&nbsp;import org.springframework.jms.core.JmsTemplate;
&nbsp;import org.springframework.transaction.annotation.Transactional;
&nbsp;import org.springframework.web.bind.annotation.PostMapping;
&nbsp;import org.springframework.web.bind.annotation.RequestMapping;
&nbsp;import org.springframework.web.bind.annotation.RestController;
&nbsp;
&nbsp;import java.time.LocalDateTime;
&nbsp;
&nbsp;@RestController
&nbsp;@Profile(HttpClientConfig.DEV_PROFILE)
&nbsp;@RequestMapping(&quot;/e2e&quot;)
<b class="nc">&nbsp;public class QueueMainframeE2EController {</b>
&nbsp;
&nbsp;    public static final String SSIN = &quot;99062404991&quot;;
&nbsp;    @Autowired
&nbsp;    private JmsTemplate jmsTemplate;
&nbsp;
&nbsp;    @Autowired
&nbsp;    private PersonRequestRepository personRequestRepository;
&nbsp;
&nbsp;    @Transactional
&nbsp;    @PostMapping(&quot;/person/created&quot;)
&nbsp;    public ResponseEntity&lt;Void&gt; postCitizenCreatedInQueueOut() throws InterruptedException {
<b class="nc">&nbsp;        var personRequest = personRequestRepository.saveAndFlush(createPersonRequest());</b>
<b class="nc">&nbsp;        jmsTemplate.convertAndSend(&quot;DOE,JANE                      700101002870000000000000000&quot; + convertPersonId(personRequest.getId()) + &quot;+0001&quot;);</b>
<b class="nc">&nbsp;        Thread.sleep(3000);</b>
<b class="nc">&nbsp;        return ResponseEntity.noContent().build();</b>
&nbsp;    }
&nbsp;
&nbsp;    private String convertPersonId(long personId) {
<b class="nc">&nbsp;        if (personId &lt; 10) {</b>
<b class="nc">&nbsp;            return &quot;00&quot; + personId;</b>
&nbsp;        }
<b class="nc">&nbsp;        if (personId &lt; 100) {</b>
<b class="nc">&nbsp;            return &quot;0&quot; + personId;</b>
&nbsp;        }
<b class="nc">&nbsp;        return &quot;&quot; + personId;</b>
&nbsp;    }
&nbsp;
&nbsp;    private PersonRequest createPersonRequest() {
<b class="nc">&nbsp;        return PersonRequest.builder()</b>
<b class="nc">&nbsp;                .type(PersonRequestType.CREATE)</b>
<b class="nc">&nbsp;                .created(LocalDateTime.now())</b>
<b class="nc">&nbsp;                .niss(SSIN)</b>
<b class="nc">&nbsp;                .firstname(&quot;John&quot;)</b>
<b class="nc">&nbsp;                .lastname(&quot;Doe&quot;)</b>
<b class="nc">&nbsp;                .sent(true)</b>
<b class="nc">&nbsp;                .updated(LocalDateTime.now())</b>
<b class="nc">&nbsp;                .build();</b>
&nbsp;    }
&nbsp;
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
