


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenInfoUnionDueDTO</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">backend.rest.model</a>
</div>

<h1>Coverage Summary for Class: CitizenInfoUnionDueDTO (backend.rest.model)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">CitizenInfoUnionDueDTO</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    58.3%
  </span>
  <span class="absValue">
    (7/12)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    40%
  </span>
  <span class="absValue">
    (4/10)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    44.8%
  </span>
  <span class="absValue">
    (13/29)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package backend.rest.model;
&nbsp;
&nbsp;import java.net.URI;
&nbsp;import java.util.Objects;
&nbsp;import com.fasterxml.jackson.annotation.JsonProperty;
&nbsp;import com.fasterxml.jackson.annotation.JsonCreator;
&nbsp;import com.fasterxml.jackson.annotation.JsonTypeName;
&nbsp;import java.time.LocalDate;
&nbsp;import org.springframework.format.annotation.DateTimeFormat;
&nbsp;import org.springframework.lang.Nullable;
&nbsp;import java.time.OffsetDateTime;
&nbsp;import jakarta.validation.Valid;
&nbsp;import jakarta.validation.constraints.*;
&nbsp;import org.hibernate.validator.constraints.*;
&nbsp;import io.swagger.v3.oas.annotations.media.Schema;
&nbsp;
&nbsp;
&nbsp;import java.util.*;
&nbsp;import jakarta.annotation.Generated;
&nbsp;
&nbsp;/**
&nbsp; * The citizen mandates or not the union to pay the union dues with his allowance money. If no mandate was ever found this will be null.
&nbsp; */
&nbsp;
&nbsp;@Schema(name = &quot;CitizenInfo_unionDue&quot;, description = &quot;The citizen mandates or not the union to pay the union dues with his allowance money. If no mandate was ever found this will be null.&quot;)
&nbsp;@JsonTypeName(&quot;CitizenInfo_unionDue&quot;)
&nbsp;@Generated(value = &quot;org.openapitools.codegen.languages.SpringCodegen&quot;, date = &quot;2025-07-16T14:11:30.627767400+02:00[Europe/Brussels]&quot;, comments = &quot;Generator version: 7.12.0&quot;)
&nbsp;public class CitizenInfoUnionDueDTO {
&nbsp;
&nbsp;  private Boolean mandateActive;
&nbsp;
&nbsp;  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
&nbsp;  private LocalDate validFrom;
&nbsp;
&nbsp;  public CitizenInfoUnionDueDTO() {
<b class="fc">&nbsp;    super();</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Constructor with only required parameters
&nbsp;   */
<b class="fc">&nbsp;  public CitizenInfoUnionDueDTO(Boolean mandateActive, LocalDate validFrom) {</b>
<b class="fc">&nbsp;    this.mandateActive = mandateActive;</b>
<b class="fc">&nbsp;    this.validFrom = validFrom;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoUnionDueDTO mandateActive(Boolean mandateActive) {
<b class="nc">&nbsp;    this.mandateActive = mandateActive;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Is the union dues mandate active or not.
&nbsp;   * @return mandateActive
&nbsp;   */
&nbsp;  @NotNull 
&nbsp;  @Schema(name = &quot;mandateActive&quot;, description = &quot;Is the union dues mandate active or not.&quot;, requiredMode = Schema.RequiredMode.REQUIRED)
&nbsp;  @JsonProperty(&quot;mandateActive&quot;)
&nbsp;  public Boolean getMandateActive() {
<b class="fc">&nbsp;    return mandateActive;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setMandateActive(Boolean mandateActive) {
<b class="fc">&nbsp;    this.mandateActive = mandateActive;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenInfoUnionDueDTO validFrom(LocalDate validFrom) {
<b class="nc">&nbsp;    this.validFrom = validFrom;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * The date on which the mandate was activated or deactivated.
&nbsp;   * @return validFrom
&nbsp;   */
&nbsp;  @NotNull @Valid 
&nbsp;  @Schema(name = &quot;validFrom&quot;, description = &quot;The date on which the mandate was activated or deactivated.&quot;, requiredMode = Schema.RequiredMode.REQUIRED)
&nbsp;  @JsonProperty(&quot;validFrom&quot;)
&nbsp;  public LocalDate getValidFrom() {
<b class="fc">&nbsp;    return validFrom;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setValidFrom(LocalDate validFrom) {
<b class="fc">&nbsp;    this.validFrom = validFrom;</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public boolean equals(Object o) {
<b class="pc">&nbsp;    if (this == o) {</b>
<b class="nc">&nbsp;      return true;</b>
&nbsp;    }
<b class="pc">&nbsp;    if (o == null || getClass() != o.getClass()) {</b>
<b class="nc">&nbsp;      return false;</b>
&nbsp;    }
<b class="fc">&nbsp;    CitizenInfoUnionDueDTO citizenInfoUnionDue = (CitizenInfoUnionDueDTO) o;</b>
<b class="pc">&nbsp;    return Objects.equals(this.mandateActive, citizenInfoUnionDue.mandateActive) &amp;&amp;</b>
<b class="fc">&nbsp;        Objects.equals(this.validFrom, citizenInfoUnionDue.validFrom);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public int hashCode() {
<b class="nc">&nbsp;    return Objects.hash(mandateActive, validFrom);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public String toString() {
<b class="nc">&nbsp;    StringBuilder sb = new StringBuilder();</b>
<b class="nc">&nbsp;    sb.append(&quot;class CitizenInfoUnionDueDTO {\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    mandateActive: &quot;).append(toIndentedString(mandateActive)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    validFrom: &quot;).append(toIndentedString(validFrom)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;}&quot;);</b>
<b class="nc">&nbsp;    return sb.toString();</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Convert the given object to string with each line indented by 4 spaces
&nbsp;   * (except the first line).
&nbsp;   */
&nbsp;  private String toIndentedString(Object o) {
<b class="nc">&nbsp;    if (o == null) {</b>
<b class="nc">&nbsp;      return &quot;null&quot;;</b>
&nbsp;    }
<b class="nc">&nbsp;    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</b>
&nbsp;  }
&nbsp;}
&nbsp;
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
