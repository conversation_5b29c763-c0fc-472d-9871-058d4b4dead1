


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > AmqpBrokerService</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.request.broker</a>
</div>

<h1>Coverage Summary for Class: AmqpBrokerService (be.fgov.onerva.person.backend.request.broker)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">AmqpBrokerService</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (4/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    70%
  </span>
  <span class="absValue">
    (7/10)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    73.9%
  </span>
  <span class="absValue">
    (17/23)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.request.broker;
&nbsp;
&nbsp;import com.fasterxml.jackson.core.JsonProcessingException;
&nbsp;import com.fasterxml.jackson.databind.JsonNode;
&nbsp;import com.fasterxml.jackson.databind.ObjectMapper;
&nbsp;import com.fasterxml.jackson.databind.node.ObjectNode;
&nbsp;import lombok.RequiredArgsConstructor;
&nbsp;import lombok.extern.slf4j.Slf4j;
&nbsp;import org.springframework.amqp.core.AmqpTemplate;
&nbsp;import org.springframework.stereotype.Service;
&nbsp;
&nbsp;import java.util.Iterator;
&nbsp;import java.util.Map;
&nbsp;
&nbsp;@Service
&nbsp;@RequiredArgsConstructor
<b class="fc">&nbsp;@Slf4j</b>
&nbsp;public class AmqpBrokerService implements BrokerService {
&nbsp;
&nbsp;    private final AmqpTemplate amqpTemplate;
&nbsp;
&nbsp;    @Override
&nbsp;    public void convertAndSend(Object message) {
<b class="fc">&nbsp;        log.debug(&quot;Sent : {}&quot;, sanitizeLogMessage(message));</b>
&nbsp;        try {
<b class="fc">&nbsp;            amqpTemplate.convertAndSend(message);</b>
&nbsp;        } catch (Exception e) {
<b class="nc">&nbsp;            log.error(e.getMessage(), e);</b>
<b class="nc">&nbsp;            throw new RuntimeException(e);</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    private String sanitizeLogMessage(Object msg) {
<b class="fc">&nbsp;        ObjectMapper objectMapper = new ObjectMapper();</b>
&nbsp;        try {
&nbsp;            // convert object to JSON
<b class="fc">&nbsp;            String jsonString = objectMapper.writeValueAsString(msg);</b>
&nbsp;
&nbsp;            // parse JSON
<b class="fc">&nbsp;            JsonNode jsonNode = objectMapper.readTree(jsonString);</b>
&nbsp;
&nbsp;            // traverse tree, replace ssin value
<b class="fc">&nbsp;            JsonNode alteredJsonNode = traverseAndObfuscate(jsonNode);</b>
&nbsp;
&nbsp;            // convert back to JSON string for logging
<b class="fc">&nbsp;            return objectMapper.writeValueAsString(alteredJsonNode);</b>
&nbsp;
&nbsp;        } catch (JsonProcessingException e) {
<b class="nc">&nbsp;            log.error(e.getMessage(), e);</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        return msg.toString();</b>
&nbsp;    }
&nbsp;
&nbsp;    private JsonNode traverseAndObfuscate(JsonNode node) {
<b class="fc">&nbsp;        if (node.isObject()) {</b>
<b class="fc">&nbsp;            Iterator&lt;Map.Entry&lt;String,JsonNode&gt;&gt; iter = node.fields();</b>
<b class="fc">&nbsp;            while (iter.hasNext()) {</b>
<b class="fc">&nbsp;                Map.Entry&lt;String,JsonNode&gt; entry = iter.next();</b>
<b class="fc">&nbsp;                if(entry.getKey().toLowerCase().equals(&quot;ssin&quot;)) {</b>
<b class="fc">&nbsp;                    ((ObjectNode) node).put(entry.getKey(), &quot;REDACTED&quot;);</b>
&nbsp;                } else {
<b class="fc">&nbsp;                    traverseAndObfuscate(entry.getValue());</b>
&nbsp;                }
&nbsp;            }
<b class="pc">&nbsp;        } else if (node.isArray()) {</b>
<b class="nc">&nbsp;            for (JsonNode arrayItem : node) {</b>
<b class="nc">&nbsp;                traverseAndObfuscate(arrayItem);</b>
&nbsp;            }
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        return node;</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
