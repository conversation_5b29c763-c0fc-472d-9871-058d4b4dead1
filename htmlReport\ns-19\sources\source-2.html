


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > PersonRequestListener</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.request.service</a>
</div>

<h1>Coverage Summary for Class: PersonRequestListener (be.fgov.onerva.person.backend.request.service)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">PersonRequestListener</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (8/8)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    75%
  </span>
  <span class="absValue">
    (12/16)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    87%
  </span>
  <span class="absValue">
    (47/54)
  </span>
</td>
</tr>
  <tr>
    <td class="name">PersonRequestListener$$SpringCGLIB$$0</td>
  </tr>
  <tr>
    <td class="name">PersonRequestListener$1</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (9/9)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    75%
  </span>
  <span class="absValue">
    (12/16)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    87.3%
  </span>
  <span class="absValue">
    (48/55)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.request.service;
&nbsp;
&nbsp;import be.fgov.onerva.common.utils.PensionNumberUtils;
&nbsp;import be.fgov.onerva.person.backend.citizen.model.CitizenEntity;
&nbsp;import be.fgov.onerva.person.backend.citizen.persistence.CitizenRepository;
&nbsp;import be.fgov.onerva.person.backend.featureflags.FeatureFlagsService;
&nbsp;import be.fgov.onerva.person.backend.request.broker.BrokerService;
&nbsp;import be.fgov.onerva.person.backend.request.event.PersonRequestEvent;
&nbsp;import be.fgov.onerva.person.backend.request.formatter.MainframeUpdateMessageFormatter;
&nbsp;import be.fgov.onerva.person.backend.request.mapper.PersonEventMapper;
&nbsp;import be.fgov.onerva.person.backend.request.model.PersonMfxResponse;
&nbsp;import be.fgov.onerva.person.backend.request.model.PersonRequest;
&nbsp;import be.fgov.onerva.person.backend.request.model.PersonRequestType;
&nbsp;import be.fgov.onerva.person.backend.request.persistence.PersonRequestRepository;
&nbsp;import jakarta.jms.JMSException;
&nbsp;import jakarta.jms.TextMessage;
&nbsp;import lombok.extern.slf4j.Slf4j;
&nbsp;import org.apache.commons.lang3.exception.ExceptionUtils;
&nbsp;import org.mapstruct.factory.Mappers;
&nbsp;import org.springframework.jms.JmsException;
&nbsp;import org.springframework.jms.annotation.JmsListener;
&nbsp;import org.springframework.jms.core.JmsTemplate;
&nbsp;import org.springframework.scheduling.annotation.Scheduled;
&nbsp;import org.springframework.stereotype.Component;
&nbsp;import org.springframework.transaction.annotation.Propagation;
&nbsp;import org.springframework.transaction.annotation.Transactional;
&nbsp;import org.springframework.transaction.event.TransactionalEventListener;
&nbsp;
&nbsp;import java.time.LocalDateTime;
&nbsp;import java.util.Optional;
&nbsp;import java.util.concurrent.TimeUnit;
&nbsp;
&nbsp;import static be.fgov.onerva.person.backend.request.model.PersonRequest.ERROR_LENGTH;
&nbsp;
<b class="fc">&nbsp;@Slf4j</b>
&nbsp;@Component
&nbsp;class PersonRequestListener {
&nbsp;
&nbsp;    private final JmsTemplate jmsTemplate;
&nbsp;    private final PersonRequestRepository repository;
&nbsp;    private final CitizenRepository citizenRepository;
&nbsp;    private final BrokerService brokerService;
&nbsp;    private final PersonEventMapper personEventMapper;
&nbsp;    private final MainframeUpdateMessageFormatter messageFormatter;
&nbsp;
&nbsp;    public PersonRequestListener(JmsTemplate jmsTemplate,
&nbsp;            PersonRequestRepository repository,
&nbsp;            CitizenRepository citizenRepository,
&nbsp;            BrokerService brokerService,
<b class="fc">&nbsp;            MainframeUpdateMessageFormatter messageFormatter) {</b>
<b class="fc">&nbsp;        this.jmsTemplate = jmsTemplate;</b>
<b class="fc">&nbsp;        this.repository = repository;</b>
<b class="fc">&nbsp;        this.citizenRepository = citizenRepository;</b>
<b class="fc">&nbsp;        this.brokerService = brokerService;</b>
<b class="fc">&nbsp;        this.messageFormatter = messageFormatter;</b>
<b class="fc">&nbsp;        this.personEventMapper = Mappers.getMapper(PersonEventMapper.class);</b>
&nbsp;    }
&nbsp;
&nbsp;    @TransactionalEventListener
&nbsp;    @Transactional(transactionManager = &quot;personTransactionManager&quot;, propagation = Propagation.REQUIRES_NEW)
&nbsp;    public void handlePersonRequestEvent(PersonRequestEvent event) {
<b class="fc">&nbsp;        PersonRequest request = event.getPersonRequest();</b>
<b class="fc">&nbsp;        long id = request.getId();</b>
&nbsp;
&nbsp;        // Use MainframeUpdateMessageFormatter for UPDATE messages
&nbsp;        String payload;
<b class="fc">&nbsp;        if (request.getType() == PersonRequestType.UPDATE) {</b>
<b class="fc">&nbsp;            payload = messageFormatter.formatUpdateMessage(request);</b>
&nbsp;        } else {
<b class="fc">&nbsp;            payload = request.toRecord();</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        log.debug(&quot;Handle request #{} with payload: {}&quot;, id, payload);</b>
&nbsp;
&nbsp;        try {
<b class="fc">&nbsp;            jmsTemplate.convertAndSend(payload);</b>
<b class="fc">&nbsp;            repository.markAsSent(id, LocalDateTime.now());</b>
&nbsp;        } catch (JmsException e) {
<b class="fc">&nbsp;            log.error(&quot;Could not send request with #id &quot; + id + &quot; and payload : &quot; + payload, e);</b>
<b class="fc">&nbsp;            repository.updateStatus(id, null, getErrorMessage(e), LocalDateTime.now());</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    String getErrorMessage(Exception e) {
<b class="fc">&nbsp;        String msg = ExceptionUtils.getRootCauseMessage(e);</b>
<b class="fc">&nbsp;        return msg.length() &gt; ERROR_LENGTH ? msg.substring(0, ERROR_LENGTH) : msg;</b>
&nbsp;    }
&nbsp;
&nbsp;    @Transactional(&quot;personTransactionManager&quot;)
&nbsp;    @JmsListener(destination = &quot;${queue.out}&quot;, selector = &quot;${queue.selector:}&quot;)
&nbsp;    public void handleReply(TextMessage reply) {
&nbsp;        try {
<b class="fc">&nbsp;            String payload = reply.getText();</b>
<b class="fc">&nbsp;            log.debug(&quot;Reply from MainFrame - Payload: {}&quot;, payload);</b>
&nbsp;
<b class="fc">&nbsp;            var response = PersonMfxResponse.from(payload);</b>
<b class="fc">&nbsp;            long id = response.getId();</b>
&nbsp;
<b class="fc">&nbsp;            int rows = repository.updateStatus(id, response.getErrorCode(), response.getErrorMessage(),</b>
<b class="fc">&nbsp;                    LocalDateTime.now());</b>
&nbsp;
<b class="fc">&nbsp;            if (rows != 1) {</b>
<b class="fc">&nbsp;                log.error(&quot;Affected rows: {} of record: {}&quot;, rows, payload);</b>
<b class="fc">&nbsp;                throw new IllegalArgumentException(&quot;Could not update return code of PersonRequest with id: &quot; + id);</b>
&nbsp;            }
<b class="fc">&nbsp;            var request = repository.findById(id).orElseThrow();</b>
&nbsp;            // For defense purpose
<b class="pc">&nbsp;            if (response.getType() == PersonRequestType.CREATE &amp;&amp; response.isSuccess()) {</b>
<b class="fc">&nbsp;                var numpens = PensionNumberUtils.convertFromInss(Long.parseLong(request.getNiss()));</b>
<b class="fc">&nbsp;                waitForCitizenToBeActuallyPersisted(numpens);</b>
&nbsp;            }
&nbsp;            // End of defense purpose
<b class="pc">&nbsp;            Object message = switch (request.getType()) {</b>
<b class="fc">&nbsp;                case CREATE -&gt; personEventMapper.mapToCloudEventCreate(request, response);</b>
<b class="fc">&nbsp;                case UPDATE -&gt; personEventMapper.mapToCloudEventUpdate(request, response);</b>
&nbsp;            };
<b class="fc">&nbsp;            brokerService.convertAndSend(message);</b>
&nbsp;        } catch (JMSException e) {
<b class="nc">&nbsp;            log.error(&quot;Could not read message!&quot;, e);</b>
<b class="nc">&nbsp;            throw new RuntimeException(e);</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    @Transactional(&quot;personTransactionManager&quot;)
&nbsp;    @Scheduled(fixedRateString = &quot;${queue.retryInMinutes:10}&quot;, timeUnit = TimeUnit.MINUTES)
&nbsp;    public void retryFailedRequests() {
<b class="fc">&nbsp;        repository.findFirst10BySentFalseOrderByCreatedAsc()</b>
<b class="fc">&nbsp;                .forEach(this::retry);</b>
&nbsp;    }
&nbsp;
&nbsp;    @Transactional(value = &quot;personTransactionManager&quot;, propagation = Propagation.REQUIRES_NEW)
&nbsp;    public void retry(PersonRequest personRequest) {
<b class="fc">&nbsp;        personRequest.incrementRetryCount();</b>
<b class="fc">&nbsp;        long id = personRequest.getId();</b>
&nbsp;
&nbsp;        // Use MainframeUpdateMessageFormatter for UPDATE messages
&nbsp;        String payload;
<b class="pc">&nbsp;        if (personRequest.getType() == PersonRequestType.UPDATE) {</b>
<b class="nc">&nbsp;            payload = messageFormatter.formatUpdateMessage(personRequest);</b>
&nbsp;        } else {
<b class="fc">&nbsp;            payload = personRequest.toRecord();</b>
&nbsp;        }
&nbsp;
&nbsp;        try {
<b class="fc">&nbsp;            jmsTemplate.convertAndSend(payload);</b>
<b class="fc">&nbsp;            personRequest.markAsSent();</b>
&nbsp;        } catch (RuntimeException e) {
<b class="nc">&nbsp;            log.error(&quot;Could not send request with #id &quot; + id + &quot; and payload : &quot; + payload, e);</b>
<b class="nc">&nbsp;            personRequest.logError(getErrorMessage(e));</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    private void waitForCitizenToBeActuallyPersisted(Integer numpens) {
&nbsp;        Optional&lt;CitizenEntity&gt; citizenEntity;
<b class="pc">&nbsp;        while ((citizenEntity = citizenRepository.findById(numpens)).isEmpty()) {</b>
&nbsp;            // log.debug(&quot;Citizen searched with numpens {} and not found.&quot;, numpens);
&nbsp;            try {
<b class="nc">&nbsp;                Thread.sleep(500);</b>
&nbsp;            } catch (InterruptedException e) {
<b class="nc">&nbsp;                Thread.currentThread().interrupt();</b>
&nbsp;            }
&nbsp;        }
<b class="fc">&nbsp;        log.debug(&quot;Citizen found for numpens {}: {}&quot;, numpens, citizenEntity.get());</b>
&nbsp;    }
&nbsp;
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
