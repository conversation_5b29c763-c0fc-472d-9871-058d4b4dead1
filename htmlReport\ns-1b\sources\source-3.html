


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > NationalityCodeValidator</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.validation</a>
</div>

<h1>Coverage Summary for Class: NationalityCodeValidator (be.fgov.onerva.person.backend.validation)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">NationalityCodeValidator</td>
<td class="coverageStat">
  <span class="percent">
    75%
  </span>
  <span class="absValue">
    (3/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    10%
  </span>
  <span class="absValue">
    (1/10)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    42.3%
  </span>
  <span class="absValue">
    (11/26)
  </span>
</td>
</tr>
  <tr>
    <td class="name">NationalityCodeValidator$ValidationResult</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/6)
  </span>
</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    37.5%
  </span>
  <span class="absValue">
    (3/8)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    10%
  </span>
  <span class="absValue">
    (1/10)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    34.4%
  </span>
  <span class="absValue">
    (11/32)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.validation;
&nbsp;
&nbsp;import be.fgov.onerva.person.backend.lookup.LookupClient;
&nbsp;import be.fgov.onerva.person.backend.lookup.model.LookupData;
&nbsp;import lombok.RequiredArgsConstructor;
&nbsp;import lombok.extern.slf4j.Slf4j;
&nbsp;import org.springframework.cache.annotation.Cacheable;
&nbsp;import org.springframework.stereotype.Component;
&nbsp;
&nbsp;import java.util.List;
&nbsp;import java.util.Set;
&nbsp;import java.util.stream.Collectors;
&nbsp;
&nbsp;/**
&nbsp; * Nationality code validator that uses the LookupClient to verify nationality codes
&nbsp; * against the CBSS nationality codes lookup service.
&nbsp; */
&nbsp;@Component
&nbsp;@RequiredArgsConstructor
<b class="fc">&nbsp;@Slf4j</b>
&nbsp;public class NationalityCodeValidator {
&nbsp;
&nbsp;    private final LookupClient lookupClient;
&nbsp;
&nbsp;    /**
&nbsp;     * Validates a nationality code against the CBSS nationality codes lookup service.
&nbsp;     *
&nbsp;     * @param nationalityCode the nationality code to validate (can be null)
&nbsp;     * @return true if the nationality code is valid, false otherwise
&nbsp;     */
&nbsp;    public boolean isValid(Integer nationalityCode) {
<b class="pc">&nbsp;        if (nationalityCode == null) {</b>
<b class="nc">&nbsp;            return false;</b>
&nbsp;        }
&nbsp;
&nbsp;        try {
<b class="fc">&nbsp;            Set&lt;String&gt; validCodes = getValidNationalityCodes();</b>
<b class="fc">&nbsp;            return validCodes.contains(nationalityCode.toString());</b>
&nbsp;        } catch (Exception e) {
<b class="nc">&nbsp;            log.error(&quot;Error validating nationality code {}: {}&quot;, nationalityCode, e.getMessage());</b>
<b class="nc">&nbsp;            return false;</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Validates a nationality code and provides detailed error information.
&nbsp;     *
&nbsp;     * @param nationalityCode the nationality code to validate
&nbsp;     * @return ValidationResult containing validation status and error details
&nbsp;     */
&nbsp;    public ValidationResult validateWithDetails(Integer nationalityCode) {
<b class="nc">&nbsp;        if (nationalityCode == null) {</b>
<b class="nc">&nbsp;            return new ValidationResult(false, &quot;Nationality code cannot be null&quot;);</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        if (nationalityCode &lt; 1 || nationalityCode &gt; 999) {</b>
<b class="nc">&nbsp;            return new ValidationResult(false, &quot;Nationality code must be between 1 and 999&quot;);</b>
&nbsp;        }
&nbsp;
&nbsp;        try {
<b class="nc">&nbsp;            Set&lt;String&gt; validCodes = getValidNationalityCodes();</b>
<b class="nc">&nbsp;            if (validCodes.contains(nationalityCode.toString())) {</b>
<b class="nc">&nbsp;                return new ValidationResult(true, &quot;Nationality code is valid&quot;);</b>
&nbsp;            } else {
<b class="nc">&nbsp;                return new ValidationResult(false, </b>
<b class="nc">&nbsp;                    String.format(&quot;Nationality code %d is not found in CBSS nationality codes&quot;, nationalityCode));</b>
&nbsp;            }
&nbsp;        } catch (Exception e) {
<b class="nc">&nbsp;            log.error(&quot;Error validating nationality code {}: {}&quot;, nationalityCode, e.getMessage());</b>
<b class="nc">&nbsp;            return new ValidationResult(false, </b>
<b class="nc">&nbsp;                String.format(&quot;Error validating nationality code: %s&quot;, e.getMessage()));</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Gets all valid nationality codes from the lookup service.
&nbsp;     * Results are cached to improve performance.
&nbsp;     *
&nbsp;     * @return Set of valid nationality code strings
&nbsp;     */
&nbsp;    @Cacheable(&quot;nationalityCodes&quot;)
&nbsp;    public Set&lt;String&gt; getValidNationalityCodes() {
<b class="fc">&nbsp;        log.debug(&quot;Fetching nationality codes from lookup service&quot;);</b>
<b class="fc">&nbsp;        List&lt;LookupData&gt; nationalityCodes = lookupClient.findAllNationalityCodes();</b>
&nbsp;        
<b class="fc">&nbsp;        Set&lt;String&gt; codes = nationalityCodes.stream()</b>
<b class="fc">&nbsp;                .map(LookupData::getCode)</b>
<b class="fc">&nbsp;                .collect(Collectors.toSet());</b>
&nbsp;        
<b class="fc">&nbsp;        log.debug(&quot;Retrieved {} nationality codes from lookup service&quot;, codes.size());</b>
<b class="fc">&nbsp;        return codes;</b>
&nbsp;    }
&nbsp;
&nbsp;//    /**
&nbsp;//     * Gets the description for a nationality code in the specified language.
&nbsp;//     *
&nbsp;//     * @param nationalityCode the nationality code
&nbsp;//     * @param language the language (&quot;fr&quot; for French, &quot;nl&quot; for Dutch)
&nbsp;//     * @return the nationality description or null if not found
&nbsp;//     */
&nbsp;//    public String getNationalityDescription(Integer nationalityCode, String language) {
&nbsp;//        if (nationalityCode == null) {
&nbsp;//            return null;
&nbsp;//        }
&nbsp;//
&nbsp;//        try {
&nbsp;//            List&lt;LookupData&gt; nationalityCodes = lookupClient.findAllNationalityCodes();
&nbsp;//            return nationalityCodes.stream()
&nbsp;//                    .filter(lookup -&gt; lookup.getCode().equals(nationalityCode.toString()))
&nbsp;//                    .findFirst()
&nbsp;//                    .map(lookup -&gt; &quot;fr&quot;.equalsIgnoreCase(language) ? lookup.getDescFr() : lookup.getDescNl())
&nbsp;//                    .orElse(null);
&nbsp;//        } catch (Exception e) {
&nbsp;//            log.error(&quot;Error getting nationality description for code {}: {}&quot;, nationalityCode, e.getMessage());
&nbsp;//            return null;
&nbsp;//        }
&nbsp;//    }
&nbsp;
&nbsp;    /**
&nbsp;     * Result of nationality code validation with detailed information.
&nbsp;     */
&nbsp;    public static class ValidationResult {
&nbsp;        private final boolean valid;
&nbsp;        private final String message;
&nbsp;
<b class="nc">&nbsp;        public ValidationResult(boolean valid, String message) {</b>
<b class="nc">&nbsp;            this.valid = valid;</b>
<b class="nc">&nbsp;            this.message = message;</b>
&nbsp;        }
&nbsp;
&nbsp;        public boolean isValid() {
<b class="nc">&nbsp;            return valid;</b>
&nbsp;        }
&nbsp;
&nbsp;        public String getMessage() {
<b class="nc">&nbsp;            return message;</b>
&nbsp;        }
&nbsp;
&nbsp;        @Override
&nbsp;        public String toString() {
<b class="nc">&nbsp;            return String.format(&quot;ValidationResult{valid=%s, message=&#39;%s&#39;}&quot;, valid, message);</b>
&nbsp;        }
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
