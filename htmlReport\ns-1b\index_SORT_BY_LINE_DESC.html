
<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > be.fgov.onerva.person.backend.validation</title>
  <style type="text/css">
    @import "../css/coverage.css";
    @import "../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../js/highlight.min.js"></script>
  <script type="text/javascript" src="../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">

<div class="breadCrumbs">
Current scope:     <a href="../index_SORT_BY_LINE_DESC.html">all classes</a>
    <span class="separator">|</span>
be.fgov.onerva.person.backend.validation</div>

<h1>Coverage Summary for Package: be.fgov.onerva.person.backend.validation</h1>
<table class="coverageStats">
  <tr>
    <th class="name">Package</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
  </tr>
  <tr>
    <td class="name">be.fgov.onerva.person.backend.validation</td>
<td class="coverageStat">
  <span class="percent">
    50%
  </span>
  <span class="absValue">
    (3/6)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    41.4%
  </span>
  <span class="absValue">
    (12/29)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    21.7%
  </span>
  <span class="absValue">
    (13/60)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    34.8%
  </span>
  <span class="absValue">
    (40/115)
  </span>
</td>
  </tr>
</table>

<br/>
<br/>

<table class="coverageStats">
<tr>
  <th class="name  
">
<a href="index.html">Class</a>  </th>
<th class="coverageStat 
">
  <a href="index_SORT_BY_CLASS.html">Class, %</a>
</th>
<th class="coverageStat 
">
  <a href="index_SORT_BY_METHOD.html">Method, %</a>
</th>
<th class="coverageStat 
">
  <a href="index_SORT_BY_BLOCK.html">Branch, %</a>
</th>
<th class="coverageStat sortedDesc
">
  <a href="index_SORT_BY_LINE.html">Line, %</a>
</th>
</tr>
  <tr>
    <td class="name"><a href="sources/source-2.html">IbanValidator</a></td>
<td class="coverageStat">
  <span class="percent">
    50%
  </span>
  <span class="absValue">
    (1/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    50%
  </span>
  <span class="absValue">
    (5/10)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    32.1%
  </span>
  <span class="absValue">
    (9/28)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    43.6%
  </span>
  <span class="absValue">
    (17/39)
  </span>
</td>
  </tr>
  <tr>
    <td class="name"><a href="sources/source-3.html">NationalityCodeValidator</a></td>
<td class="coverageStat">
  <span class="percent">
    50%
  </span>
  <span class="absValue">
    (1/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    37.5%
  </span>
  <span class="absValue">
    (3/8)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    10%
  </span>
  <span class="absValue">
    (1/10)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    34.4%
  </span>
  <span class="absValue">
    (11/32)
  </span>
</td>
  </tr>
  <tr>
    <td class="name"><a href="sources/source-1.html">CountryCodeValidator</a></td>
<td class="coverageStat">
  <span class="percent">
    50%
  </span>
  <span class="absValue">
    (1/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    36.4%
  </span>
  <span class="absValue">
    (4/11)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    13.6%
  </span>
  <span class="absValue">
    (3/22)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    27.3%
  </span>
  <span class="absValue">
    (12/44)
  </span>
</td>
  </tr>
</table>

</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
