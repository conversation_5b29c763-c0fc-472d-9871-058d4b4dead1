


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenUpdateRequest</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.citizen.model</a>
</div>

<h1>Coverage Summary for Class: CitizenUpdateRequest (be.fgov.onerva.person.backend.citizen.model)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
</tr>
<tr>
  <td class="name">CitizenUpdateRequest</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.citizen.model;
&nbsp;
&nbsp;import be.fgov.onerva.person.backend.request.model.BankUpdateInfo;
&nbsp;import jakarta.validation.Valid;
&nbsp;import jakarta.validation.constraints.*;
&nbsp;import lombok.Builder;
&nbsp;import lombok.Getter;
&nbsp;import lombok.ToString;
&nbsp;
&nbsp;import java.time.LocalDate;
&nbsp;
&nbsp;@Builder
&nbsp;@Getter
&nbsp;@ToString
&nbsp;public class CitizenUpdateRequest {
&nbsp;    @NotNull
&nbsp;    @Pattern(regexp = &quot;\\d{11}&quot;)
&nbsp;    private String niss;
&nbsp;    @NotNull
&nbsp;    @Valid
&nbsp;    private AddressUpdateRequest address;
&nbsp;    @Min(100)
&nbsp;    @Max(999)
&nbsp;    private Integer nationalityCode;
&nbsp;
&nbsp;    /**
&nbsp;     * Birth date
&nbsp;     */
&nbsp;    private LocalDate birthDate;
&nbsp;
&nbsp;    /**
&nbsp;     * Language code: 1=fr, 2=nl, 3=de as per API specification
&nbsp;     */
&nbsp;    @Min(1)
&nbsp;    @Max(3)
&nbsp;    private Integer languageCode;
&nbsp;
&nbsp;    /**
&nbsp;     * Unemployment office number (2 digits) as per mainframe specification
&nbsp;     */
&nbsp;    @Min(1)
&nbsp;    @Max(99)
&nbsp;    private Integer unemploymentOffice;
&nbsp;
&nbsp;    /**
&nbsp;     * Bank information for updates - nested object
&nbsp;     */
&nbsp;    @Valid
&nbsp;    private BankUpdateInfo bankInfo;
&nbsp;
&nbsp;    /**
&nbsp;     * Union due information for updates - nested object
&nbsp;     */
&nbsp;    @Valid
&nbsp;    private UnionDueUpdateInfo unionDueInfo;
&nbsp;
&nbsp;    @NotNull
&nbsp;    private LocalDate validFrom;
&nbsp;    @NotBlank
&nbsp;    private String username;
&nbsp;    private String correlationId;
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
