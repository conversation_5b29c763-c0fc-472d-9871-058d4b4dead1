package be.fgov.onerva.person.backend.request.service;

import be.fgov.onerva.person.backend.citizen.model.AddressUpdateRequest;
import be.fgov.onerva.person.backend.citizen.model.CitizenUpdateRequest;
import be.fgov.onerva.person.backend.request.event.PersonRequestEvent;
import be.fgov.onerva.person.backend.request.formatter.MainframeUpdateMessageFormatter;
import be.fgov.onerva.person.backend.request.model.Address;
import be.fgov.onerva.person.backend.request.model.PersonRequest;
import be.fgov.onerva.person.backend.request.model.PersonRequestType;
import be.fgov.onerva.person.backend.request.persistence.PersonRequestRepository;
import be.fgov.onerva.wave.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Slf4j
@RequiredArgsConstructor
@Service
public class PersonRequestService {

    private final PersonRequestRepository repository;
    private final ApplicationEventPublisher eventPublisher;
    private final MainframeUpdateMessageFormatter messageFormatter;

    @Transactional("personTransactionManager")
    public PersonRequest createMinimalPersonInfo(String firstname, String lastname, String inss, String correlationId) {
        var saved = repository.saveAndFlush(PersonRequest.builder()
                .type(PersonRequestType.CREATE)
                .niss(inss)
                .firstname(firstname)
                .lastname(lastname)
                .correlationId(correlationId)
                .created(LocalDateTime.now())
                .build());

        eventPublisher.publishEvent(new PersonRequestEvent(saved));

        return saved;
    }

    @Transactional("personTransactionManager")
    public PersonRequest updatePersonInfo(CitizenUpdateRequest update, User user) {

        // Set default value dates if specific ones are not provided

        // Extract union due from union due info if available
        AddressUpdateRequest addressUpdateRequest = update.getAddress();
        var address = Address.builder()
                .street(addressUpdateRequest.getStreet())
                .number(addressUpdateRequest.getNumber())
                .box(addressUpdateRequest.getBox())
                .zip(addressUpdateRequest.getZip())
                .city(addressUpdateRequest.getCity())
                .countryCode(addressUpdateRequest.getCountryCode())
                .build();
        var unionDueInfo = update.getUnionDueInfo();
        var bankInfo = update.getBankInfo();

        var saved = repository.saveAndFlush(PersonRequest.builder()
                .type(PersonRequestType.UPDATE)
                .niss(update.getNiss())
                .nationalityCode(update.getNationalityCode())
                .paymentType(update.getBankInfo().getPaymentType())
                .unionDue(unionDueInfo.getUnionDue())
                .unionDueValueDate(unionDueInfo.getValidFrom())
                .valueDate(update.getValidFrom())
                .address(address)
                .username(user.getUsername())
                .operatorCode(getOperatorCode(user))
                .correlationId(update.getCorrelationId())
                .created(LocalDateTime.now())
                // Add new fields from CitizenUpdateRequest
                .birthDate(update.getBirthDate() != null ? update.getBirthDate() : null)
                .languageCode(update.getLanguageCode())
                .unemploymentOffice(update.getUnemploymentOffice())
                // Add bank info fields
                .iban(bankInfo != null ? bankInfo.getIban() : null)
                .bic(bankInfo != null ? bankInfo.getBic() : null)
                .accountHolder(bankInfo != null ? bankInfo.getAccountHolder() : null)
                .bankInfoValueDate(bankInfo != null ? bankInfo.getValidFrom() : null)
                .build());

        eventPublisher.publishEvent(new PersonRequestEvent(saved));

        return saved;
    }

    Integer getOperatorCode(User user) {
        if (!user.getOperatorCodes().isEmpty()) {
            try {
                return Integer.parseInt(user.getOperatorCodes().getFirst());
            } catch (NumberFormatException e) {
                log.warn("Could not convert operator code to an int! ... {}", user);
            }
        }
        return null;
    }
}
