


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > FlagsmithConfig</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.config</a>
</div>

<h1>Coverage Summary for Class: FlagsmithConfig (be.fgov.onerva.person.backend.config)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">FlagsmithConfig</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (2/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (10/10)
  </span>
</td>
</tr>
  <tr>
    <td class="name">FlagsmithConfig$$SpringCGLIB$$0</td>
  </tr>
  <tr>
    <td class="name">FlagsmithConfig$$SpringCGLIB$$FastClass$$0</td>
  </tr>
  <tr>
    <td class="name">FlagsmithConfig$$SpringCGLIB$$FastClass$$1</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (2/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (10/10)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.config;
&nbsp;
&nbsp;import com.flagsmith.FlagsmithClient;
&nbsp;import com.flagsmith.config.FlagsmithCacheConfig;
&nbsp;import org.springframework.beans.factory.annotation.Value;
&nbsp;import org.springframework.context.annotation.Bean;
&nbsp;import org.springframework.context.annotation.Configuration;
&nbsp;
&nbsp;import java.util.concurrent.TimeUnit;
&nbsp;
&nbsp;@Configuration
<b class="fc">&nbsp;public class FlagsmithConfig {</b>
&nbsp;
&nbsp;    @Value(&quot;${flagsmith.environment.id}&quot;)
&nbsp;    private String flagsmithEnvironmentId;
&nbsp;    @Value(&quot;${flagsmith.api}&quot;)
&nbsp;    private String flagsmithApiUrl;
&nbsp;
&nbsp;    @Bean
&nbsp;    public FlagsmithClient flags() {
<b class="fc">&nbsp;        return FlagsmithClient.newBuilder()</b>
<b class="fc">&nbsp;                .withApiUrl(flagsmithApiUrl)</b>
<b class="fc">&nbsp;                .setApiKey(flagsmithEnvironmentId)</b>
<b class="fc">&nbsp;                .enableLogging()</b>
<b class="fc">&nbsp;                .withCache(</b>
&nbsp;                        FlagsmithCacheConfig
<b class="fc">&nbsp;                        .newBuilder()</b>
<b class="fc">&nbsp;                        .expireAfterAccess(1, TimeUnit.MINUTES)</b>
<b class="fc">&nbsp;                        .build()</b>
&nbsp;                )
<b class="fc">&nbsp;                .build();</b>
&nbsp;    }
&nbsp;
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
