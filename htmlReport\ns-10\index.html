
<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > be.fgov.onerva.person.backend.config</title>
  <style type="text/css">
    @import "../css/coverage.css";
    @import "../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../js/highlight.min.js"></script>
  <script type="text/javascript" src="../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">

<div class="breadCrumbs">
Current scope:     <a href="../index.html">all classes</a>
    <span class="separator">|</span>
be.fgov.onerva.person.backend.config</div>

<h1>Coverage Summary for Package: be.fgov.onerva.person.backend.config</h1>
<table class="coverageStats">
  <tr>
    <th class="name">Package</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
  </tr>
  <tr>
    <td class="name">be.fgov.onerva.person.backend.config</td>
<td class="coverageStat">
  <span class="percent">
    66.7%
  </span>
  <span class="absValue">
    (8/12)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    67.6%
  </span>
  <span class="absValue">
    (25/37)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    20%
  </span>
  <span class="absValue">
    (2/10)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    66%
  </span>
  <span class="absValue">
    (64/97)
  </span>
</td>
  </tr>
</table>

<br/>
<br/>

<table class="coverageStats">
<tr>
  <th class="name  sortedAsc
">
<a href="index_SORT_BY_NAME_DESC.html">Class</a>  </th>
<th class="coverageStat 
">
  <a href="index_SORT_BY_CLASS.html">Class, %</a>
</th>
<th class="coverageStat 
">
  <a href="index_SORT_BY_METHOD.html">Method, %</a>
</th>
<th class="coverageStat 
">
  <a href="index_SORT_BY_BLOCK.html">Branch, %</a>
</th>
<th class="coverageStat 
">
  <a href="index_SORT_BY_LINE.html">Line, %</a>
</th>
</tr>
  <tr>
    <td class="name"><a href="sources/source-1.html">DatabaseConfig</a></td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (3/3)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (11/11)
  </span>
</td>
    <td class="coverageStat"/>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (18/18)
  </span>
</td>
  </tr>
  <tr>
    <td class="name"><a href="sources/source-2.html">FlagsmithConfig</a></td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (2/2)
  </span>
</td>
    <td class="coverageStat"/>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (10/10)
  </span>
</td>
  </tr>
  <tr>
    <td class="name"><a href="sources/source-3.html">HttpClientConfig</a></td>
<td class="coverageStat">
  <span class="percent">
    50%
  </span>
  <span class="absValue">
    (1/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    28.6%
  </span>
  <span class="absValue">
    (2/7)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    25%
  </span>
  <span class="absValue">
    (4/16)
  </span>
</td>
  </tr>
  <tr>
    <td class="name"><a href="sources/source-4.html">JmsConfig</a></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
    <td class="coverageStat"/>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
  </tr>
  <tr>
    <td class="name"><a href="sources/source-5.html">OpenApiConfig</a></td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (2/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    50%
  </span>
  <span class="absValue">
    (1/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (12/12)
  </span>
</td>
  </tr>
  <tr>
    <td class="name"><a href="sources/source-6.html">RabbitConfig</a></td>
<td class="coverageStat">
  <span class="percent">
    50%
  </span>
  <span class="absValue">
    (1/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    57.1%
  </span>
  <span class="absValue">
    (4/7)
  </span>
</td>
    <td class="coverageStat"/>
<td class="coverageStat">
  <span class="percent">
    50%
  </span>
  <span class="absValue">
    (4/8)
  </span>
</td>
  </tr>
  <tr>
    <td class="name"><a href="sources/source-7.html">SchedulingConfig</a></td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
    <td class="coverageStat"/>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
  </tr>
  <tr>
    <td class="name"><a href="sources/source-8.html">SecurityConfig</a></td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    66.7%
  </span>
  <span class="absValue">
    (4/6)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    25%
  </span>
  <span class="absValue">
    (1/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    51.6%
  </span>
  <span class="absValue">
    (16/31)
  </span>
</td>
  </tr>
</table>

</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
