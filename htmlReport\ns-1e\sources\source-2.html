


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > JavaTimeFormatter</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.wave</a>
</div>

<h1>Coverage Summary for Class: JavaTimeFormatter (be.fgov.onerva.wave)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">JavaTimeFormatter</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/5)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/7)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;/*
&nbsp; * WO facade API
&nbsp; * API to manage tasks (and processes) in a simplified manner
&nbsp; *
&nbsp; * The version of the OpenAPI document: v1
&nbsp; * 
&nbsp; *
&nbsp; * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
&nbsp; * https://openapi-generator.tech
&nbsp; * Do not edit the class manually.
&nbsp; */
&nbsp;
&nbsp;package be.fgov.onerva.wave;
&nbsp;
&nbsp;import java.time.OffsetDateTime;
&nbsp;import java.time.format.DateTimeFormatter;
&nbsp;import java.time.format.DateTimeParseException;
&nbsp;
&nbsp;/**
&nbsp; * Class that add parsing/formatting support for Java 8+ {@code OffsetDateTime} class.
&nbsp; * It&#39;s generated for java clients when {@code AbstractJavaCodegen#dateLibrary} specified as {@code java8}.
&nbsp; */
&nbsp;@jakarta.annotation.Generated(value = &quot;org.openapitools.codegen.languages.JavaClientCodegen&quot;, date = &quot;2025-07-16T14:11:32.899250600+02:00[Europe/Brussels]&quot;, comments = &quot;Generator version: 7.12.0&quot;)
<b class="nc">&nbsp;public class JavaTimeFormatter {</b>
&nbsp;
<b class="nc">&nbsp;    private DateTimeFormatter offsetDateTimeFormatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;</b>
&nbsp;
&nbsp;    /**
&nbsp;     * Get the date format used to parse/format {@code OffsetDateTime} parameters.
&nbsp;     * @return DateTimeFormatter
&nbsp;     */
&nbsp;    public DateTimeFormatter getOffsetDateTimeFormatter() {
<b class="nc">&nbsp;        return offsetDateTimeFormatter;</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Set the date format used to parse/format {@code OffsetDateTime} parameters.
&nbsp;     * @param offsetDateTimeFormatter {@code DateTimeFormatter}
&nbsp;     */
&nbsp;    public void setOffsetDateTimeFormatter(DateTimeFormatter offsetDateTimeFormatter) {
<b class="nc">&nbsp;        this.offsetDateTimeFormatter = offsetDateTimeFormatter;</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * Parse the given string into {@code OffsetDateTime} object.
&nbsp;     * @param str String
&nbsp;     * @return {@code OffsetDateTime}
&nbsp;     */
&nbsp;    public OffsetDateTime parseOffsetDateTime(String str) {
&nbsp;        try {
<b class="nc">&nbsp;            return OffsetDateTime.parse(str, offsetDateTimeFormatter);</b>
&nbsp;        } catch (DateTimeParseException e) {
<b class="nc">&nbsp;            throw new RuntimeException(e);</b>
&nbsp;        }
&nbsp;    }
&nbsp;    /**
&nbsp;     * Format the given {@code OffsetDateTime} object into string.
&nbsp;     * @param offsetDateTime {@code OffsetDateTime}
&nbsp;     * @return {@code OffsetDateTime} in string format
&nbsp;     */
&nbsp;    public String formatOffsetDateTime(OffsetDateTime offsetDateTime) {
<b class="nc">&nbsp;        return offsetDateTimeFormatter.format(offsetDateTime);</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
