


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenInfoMapperV2Impl</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.citizeninfo.v2.mapper</a>
</div>

<h1>Coverage Summary for Class: CitizenInfoMapperV2Impl (be.fgov.onerva.person.backend.citizeninfo.v2.mapper)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">CitizenInfoMapperV2Impl</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    25%
  </span>
  <span class="absValue">
    (1/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/10)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    3.8%
  </span>
  <span class="absValue">
    (1/26)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.citizeninfo.v2.mapper;
&nbsp;
&nbsp;import backend.rest.model.CitizenInfoPageV2DTO;
&nbsp;import backend.rest.model.CitizenInfoV2DTO;
&nbsp;import be.fgov.onerva.person.backend.citizeninfo.model.CitizenInfoEntity;
&nbsp;import java.math.BigDecimal;
&nbsp;import java.util.ArrayList;
&nbsp;import java.util.List;
&nbsp;import javax.annotation.processing.Generated;
&nbsp;import org.springframework.data.domain.Page;
&nbsp;import org.springframework.stereotype.Component;
&nbsp;
&nbsp;@Generated(
&nbsp;    value = &quot;org.mapstruct.ap.MappingProcessor&quot;,
&nbsp;    date = &quot;2025-07-16T14:12:58+0200&quot;,
&nbsp;    comments = &quot;version: 1.6.3, compiler: javac, environment: Java 21.0.6 (Eclipse Adoptium)&quot;
&nbsp;)
&nbsp;@Component
<b class="fc">&nbsp;public class CitizenInfoMapperV2Impl implements CitizenInfoMapperV2 {</b>
&nbsp;
&nbsp;    @Override
&nbsp;    public CitizenInfoV2DTO map(CitizenInfoEntity entity) {
<b class="nc">&nbsp;        if ( entity == null ) {</b>
<b class="nc">&nbsp;            return null;</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        CitizenInfoV2DTO citizenInfoV2DTO = new CitizenInfoV2DTO();</b>
&nbsp;
<b class="nc">&nbsp;        citizenInfoV2DTO.setNumPens( BigDecimal.valueOf( entity.getNumPens() ) );</b>
<b class="nc">&nbsp;        citizenInfoV2DTO.setNumBox( BigDecimal.valueOf( entity.getNumBox() ) );</b>
<b class="nc">&nbsp;        citizenInfoV2DTO.setFlagPurge( CitizenInfoMapperV2.convertFlag( entity.isFlagPurge() ) );</b>
&nbsp;
<b class="nc">&nbsp;        return citizenInfoV2DTO;</b>
&nbsp;    }
&nbsp;
&nbsp;    @Override
&nbsp;    public CitizenInfoPageV2DTO mapPageToDto(Page&lt;CitizenInfoEntity&gt; source) {
<b class="nc">&nbsp;        if ( source == null ) {</b>
<b class="nc">&nbsp;            return null;</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        CitizenInfoPageV2DTO citizenInfoPageV2DTO = new CitizenInfoPageV2DTO();</b>
&nbsp;
<b class="nc">&nbsp;        citizenInfoPageV2DTO.setPageNumber( source.getNumber() );</b>
<b class="nc">&nbsp;        citizenInfoPageV2DTO.setPageSize( source.getSize() );</b>
<b class="nc">&nbsp;        citizenInfoPageV2DTO.setTotalPage( source.getTotalPages() );</b>
<b class="nc">&nbsp;        citizenInfoPageV2DTO.setTotalElements( (int) source.getTotalElements() );</b>
<b class="nc">&nbsp;        citizenInfoPageV2DTO.setIsFirst( source.isFirst() );</b>
<b class="nc">&nbsp;        citizenInfoPageV2DTO.setIsLast( source.isLast() );</b>
<b class="nc">&nbsp;        if ( source.hasContent() ) {</b>
<b class="nc">&nbsp;            citizenInfoPageV2DTO.setContent( citizenInfoEntityListToCitizenInfoV2DTOList( source.getContent() ) );</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        return citizenInfoPageV2DTO;</b>
&nbsp;    }
&nbsp;
&nbsp;    protected List&lt;CitizenInfoV2DTO&gt; citizenInfoEntityListToCitizenInfoV2DTOList(List&lt;CitizenInfoEntity&gt; list) {
<b class="nc">&nbsp;        if ( list == null ) {</b>
<b class="nc">&nbsp;            return null;</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        List&lt;CitizenInfoV2DTO&gt; list1 = new ArrayList&lt;CitizenInfoV2DTO&gt;( list.size() );</b>
<b class="nc">&nbsp;        for ( CitizenInfoEntity citizenInfoEntity : list ) {</b>
<b class="nc">&nbsp;            list1.add( map( citizenInfoEntity ) );</b>
&nbsp;        }
&nbsp;
<b class="nc">&nbsp;        return list1;</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
