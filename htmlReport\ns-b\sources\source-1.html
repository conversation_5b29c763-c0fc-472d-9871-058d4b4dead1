


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenInfoService</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.citizeninfo.service</a>
</div>

<h1>Coverage Summary for Class: CitizenInfoService (be.fgov.onerva.person.backend.citizeninfo.service)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">CitizenInfoService</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (28/28)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    85.2%
  </span>
  <span class="absValue">
    (75/88)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    99.1%
  </span>
  <span class="absValue">
    (212/214)
  </span>
</td>
</tr>
  <tr>
    <td class="name">CitizenInfoService$$SpringCGLIB$$0</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (28/28)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    85.2%
  </span>
  <span class="absValue">
    (75/88)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    99.1%
  </span>
  <span class="absValue">
    (212/214)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.citizeninfo.service;
&nbsp;
&nbsp;import backend.rest.model.*;
&nbsp;import be.fgov.onerva.common.utils.InssUtils;
&nbsp;import be.fgov.onerva.common.utils.PensionNumberUtils;
&nbsp;import be.fgov.onerva.person.backend.citizeninfo.mapper.AddressUtils;
&nbsp;import be.fgov.onerva.person.backend.citizeninfo.mapper.DateUtils;
&nbsp;import be.fgov.onerva.person.backend.citizeninfo.model.*;
&nbsp;import be.fgov.onerva.person.backend.citizeninfo.persistence.*;
&nbsp;import lombok.RequiredArgsConstructor;
&nbsp;import lombok.extern.slf4j.Slf4j;
&nbsp;import org.springframework.data.domain.Pageable;
&nbsp;import org.springframework.stereotype.Service;
&nbsp;import org.springframework.transaction.annotation.Transactional;
&nbsp;import org.springframework.util.CollectionUtils;
&nbsp;
&nbsp;import java.math.BigDecimal;
&nbsp;import java.util.*;
&nbsp;import java.util.function.Function;
&nbsp;import java.util.stream.Collectors;
&nbsp;
&nbsp;@RequiredArgsConstructor
<b class="fc">&nbsp;@Slf4j</b>
&nbsp;@Service
&nbsp;public class CitizenInfoService {
&nbsp;
<b class="fc">&nbsp;    private static final BigDecimal BELGIAN_NATIONALITY = BigDecimal.valueOf(150);</b>
&nbsp;    private static final int BELGIUM = 1;
&nbsp;    private static final int LATEST = 9;
&nbsp;
&nbsp;    private final CitizenInfoRepository infoRepository;
&nbsp;    private final CitizenInfoCompteRepository compteRepository;
&nbsp;    private final CitizenInfoGeneralRepository generalRepository;
&nbsp;    private final CitizenInfoNationRepository nationRepository;
&nbsp;    private final CitizenInfoCommunicRepository communicRepository;
&nbsp;    private final CitizenInfoComuneDsRepository comuneDsRepository;
&nbsp;    private final CitizenInfoCommuneEtrDsRepository communeEtrDsRepository;
&nbsp;    private final CitizenInfoSiggenDsRepository siggenDsRepository;
&nbsp;    private final CitizenInfoSectopDsRepository sectopDsRepository;
&nbsp;    private final CitizenInfoProfDsRepository profDsRepository;
&nbsp;    private final CitizenInfoContratDsRepository contratDsRepository;
&nbsp;
&nbsp;    @Transactional(readOnly = true)
&nbsp;    public CitizenInfoPageDTO findCitizenInfo(List&lt;String&gt; ssin, List&lt;Integer&gt; numbox, Pageable pageable) {
<b class="fc">&nbsp;        boolean ssinEmpty = CollectionUtils.isEmpty(ssin);</b>
<b class="fc">&nbsp;        boolean numboxEmpty = CollectionUtils.isEmpty(numbox);</b>
&nbsp;
<b class="fc">&nbsp;        if (ssinEmpty &amp;&amp; numboxEmpty) {</b>
<b class="fc">&nbsp;            return toPage(List.of(), pageable);</b>
<b class="fc">&nbsp;        } else if (!ssinEmpty &amp;&amp; !numboxEmpty) {</b>
<b class="fc">&nbsp;            throw new IllegalArgumentException(&quot;Choose parameter ssins or citizedId but not both.&quot;);</b>
&nbsp;        }
&nbsp;
&nbsp;        List&lt;CitizenInfoEntity&gt; citizensWithSsinHistory;
<b class="fc">&nbsp;        if (!ssinEmpty) {</b>
<b class="fc">&nbsp;            List&lt;String&gt; invalidSsins = ssin.stream().filter(this::inssInvalid).toList();</b>
<b class="fc">&nbsp;            if (invalidSsins.size() &gt; 0) {</b>
<b class="fc">&nbsp;                throw new IllegalArgumentException(&quot;Given ssins are invalid &quot; + invalidSsins);</b>
&nbsp;            }
&nbsp;
<b class="fc">&nbsp;            citizensWithSsinHistory = findCitizensWithSsinHistory(ssin);</b>
&nbsp;        } else {
<b class="fc">&nbsp;            citizensWithSsinHistory = infoRepository.findByNumBoxIn(numbox);</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        var citizens = citizensWithSsinHistory.stream().filter(c -&gt; c.getLastId() == LATEST).toList();</b>
<b class="fc">&nbsp;        if (citizens.isEmpty()) {</b>
<b class="fc">&nbsp;            return toPage(List.of(), pageable);</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        int toDay = DateUtils.currentDate();</b>
<b class="fc">&nbsp;        var citizenIds = citizens.stream().map(CitizenInfoEntity::getNumBox).toList();</b>
<b class="fc">&nbsp;        var ssinHistoryById = getSsinHistoryById(citizensWithSsinHistory);</b>
&nbsp;
<b class="fc">&nbsp;        var infoByCitizenId = generalRepository.getCitizenInfoGeneralByNumBoxIn(citizenIds).stream().collect(Collectors.toMap(CitizenInfoGeneral::getNumBox, Function.identity()));</b>
<b class="fc">&nbsp;        var nationalities = nationRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(citizenIds, LATEST, toDay);</b>
<b class="fc">&nbsp;        var contactsInfo = communicRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(citizenIds, LATEST, toDay);</b>
<b class="fc">&nbsp;        var contracts = contratDsRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(citizenIds,LATEST, toDay);</b>
<b class="fc">&nbsp;        var addresses = comuneDsRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(citizenIds, LATEST, toDay);</b>
<b class="fc">&nbsp;        var sectops = sectopDsRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(citizenIds, LATEST, toDay);</b>
<b class="fc">&nbsp;        var profs = profDsRepository.findByNumBoxInAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(citizenIds, LATEST, toDay);</b>
&nbsp;
<b class="fc">&nbsp;        var dtos = citizens.stream()</b>
<b class="fc">&nbsp;                .filter(citizen -&gt; infoByCitizenId.containsKey(citizen.getNumBox()))</b>
<b class="fc">&nbsp;                .map(citizen -&gt; {</b>
<b class="fc">&nbsp;            int citizenId = citizen.getNumBox();</b>
&nbsp;
<b class="fc">&nbsp;            CitizenInfoDTO dto = new CitizenInfoDTO();</b>
<b class="fc">&nbsp;            dto.setNumBox(BigDecimal.valueOf(citizenId));</b>
<b class="fc">&nbsp;            dto.setSsin(toSsin(citizen.getNumPens()));</b>
<b class="fc">&nbsp;            dto.setNumPens(BigDecimal.valueOf(citizen.getNumPens()));</b>
<b class="fc">&nbsp;            dto.setFlagToPurge(citizen.isFlagToPurge() ? FlagDTO.y.name() : FlagDTO.n.name());</b>
&nbsp;
<b class="fc">&nbsp;            var generalInfo = infoByCitizenId.get(citizenId);</b>
<b class="fc">&nbsp;            mapGeneralInfo(dto, generalInfo);</b>
<b class="fc">&nbsp;            setPersonalInfo(dto, generalInfo);</b>
<b class="fc">&nbsp;            setPaymentInstitutionInfo(dto, sectops);</b>
<b class="fc">&nbsp;            setAddress(dto, addresses);</b>
<b class="fc">&nbsp;            setNationalityCode(dto, generalInfo, nationalities);</b>
<b class="fc">&nbsp;            setBankAccount(dto, generalInfo);</b>
<b class="fc">&nbsp;            setContactInformation(dto, contactsInfo);</b>
<b class="fc">&nbsp;            setEmploymentContract(dto, contracts);</b>
<b class="fc">&nbsp;            dto.setBisNumber(ssinHistoryById.get(citizenId));</b>
&nbsp;
<b class="fc">&nbsp;            dto.setLastModifDate(computeGreatestDateValid(generalInfo, addresses, sectops, profs, contracts, nationalities));</b>
&nbsp;
<b class="fc">&nbsp;            return dto;</b>
<b class="fc">&nbsp;        }).toList();</b>
&nbsp;
<b class="fc">&nbsp;        return toPage(dtos, pageable);</b>
&nbsp;    }
&nbsp;
&nbsp;    List&lt;CitizenInfoEntity&gt; findCitizensWithSsinHistory(List&lt;String&gt; ssin) {
<b class="fc">&nbsp;        List&lt;Integer&gt; numpens = ssin.stream().map(el -&gt; PensionNumberUtils.convertFromInss(Long.valueOf(el))).toList();</b>
<b class="fc">&nbsp;        return infoRepository.findByNumPensIn(numpens);</b>
&nbsp;    }
&nbsp;
&nbsp;    Map&lt;Integer,List&lt;String&gt;&gt; getSsinHistoryById(List&lt;CitizenInfoEntity&gt; citizenHistory) {
<b class="fc">&nbsp;        return citizenHistory.stream()</b>
<b class="fc">&nbsp;                .filter(info -&gt; info.getLastId() != LATEST)</b>
<b class="fc">&nbsp;                .collect(Collectors.groupingBy(</b>
&nbsp;                        CitizenInfoEntity::getNumBox,
<b class="fc">&nbsp;                        Collectors.mapping(info -&gt; toSsin(info.getNumPens()), Collectors.toList())</b>
&nbsp;                ));
&nbsp;    }
&nbsp;
&nbsp;    void mapGeneralInfo(CitizenInfoDTO dto, CitizenInfoGeneral generalInfo) {
<b class="pc">&nbsp;        if (generalInfo.getFullName() != null) {</b>
<b class="fc">&nbsp;            var name = trim(generalInfo.getFullName());</b>
<b class="pc">&nbsp;            if (name.contains(&quot;,&quot;)) {</b>
<b class="fc">&nbsp;                var parts = name.split(&quot;,&quot;);</b>
<b class="fc">&nbsp;                dto.setLastName(parts[0].trim());</b>
<b class="fc">&nbsp;                dto.setFirstName(parts[1].trim());</b>
&nbsp;            } else {
<b class="nc">&nbsp;                dto.setLastName(name);</b>
&nbsp;            }
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        dto.setSex(SexDTO.fromValue(generalInfo.getSex()).name());</b>
<b class="fc">&nbsp;        dto.setBirthDate(DateUtils.convertToDate(generalInfo.getBirthDate()));</b>
<b class="fc">&nbsp;        dto.setDeceasedDate(DateUtils.convertToDate(generalInfo.getDeceasedDate()));</b>
<b class="fc">&nbsp;        dto.setUnemploymentOffice(toBigDecimal(generalInfo.getUnemploymentOffice()));</b>
&nbsp;    }
&nbsp;
&nbsp;    void setPaymentInstitutionInfo(CitizenInfoDTO dto, List&lt;CitizenInfoSectopDs&gt; sectops) {
<b class="fc">&nbsp;        var piHistory = sectops.stream()</b>
<b class="fc">&nbsp;                .filter(piInfo -&gt; piInfo.getNumBox() == dto.getNumBox().intValue())</b>
<b class="fc">&nbsp;                .toList();</b>
&nbsp;
<b class="fc">&nbsp;        piHistory.stream()</b>
<b class="fc">&nbsp;                .findFirst()</b>
<b class="fc">&nbsp;                .ifPresent(piInfo -&gt;</b>
<b class="fc">&nbsp;                    dto.setOP(toBigDecimal(piInfo.getSectOp()))</b>
&nbsp;                );
&nbsp;
<b class="fc">&nbsp;        boolean hasUnionDueOnce = piHistory.stream().anyMatch(CitizenInfoSectopDs::hasUnionDue);</b>
<b class="fc">&nbsp;        if (hasUnionDueOnce) {// only give info about mandate if there was one once</b>
<b class="fc">&nbsp;            CitizenInfoSectopDs piFound = null;</b>
<b class="pc">&nbsp;            for (int i = 0; i &lt; piHistory.size(); i++) {</b>
<b class="fc">&nbsp;                var current = piHistory.get(i);</b>
<b class="pc">&nbsp;                if (piHistory.size() == 1) {</b>
<b class="nc">&nbsp;                    piFound = current;</b>
&nbsp;                    break;
&nbsp;                }
<b class="fc">&nbsp;                var previous = piHistory.get(i + 1);</b>
<b class="fc">&nbsp;                if (current.hasUnionDue() != previous.hasUnionDue()) {</b>
<b class="fc">&nbsp;                    piFound = current;</b>
&nbsp;                    break;
<b class="pc">&nbsp;                } else if (i + 2 == piHistory.size()) {</b>
<b class="fc">&nbsp;                    piFound = previous;</b>
&nbsp;                    break;
&nbsp;                }
&nbsp;            }
<b class="fc">&nbsp;            dto.setUnionDue(new CitizenInfoUnionDueDTO(piFound.hasUnionDue(), DateUtils.convertToDate(piFound.getDateValid())));</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    void setAddress(CitizenInfoDTO dto, List&lt;CitizenInfoComuneDs&gt; adresses) {
<b class="fc">&nbsp;        int citizenId = dto.getNumBox().intValue();</b>
<b class="fc">&nbsp;        adresses.stream()</b>
<b class="fc">&nbsp;                .filter(address -&gt; address.getNumBox() == citizenId)</b>
<b class="fc">&nbsp;                .findFirst()</b>
<b class="fc">&nbsp;                .ifPresent(address -&gt; {</b>
<b class="fc">&nbsp;                    String addressLine = trim(address.getAdresse());</b>
<b class="fc">&nbsp;                    dto.setAddress(addressLine);</b>
&nbsp;
<b class="pc">&nbsp;                    if (address.getRvaCountryCode() != null) {</b>
&nbsp;                        // normally the code-resid is the NIS code of a belgian town but if smaller than a normal NIS code it is the NEO country code
<b class="fc">&nbsp;                        if (address.getRvaCountryCode() &gt; 9999) {</b>
<b class="pc">&nbsp;                            String zip = address.getCodePost() == null ? null : address.getCodePost().toString();</b>
<b class="fc">&nbsp;                            dto.setPostalCode(zip);</b>
<b class="fc">&nbsp;                            var streetParts = AddressUtils.splitStreet(addressLine);</b>
<b class="fc">&nbsp;                            var numberParts = AddressUtils.splitHouseNumberAndBox(streetParts[1]);</b>
<b class="fc">&nbsp;                            dto.setAddressObj(new ForeignAddressDTO()</b>
<b class="fc">&nbsp;                                    .street(streetParts[0])</b>
<b class="fc">&nbsp;                                    .number(numberParts[0])</b>
<b class="fc">&nbsp;                                    .box(numberParts[1])</b>
<b class="fc">&nbsp;                                    .zip(zip)</b>
<b class="fc">&nbsp;                                    .countryCode(BELGIUM)</b>
<b class="fc">&nbsp;                                    .validFrom(DateUtils.convertToDate(address.getDateValid()))</b>
&nbsp;                            );
&nbsp;                        } else {
<b class="fc">&nbsp;                            setForeignAddress(dto, address.getRvaCountryCode(), citizenId);</b>
&nbsp;                        }
&nbsp;                    }
&nbsp;                });
&nbsp;    }
&nbsp;
&nbsp;    void setForeignAddress(CitizenInfoDTO dto, int countryCode, int citizenId) {
<b class="fc">&nbsp;        communeEtrDsRepository.findFirstByNumBoxAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(citizenId, LATEST, DateUtils.currentDate())</b>
<b class="fc">&nbsp;                .ifPresent(adr -&gt; {</b>
<b class="fc">&nbsp;                    String zip = trim(adr.getZip());</b>
<b class="fc">&nbsp;                    dto.setPostalCode(zip);</b>
<b class="fc">&nbsp;                    var numberParts = AddressUtils.splitHouseNumberAndBox(trim(adr.getNumber()));</b>
<b class="fc">&nbsp;                    dto.setAddressObj(new ForeignAddressDTO()</b>
<b class="fc">&nbsp;                            .street(trim(adr.getStreet()))</b>
<b class="fc">&nbsp;                            .number(numberParts[0])</b>
<b class="fc">&nbsp;                            .box(numberParts[1])</b>
<b class="fc">&nbsp;                            .zip(zip)</b>
<b class="fc">&nbsp;                            .city(trim(adr.getCity()))</b>
<b class="fc">&nbsp;                            .countryCode(countryCode)</b>
<b class="fc">&nbsp;                            .validFrom(DateUtils.convertToDate(adr.getDateValid()))</b>
&nbsp;                    );
&nbsp;                });
&nbsp;    }
&nbsp;
&nbsp;    void setContactInformation(CitizenInfoDTO dto, List&lt;CitizenInfoCommunic&gt; contactsInfo) {
<b class="fc">&nbsp;        contactsInfo.stream()</b>
<b class="fc">&nbsp;                .filter(contactInfo -&gt; contactInfo.getNumBox() == dto.getNumBox().intValue())</b>
<b class="fc">&nbsp;                .findFirst()</b>
<b class="fc">&nbsp;                .ifPresent(contactInfo -&gt; {</b>
<b class="fc">&nbsp;                        dto.setEmail(trim(contactInfo.getEmail()));</b>
<b class="fc">&nbsp;                        dto.setTelephoneOnem(trim(contactInfo.getTelephoneOnem()));</b>
<b class="fc">&nbsp;                        dto.setTelephoneReg(trim(contactInfo.getTelephoneReg()));</b>
<b class="fc">&nbsp;                        dto.setGsmOnem(trim(contactInfo.getGsmOnem()));</b>
<b class="fc">&nbsp;                        dto.setGsmReg(trim(contactInfo.getGsmReg()));</b>
<b class="fc">&nbsp;                        dto.setEmailReg(trim(contactInfo.getEmailReg()));</b>
&nbsp;                });
&nbsp;    }
&nbsp;
&nbsp;    void setEmploymentContract(CitizenInfoDTO dto, List&lt;CitizenInfoContratDs&gt; contracts) {
<b class="fc">&nbsp;        contracts.stream()</b>
<b class="fc">&nbsp;                .filter(contactInfo -&gt; contactInfo.getNumBox() == dto.getNumBox().intValue())</b>
<b class="fc">&nbsp;                .findFirst()</b>
<b class="fc">&nbsp;                .map(CitizenInfoContratDs::getEmploymentContract)</b>
<b class="fc">&nbsp;                .ifPresent(dto::setEmploymentContract);</b>
&nbsp;    }
&nbsp;
&nbsp;    void setNationalityCode(CitizenInfoDTO dto, CitizenInfoGeneral generalInfo, List&lt;CitizenInfoNation&gt; nationalities) {
<b class="fc">&nbsp;        if (generalInfo.isFlagNation()) {</b>
<b class="fc">&nbsp;            dto.setFlagNation(</b>
<b class="pc">&nbsp;                    nationalities.stream().filter(nationality -&gt; nationality.getNumBox() == dto.getNumBox().intValue() &amp;&amp; nationality.getNation() != null)</b>
<b class="fc">&nbsp;                            .map(nationality -&gt; BigDecimal.valueOf(nationality.getNation()))</b>
<b class="fc">&nbsp;                            .findFirst()</b>
<b class="fc">&nbsp;                            .orElse(BELGIAN_NATIONALITY)</b>
&nbsp;            );
&nbsp;        } else {
<b class="fc">&nbsp;            dto.setFlagNation(BELGIAN_NATIONALITY);</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    void setBankAccount(CitizenInfoDTO dto, CitizenInfoGeneral generalInfo) {
<b class="fc">&nbsp;        int toDay = DateUtils.currentDate();</b>
<b class="pc">&nbsp;        if (generalInfo.isFlagVCpte() &amp;&amp; generalInfo.getDateVCpte() &lt;= toDay) {</b>
<b class="fc">&nbsp;            fillBankAccount(dto, trim(generalInfo.getIban()), trim(generalInfo.getBic()), trim(generalInfo.getBankAccountHolder()), generalInfo.getDateVCpte());</b>
&nbsp;        } else {
<b class="fc">&nbsp;            compteRepository.findFirstByParentIdAndFlagValidAndDateValidLessThanEqualOrderByDateValidDesc(generalInfo.getId(), LATEST, toDay)</b>
<b class="fc">&nbsp;                    .ifPresent(bankAccount -&gt;</b>
<b class="fc">&nbsp;                            fillBankAccount(dto, trim(bankAccount.getIban()), trim(bankAccount.getBic()), trim(bankAccount.getBankAccountHolder()), bankAccount.getDateValid())</b>
&nbsp;                    );
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    void fillBankAccount(CitizenInfoDTO dto, String iban, String bic, String holder, int validFrom) {
<b class="fc">&nbsp;        if (iban != null) {</b>
<b class="fc">&nbsp;            dto.setIban(iban);</b>
<b class="fc">&nbsp;            dto.setBankAccount(new BankAccountDTO()</b>
<b class="fc">&nbsp;                    .iban(iban)</b>
<b class="fc">&nbsp;                    .bic(bic)</b>
<b class="fc">&nbsp;                    .holder(holder)</b>
<b class="fc">&nbsp;                    .validFrom(DateUtils.convertToDate(validFrom))</b>
&nbsp;            );
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    void setPersonalInfo(CitizenInfoDTO dto, CitizenInfoGeneral generalInfo) {
<b class="fc">&nbsp;        int toDay = DateUtils.currentDate();</b>
<b class="pc">&nbsp;        if (generalInfo.isFlagSiggen() &amp;&amp; generalInfo.getDateVSiggen() &lt;= toDay) {</b>
<b class="fc">&nbsp;            setLanguageAndPaymentMode(dto, generalInfo.getLanguage(), generalInfo.getPaymentMode());</b>
&nbsp;        } else {
<b class="fc">&nbsp;            siggenDsRepository.findTopByParentIdAndFlagValidOrderByDateValidDesc(generalInfo.getId(), LATEST)</b>
<b class="fc">&nbsp;                    .ifPresent(siggen -&gt;</b>
<b class="fc">&nbsp;                            setLanguageAndPaymentMode(dto, siggen.getLanguage(), siggen.getPaymentMode())</b>
&nbsp;                    );
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    void setLanguageAndPaymentMode(CitizenInfoDTO dto, Integer language, Integer paymentMode) {
<b class="fc">&nbsp;        if (language != null) {</b>
<b class="fc">&nbsp;            dto.setLanguage(LanguageDTO.fromValue(language).name());</b>
&nbsp;        }
<b class="fc">&nbsp;        dto.setPaymentMode(paymentMode);</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     To be removed if not needed.
&nbsp;     From KDD analysis:
&nbsp;        The validity date in the upper right corner is computed based on different tables.
&nbsp;        It is the last date among the 6 following dates (attention: it is possible that one of those dates is null):
&nbsp;        date_v1: [date_v_siggen] if [flag_v_siggen] = 1 in table [general_ds] otherwise last [date_valid] in table [siggen_ds] for given [num_box] and [flag_valid] = 9
&nbsp;        date_v2: last [date_valid] in table [commune_ds] for given [num_box] and [flag_valid] = 9
&nbsp;        date_v3: last [date_valid] in table [sectop_ds] for given [num_box] and [flag_valid] = 9
&nbsp;        date_v4: last [date_valid] in table [prof_ds] for given [num_box] and [flag_valid] = 9
&nbsp;        date_v5: last [date_valid] in table [contrat_ds] for given [num_box] and [flag_valid] = 9
&nbsp;        date_v6: last [date_valid] in table [nation_ds] for given [num_box] and [flag_valid] = 9
&nbsp;     */
&nbsp;    Integer computeGreatestDateValid(
&nbsp;            CitizenInfoGeneral generalInfo,
&nbsp;            List&lt;CitizenInfoComuneDs&gt; addresses,
&nbsp;            List&lt;CitizenInfoSectopDs&gt; sectops,
&nbsp;            List&lt;CitizenInfoProfDs&gt; profs,
&nbsp;            List&lt;CitizenInfoContratDs&gt; contracts,
&nbsp;            List&lt;CitizenInfoNation&gt; nationalities
&nbsp;    ) {
<b class="fc">&nbsp;        var dateList = new ArrayList&lt;Integer&gt;();</b>
<b class="fc">&nbsp;        int citizenId = generalInfo.getNumBox();</b>
&nbsp;
<b class="fc">&nbsp;        if (generalInfo.isFlagSiggen()) {</b>
<b class="fc">&nbsp;            dateList.add(generalInfo.getDateVSiggen());</b>
&nbsp;        } else {
<b class="fc">&nbsp;            siggenDsRepository.findTopByParentIdAndFlagValidOrderByDateValidDesc(generalInfo.getId(), LATEST)</b>
<b class="fc">&nbsp;                    .map(CitizenInfoSiggenDs::getDateValid)</b>
<b class="fc">&nbsp;                    .ifPresent(dateList::add);</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        addresses.stream()</b>
<b class="fc">&nbsp;                .filter(address -&gt; address.getNumBox() == citizenId)</b>
<b class="fc">&nbsp;                .findFirst()</b>
<b class="fc">&nbsp;                .map(CitizenInfoComuneDs::getDateValid)</b>
<b class="fc">&nbsp;                .ifPresent(dateList::add);</b>
&nbsp;
<b class="fc">&nbsp;        sectops.stream()</b>
<b class="fc">&nbsp;                .filter(sectop -&gt; sectop.getNumBox() == citizenId)</b>
<b class="fc">&nbsp;                .findFirst()</b>
<b class="fc">&nbsp;                .map(CitizenInfoSectopDs::getDateValid)</b>
<b class="fc">&nbsp;                .ifPresent(dateList::add);</b>
&nbsp;
<b class="fc">&nbsp;        profs.stream()</b>
<b class="pc">&nbsp;                .filter(prof -&gt; prof.getNumBox() == citizenId)</b>
<b class="fc">&nbsp;                .findFirst()</b>
<b class="fc">&nbsp;                .map(CitizenInfoProfDs::getDateValid)</b>
<b class="fc">&nbsp;                .ifPresent(dateList::add);</b>
&nbsp;
<b class="fc">&nbsp;        contracts.stream()</b>
<b class="fc">&nbsp;                .filter(contract -&gt; contract.getNumBox() == citizenId)</b>
<b class="fc">&nbsp;                .findFirst()</b>
<b class="fc">&nbsp;                .map(CitizenInfoContratDs::getDateValid)</b>
<b class="fc">&nbsp;                .ifPresent(dateList::add);</b>
&nbsp;
<b class="fc">&nbsp;        nationalities.stream()</b>
<b class="fc">&nbsp;                .filter(nationality -&gt; nationality.getNumBox() == citizenId)</b>
<b class="fc">&nbsp;                .findFirst()</b>
<b class="fc">&nbsp;                .map(CitizenInfoNation::getDateValid)</b>
<b class="fc">&nbsp;                .ifPresent(dateList::add);</b>
&nbsp;
<b class="pc">&nbsp;        return dateList.isEmpty() ? null : Collections.max(dateList);</b>
&nbsp;    }
&nbsp;
&nbsp;    boolean inssInvalid(String inss) {
&nbsp;        try {
<b class="fc">&nbsp;            return !InssUtils.isValid(Long.parseLong(inss));</b>
&nbsp;        } catch (NumberFormatException e) {
<b class="fc">&nbsp;            return true;</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    String toSsin(int numPens) {
<b class="fc">&nbsp;        return String.format(&quot;%011d&quot;, PensionNumberUtils.convertToInssWithDefault(numPens));</b>
&nbsp;    }
&nbsp;
&nbsp;    CitizenInfoPageDTO toPage(List&lt;CitizenInfoDTO&gt; dtos, Pageable pageable) {
<b class="fc">&nbsp;        return new CitizenInfoPageDTO()</b>
<b class="fc">&nbsp;                .pageSize(pageable.getPageSize())</b>
<b class="fc">&nbsp;                .pageNumber(pageable.getPageNumber())</b>
<b class="fc">&nbsp;                .content(dtos)</b>
<b class="fc">&nbsp;                .isFirst(true)</b>
<b class="fc">&nbsp;                .isLast(true)</b>
<b class="fc">&nbsp;                .totalElements(dtos.size())</b>
<b class="fc">&nbsp;                .totalPage(dtos.isEmpty() ? 0 : 1);</b>
&nbsp;    }
&nbsp;
&nbsp;    static String trim(String s) {
<b class="fc">&nbsp;        if (s == null) {</b>
<b class="fc">&nbsp;            return null;</b>
&nbsp;        }
<b class="fc">&nbsp;        String trimmed = s.trim();</b>
<b class="fc">&nbsp;        return trimmed.isEmpty() ? null : trimmed;</b>
&nbsp;    }
&nbsp;
&nbsp;    BigDecimal toBigDecimal(Number n) {
<b class="pc">&nbsp;        return n == null? null : BigDecimal.valueOf(n.longValue());</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
