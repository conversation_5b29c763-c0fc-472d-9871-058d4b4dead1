package be.fgov.onerva.person.backend.citizen.controller;

import backend.rest.model.BankUpdateRequestDTO;
import backend.rest.model.CitizenCreationRequestDTO;
import backend.rest.model.CitizenDTO;
import backend.rest.model.CitizenRequestDTO;
import backend.rest.model.CitizenUpdateRequestDTO;
import backend.rest.model.ForeignAddressDTO;
import backend.rest.model.PaymentTypeDTO;
import backend.rest.model.UnionDueUpdateRequestDTO;
import be.fgov.onerva.person.backend.IntegrationTest;
import be.fgov.onerva.person.backend.citizen.model.CitizenEntity;
import be.fgov.onerva.person.backend.citizen.persistence.CitizenRepository;
import be.fgov.onerva.person.backend.request.model.PersonRequest;
import be.fgov.onerva.person.backend.request.persistence.PersonRequestRepository;
import be.fgov.onerva.wave.model.User;
import be.fgov.onerva.wave.model.UserCriteria;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.jdbc.Sql;

import java.time.LocalDate;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@Sql(value = "/scripts/init-data.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
class CitizenControllerTest extends IntegrationTest {

    private static final String BASE_PATH = "/api/citizen";

    @Autowired
    private CitizenRepository citizenRepository;

    @Autowired
    private PersonRequestRepository requestRepository;

    @Test
    void getByNiss_404() {
        rest.get().uri(BASE_PATH + "/800064925").exchange().assertThat().hasStatus(HttpStatus.NOT_FOUND);

        rest.get().uri(BASE_PATH + "/22100200625").exchange().assertThat().hasStatus(HttpStatus.NOT_FOUND);
    }

    @Test
    void getByNiss_400() {
        rest.get().uri(BASE_PATH + "/person").exchange().assertThat().hasStatus(HttpStatus.BAD_REQUEST);

        rest.get().uri(BASE_PATH + "/").exchange().assertThat().hasStatus(HttpStatus.NOT_FOUND);

        rest.get().uri(BASE_PATH).exchange().assertThat().hasStatus(HttpStatus.BAD_REQUEST);
    }

    @Test
    void getByNiss_200() {
        var citizenDto = new CitizenDTO().niss("99123199940")
                .firstname("NAME1")
                .lastname("LASTNAME1")
                .zipCode(1180)
                .pensionNumber(999231999)
                .numbox(47000)
                .agent(false);

        rest.get()
                .uri(BASE_PATH + "/99123199940")
                .exchange()
                .assertThat()
                .hasStatusOk()
                .bodyJson()
                .convertTo(CitizenDTO.class)
                .isEqualTo(citizenDto);
    }

    @Test
    void getByNumbox_200() {
        var citizenDto = new CitizenDTO().niss("99123199940")
                .firstname("NAME1")
                .lastname("LASTNAME1")
                .zipCode(1180)
                .pensionNumber(999231999)
                .numbox(47000)
                .agent(false);

        rest.get()
                .uri(BASE_PATH + "/numbox/47000")
                .exchange()
                .assertThat()
                .hasStatusOk()
                .bodyJson()
                .convertTo(CitizenDTO.class)
                .isEqualTo(citizenDto);
    }

    @Test
    void getByNumbox_404() {

        rest.get().uri(BASE_PATH + "/numbox/991000").exchange().assertThat().hasStatus(HttpStatus.NOT_FOUND);
    }

    @Test
    void getByDenomination_200() {
        var citizenDto = new CitizenDTO().niss("99123199940")
                .firstname("NAME1")
                .lastname("LASTNAME1")
                .zipCode(1180)
                .pensionNumber(999231999)
                .numbox(47000)
                .agent(false);

        rest.get()
                .uri(BASE_PATH + "?query=fullName==^LASTNAME1*;fullName=ilike=nA;fullName=ilike=STNaMe1")
                .exchange()
                .assertThat()
                .hasStatusOk()
                .bodyJson()
                .extractingPath("$.content[0]")
                .convertTo(CitizenDTO.class)
                .isEqualTo(citizenDto);
    }

    @Test
    void getByDenomination_200_ignore_case() {
        var citizenDto = new CitizenDTO().niss("99123199940")
                .firstname("NAME1")
                .lastname("LASTNAME1")
                .zipCode(1180)
                .pensionNumber(999231999)
                .numbox(47000)
                .agent(false);

        rest.get()
                .uri(BASE_PATH + "?query=fullName==^LASTNAME1*;fullName=ilike=nA;fullName=ilike=STNaMe1")
                .exchange()
                .assertThat()
                .hasStatusOk()
                .bodyJson()
                .extractingPath("$.content[0]")
                .convertTo(CitizenDTO.class)
                .isEqualTo(citizenDto);
    }

    @Test
    void createCitizen_400() {
        var request = new CitizenCreationRequestDTO().niss("***********").lastname("NAME1").firstname("LASTNAME1");

        // no params
        rest.post().uri(BASE_PATH).exchange().assertThat().hasStatus(HttpStatus.BAD_REQUEST);

        // no businessDomain
        rest.post()
                .uri(BASE_PATH + "?allowance=false")
                .content(toJson(request))
                .exchange()
                .assertThat()
                .hasStatus(HttpStatus.BAD_REQUEST);

        // wrong businessDomain
        rest.post()
                .uri(BASE_PATH + "?businessDomain=DUMMY&allowance=false")
                .contentType(MediaType.APPLICATION_JSON)
                .content(toJson(request))
                .exchange()
                .assertThat()
                .hasStatus(HttpStatus.BAD_REQUEST);

        // no allowance
        rest.post()
                .uri(BASE_PATH + "?businessDomain=ADMISSIBILITY")
                .contentType(MediaType.APPLICATION_JSON)
                .content(toJson(request))
                .exchange()
                .assertThat()
                .hasStatus(HttpStatus.BAD_REQUEST);

        // wrong allowance type
        rest.post()
                .uri(BASE_PATH + "?businessDomain=ADMISSIBILITY&allowance=78")
                .contentType(MediaType.APPLICATION_JSON)
                .content(toJson(request))
                .exchange()
                .assertThat()
                .hasStatus(HttpStatus.BAD_REQUEST);

        // allowance is true
        rest.post()
                .uri(BASE_PATH + "?businessDomain=ADMISSIBILITY&allowance=true")
                .contentType(MediaType.APPLICATION_JSON)
                .content(toJson(request))
                .exchange()
                .assertThat()
                .hasStatus(HttpStatus.BAD_REQUEST);

        // no req body
        rest.post()
                .uri(BASE_PATH + "?businessDomain=ADMISSIBILITY&allowance=false")
                .contentType(MediaType.APPLICATION_JSON)
                .exchange()
                .assertThat()
                .hasStatus(HttpStatus.BAD_REQUEST);

        // empty req body
        rest.post()
                .uri(BASE_PATH + "?businessDomain=ADMISSIBILITY&allowance=false")
                .contentType(MediaType.APPLICATION_JSON)
                .content(toJson(new CitizenCreationRequestDTO()))
                .exchange()
                .assertThat()
                .hasStatus(HttpStatus.BAD_REQUEST);

        // empty firstname
        rest.post()
                .uri(BASE_PATH + "?businessDomain=ADMISSIBILITY&allowance=false")
                .contentType(MediaType.APPLICATION_JSON)
                .content(toJson(new CitizenCreationRequestDTO().niss("***********").firstname("   ").lastname("NAME1")))
                .exchange()
                .assertThat()
                .hasStatus(HttpStatus.BAD_REQUEST);

        // empty lastname
        rest.post()
                .uri(BASE_PATH + "?businessDomain=ADMISSIBILITY&allowance=false")
                .contentType(MediaType.APPLICATION_JSON)
                .content(toJson(new CitizenCreationRequestDTO().niss("***********")
                        .firstname("LASTNAME1")
                        .lastname("   ")))
                .exchange()
                .assertThat()
                .hasStatus(HttpStatus.BAD_REQUEST);

        // invalid niss
        rest.post()
                .uri(BASE_PATH + "?businessDomain=ADMISSIBILITY&allowance=false")
                .contentType(MediaType.APPLICATION_JSON)
                .content(toJson(new CitizenCreationRequestDTO().niss("123").firstname("LASTNAME1").lastname("NAME1")))
                .exchange()
                .assertThat()
                .hasStatus(HttpStatus.BAD_REQUEST);

        // deprecated faalback url
        rest.post()
                .uri(BASE_PATH + "?businessDomain=ADMISSIBILITY&allowance=false")
                .contentType(MediaType.APPLICATION_JSON)
                .content(toJson(new CitizenCreationRequestDTO().niss("123")
                        .firstname("LASTNAME1")
                        .lastname("NAME1")
                        .fallbackUrl("http://dont-call-me")))
                .exchange()
                .assertThat()
                .hasStatus(HttpStatus.BAD_REQUEST);
    }

    @Test
    void createCitizen_201() {
        citizenRepository.deleteAllInBatch();
        var request = new CitizenCreationRequestDTO().niss("***********")
                .firstname("LASTNAME1")
                .lastname("NAME1")
                .correlationId("123");

        var result = rest.post()
                .uri(BASE_PATH + "?businessDomain=ADMISSIBILITY&allowance=false")
                .contentType(MediaType.APPLICATION_JSON)
                .content(toJson(request))
                .exchange();

        result.assertThat().hasStatus(HttpStatus.CREATED).containsHeader("Location").body().isEmpty();

        var location = result.getResponse().getHeader("Location");
        int index = location.lastIndexOf('/');
        long id = Long.parseLong(location.substring(index + 1));
        String formattedId = PersonRequest.ID_FORMAT.formatted(id);

        Optional<PersonRequest> byId = requestRepository.findById(id);
        assertThat(byId).isNotEmpty();
        var personRequest = byId.get();
        assertThat(personRequest.getNiss()).isEqualTo("***********");
        assertThat(personRequest.getFirstname()).isEqualTo("LASTNAME1");
        assertThat(personRequest.getLastname()).isEqualTo("NAME1");
        assertThat(personRequest.getCorrelationId()).isEqualTo("123");

        verify(jmsTemplate).convertAndSend("0001" + formattedId + "***********0000NAME1,LASTNAME1               ");
    }

    @Test
    void updateCitizen_204() {
        when(userApi.searchUsers(any())).thenReturn(new User().firstname("John").lastname("Doe").username("jdoe"));

        citizenRepository.saveAndFlush(new CitizenEntity(639006147, "BONTE,DIRK", 2911472, 8900, 33, 9, false));

        var request = new CitizenUpdateRequestDTO().address(new ForeignAddressDTO().street("Chaussée de Charleroi")
                        .number("2087a")
                        .box("boite 3b")
                        .zip("5032")
                        .city("Gembloux")
                        .countryCode(1).validFrom(
                                LocalDate.of(2024, 11, 11)
                        ))
                .nationalityCode(150)
                .unionDueInfo(new UnionDueUpdateRequestDTO().unionDue(false).validFrom(LocalDate.of(2024, 11, 11)))
                .bankInfo(new BankUpdateRequestDTO().iban("****************").paymentType(
                        PaymentTypeDTO.CIRCULAR_CHEQUE
                ).validFrom(
                        LocalDate.of(2024, 11, 12)
                ))
                .validFrom(
                        LocalDate.of(2024, 11, 10)
                )
                .correlationId("abc");

        var result = rest.put()
                .uri(BASE_PATH + "/{niss}?username={user}", "***********", "jdoe")
                .contentType(MediaType.APPLICATION_JSON)
                .content(toJson(request))
                .exchange();

        result.assertThat().hasStatus(HttpStatus.NO_CONTENT).containsHeader("Location").body().isEmpty();

        var reqId = new AtomicReference<Long>();
        rest.get()
                .uri(result.getResponse().getHeader("Location"))
                .exchange()
                .assertThat()
                .hasStatusOk()
                .bodyJson()
                .convertTo(CitizenRequestDTO.class)
                .satisfies(req -> {
                    reqId.set(req.getId());
                    assertThat(req.getId()).isNotNull();
                    assertThat(req.getCorrelationId()).isEqualTo("abc");
//                    assertThat(req.getAddress()).isEqualTo(request.getAddress());
                    assertThat(req.getNationalityCode()).isEqualTo(150);
                    assertThat(req.getUnionDue()).isFalse();
                    assertThat(req.getPaymentType()).isEqualTo(PaymentTypeDTO.CIRCULAR_CHEQUE);
                    assertThat(req.getValueDate()).isEqualTo("2024-11-10");
                });

        verify(userApi).searchUsers(new UserCriteria().username("jdoe"));
        verify(jmsTemplate).convertAndSend("0002" +
                String.format(PersonRequest.ID_FORMAT, reqId.get()) +
                "***********0000000220241110CHAUSSEE DE CHARL 2087A BTE 3B5032      GEMBLOUX                      001 150        00          000320241112****************                                                           30004202411110                                                                                                                ");
    }

    // Tests to delete after migration in kdd
    @Test
    void getByDenomination_200_ignore_caseOld() {
        var citizenDto = new CitizenDTO().niss("99123199940")
                .firstname("NAME1")
                .lastname("LASTNAME1")
                .zipCode(1180)
                .pensionNumber(999231999)
                .numbox(47000)
                .agent(false);

        rest.get()
                .uri(BASE_PATH + "?query=fullName==^LASTNAME1*;fullName=ilike=nA;fullName=ilike=STNaMe1")
                .exchange()
                .assertThat()
                .hasStatusOk()
                .bodyJson()
                .extractingPath("$.content[0]")
                .convertTo(CitizenDTO.class)
                .isEqualTo(citizenDto);
    }
}