


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > DateUtils</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.citizeninfo.mapper</a>
</div>

<h1>Coverage Summary for Class: DateUtils (be.fgov.onerva.person.backend.citizeninfo.mapper)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">DateUtils</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (3/3)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (6/6)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (11/11)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.citizeninfo.mapper;
&nbsp;
&nbsp;import lombok.experimental.UtilityClass;
&nbsp;
&nbsp;import java.time.LocalDate;
&nbsp;
&nbsp;@UtilityClass
&nbsp;public final class DateUtils {
&nbsp;
&nbsp;    private static final int NULL_DATE = 9999_99_99;
&nbsp;
&nbsp;    public static LocalDate convertToDate(Integer dateInteger) {
<b class="fc">&nbsp;        if (dateInteger == null ||  dateInteger &gt;= NULL_DATE) {</b>
<b class="fc">&nbsp;            return null;</b>
&nbsp;        }
&nbsp;        try {
<b class="fc">&nbsp;            return LocalDate.of(</b>
<b class="fc">&nbsp;                    dateInteger / 10_000,</b>
<b class="fc">&nbsp;                    (dateInteger % 10_000) / 100,</b>
<b class="fc">&nbsp;                    dateInteger % 100</b>
&nbsp;            );
&nbsp;        } catch (RuntimeException e) {
<b class="fc">&nbsp;            return null;</b>
&nbsp;        }
&nbsp;    }
&nbsp;
&nbsp;    public static Integer convertToInt(LocalDate date) {
<b class="fc">&nbsp;        if (date == null) {</b>
<b class="fc">&nbsp;            return null;</b>
&nbsp;        }
<b class="fc">&nbsp;        return (date.getYear() * 10_000) + (date.getMonthValue() * 100) +  date.getDayOfMonth();</b>
&nbsp;    }
&nbsp;
&nbsp;    public static int currentDate() {
<b class="fc">&nbsp;        return convertToInt(LocalDate.now());</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
