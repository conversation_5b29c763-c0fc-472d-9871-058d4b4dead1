


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > ApiUtil</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">backend.api</a>
</div>

<h1>Coverage Summary for Class: ApiUtil (backend.api)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">ApiUtil</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/6)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package backend.api;
&nbsp;
&nbsp;import org.springframework.web.context.request.NativeWebRequest;
&nbsp;
&nbsp;import jakarta.servlet.http.HttpServletResponse;
&nbsp;import java.io.IOException;
&nbsp;
<b class="nc">&nbsp;public class ApiUtil {</b>
&nbsp;    public static void setExampleResponse(NativeWebRequest req, String contentType, String example) {
&nbsp;        try {
<b class="nc">&nbsp;            HttpServletResponse res = req.getNativeResponse(HttpServletResponse.class);</b>
<b class="nc">&nbsp;            res.setCharacterEncoding(&quot;UTF-8&quot;);</b>
<b class="nc">&nbsp;            res.addHeader(&quot;Content-Type&quot;, contentType);</b>
<b class="nc">&nbsp;            res.getWriter().print(example);</b>
&nbsp;        } catch (IOException e) {
<b class="nc">&nbsp;            throw new RuntimeException(e);</b>
&nbsp;        }
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
