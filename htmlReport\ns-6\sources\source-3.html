


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > UnionDueUpdateInfo</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.citizen.model</a>
</div>

<h1>Coverage Summary for Class: UnionDueUpdateInfo (be.fgov.onerva.person.backend.citizen.model)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
</tr>
<tr>
  <td class="name">UnionDueUpdateInfo</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.citizen.model;
&nbsp;
&nbsp;import jakarta.validation.constraints.NotNull;
&nbsp;import lombok.Builder;
&nbsp;import lombok.Getter;
&nbsp;import lombok.ToString;
&nbsp;
&nbsp;import java.time.LocalDate;
&nbsp;
&nbsp;/**
&nbsp; * DTO for union due information updates matching UnionDueUpdateRequest schema from API specification.
&nbsp; * Contains union due mandate status and value date information.
&nbsp; */
&nbsp;@Builder
&nbsp;@Getter
&nbsp;@ToString
&nbsp;public class UnionDueUpdateInfo {
&nbsp;    
&nbsp;    /**
&nbsp;     * Union due mandate status - whether the citizen mandates the union to pay union dues
&nbsp;     */
&nbsp;    @NotNull(message = &quot;Union due status is required&quot;)
&nbsp;    private Boolean unionDue;
&nbsp;    
&nbsp;    /**
&nbsp;     * Value date for when the union due information becomes effective
&nbsp;     */
&nbsp;    @NotNull(message = &quot;Valid from date is required&quot;)
&nbsp;    private LocalDate validFrom;
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
