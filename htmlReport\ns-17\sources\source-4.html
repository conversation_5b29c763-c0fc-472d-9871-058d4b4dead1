


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > PersonRequestMapperImpl</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.request.mapper</a>
</div>

<h1>Coverage Summary for Class: PersonRequestMapperImpl (be.fgov.onerva.person.backend.request.mapper)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">PersonRequestMapperImpl</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (7/7)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    71.4%
  </span>
  <span class="absValue">
    (15/21)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    87.9%
  </span>
  <span class="absValue">
    (58/66)
  </span>
</td>
</tr>
  <tr>
    <td class="name">PersonRequestMapperImpl$1</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (2/2)
  </span>
</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (8/8)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    71.4%
  </span>
  <span class="absValue">
    (15/21)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    88.2%
  </span>
  <span class="absValue">
    (60/68)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.request.mapper;
&nbsp;
&nbsp;import backend.rest.model.AddressDTO;
&nbsp;import backend.rest.model.CitizenRequestDTO;
&nbsp;import backend.rest.model.CitizenRequestPageDTO;
&nbsp;import backend.rest.model.PaymentTypeDTO;
&nbsp;import be.fgov.onerva.person.backend.request.model.Address;
&nbsp;import be.fgov.onerva.person.backend.request.model.PaymentType;
&nbsp;import be.fgov.onerva.person.backend.request.model.PersonRequest;
&nbsp;import be.fgov.onerva.person.backend.request.model.PersonRequestType;
&nbsp;import java.util.ArrayList;
&nbsp;import java.util.List;
&nbsp;import javax.annotation.processing.Generated;
&nbsp;import org.springframework.data.domain.Page;
&nbsp;
&nbsp;@Generated(
&nbsp;    value = &quot;org.mapstruct.ap.MappingProcessor&quot;,
&nbsp;    date = &quot;2025-07-16T14:12:59+0200&quot;,
&nbsp;    comments = &quot;version: 1.6.3, compiler: javac, environment: Java 21.0.6 (Eclipse Adoptium)&quot;
&nbsp;)
<b class="fc">&nbsp;public class PersonRequestMapperImpl implements PersonRequestMapper {</b>
&nbsp;
&nbsp;    @Override
&nbsp;    public CitizenRequestDTO toDto(PersonRequest entity) {
<b class="pc">&nbsp;        if ( entity == null ) {</b>
<b class="nc">&nbsp;            return null;</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        CitizenRequestDTO citizenRequestDTO = new CitizenRequestDTO();</b>
&nbsp;
<b class="fc">&nbsp;        citizenRequestDTO.setId( entity.getId() );</b>
<b class="fc">&nbsp;        citizenRequestDTO.setCreated( PersonRequestMapper.map( entity.getCreated() ) );</b>
<b class="fc">&nbsp;        citizenRequestDTO.setNiss( entity.getNiss() );</b>
<b class="fc">&nbsp;        citizenRequestDTO.setFirstname( entity.getFirstname() );</b>
<b class="fc">&nbsp;        citizenRequestDTO.setLastname( entity.getLastname() );</b>
<b class="fc">&nbsp;        citizenRequestDTO.setCorrelationId( entity.getCorrelationId() );</b>
<b class="fc">&nbsp;        citizenRequestDTO.setSent( entity.isSent() );</b>
<b class="fc">&nbsp;        citizenRequestDTO.setUpdated( PersonRequestMapper.map( entity.getUpdated() ) );</b>
<b class="fc">&nbsp;        citizenRequestDTO.setRetryCount( entity.getRetryCount() );</b>
<b class="fc">&nbsp;        citizenRequestDTO.setReturnCode( entity.getReturnCode() );</b>
<b class="fc">&nbsp;        citizenRequestDTO.setError( entity.getError() );</b>
<b class="fc">&nbsp;        citizenRequestDTO.setType( personRequestTypeToTypeEnum( entity.getType() ) );</b>
<b class="fc">&nbsp;        citizenRequestDTO.setNationalityCode( entity.getNationalityCode() );</b>
<b class="fc">&nbsp;        citizenRequestDTO.setPaymentType( paymentTypeToPaymentTypeDTO( entity.getPaymentType() ) );</b>
<b class="fc">&nbsp;        citizenRequestDTO.setUnionDue( entity.getUnionDue() );</b>
<b class="fc">&nbsp;        citizenRequestDTO.setValueDate( entity.getValueDate() );</b>
<b class="fc">&nbsp;        citizenRequestDTO.setAddress( addressToAddressDTO( entity.getAddress() ) );</b>
<b class="fc">&nbsp;        citizenRequestDTO.setUsername( entity.getUsername() );</b>
&nbsp;
<b class="fc">&nbsp;        return citizenRequestDTO;</b>
&nbsp;    }
&nbsp;
&nbsp;    @Override
&nbsp;    public CitizenRequestPageDTO toDto(Page&lt;PersonRequest&gt; page) {
<b class="pc">&nbsp;        if ( page == null ) {</b>
<b class="nc">&nbsp;            return null;</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        CitizenRequestPageDTO citizenRequestPageDTO = new CitizenRequestPageDTO();</b>
&nbsp;
<b class="fc">&nbsp;        citizenRequestPageDTO.setPageNumber( page.getNumber() );</b>
<b class="fc">&nbsp;        citizenRequestPageDTO.setPageSize( page.getSize() );</b>
<b class="fc">&nbsp;        citizenRequestPageDTO.setTotalPage( page.getTotalPages() );</b>
<b class="fc">&nbsp;        citizenRequestPageDTO.setIsFirst( page.isFirst() );</b>
<b class="fc">&nbsp;        citizenRequestPageDTO.setIsLast( page.isLast() );</b>
<b class="fc">&nbsp;        if ( page.hasContent() ) {</b>
<b class="fc">&nbsp;            citizenRequestPageDTO.setContent( personRequestListToCitizenRequestDTOList( page.getContent() ) );</b>
&nbsp;        }
<b class="fc">&nbsp;        citizenRequestPageDTO.setTotalElements( (int) page.getTotalElements() );</b>
&nbsp;
<b class="fc">&nbsp;        return citizenRequestPageDTO;</b>
&nbsp;    }
&nbsp;
&nbsp;    protected CitizenRequestDTO.TypeEnum personRequestTypeToTypeEnum(PersonRequestType personRequestType) {
<b class="pc">&nbsp;        if ( personRequestType == null ) {</b>
<b class="nc">&nbsp;            return null;</b>
&nbsp;        }
&nbsp;
&nbsp;        CitizenRequestDTO.TypeEnum typeEnum;
&nbsp;
<b class="pc">&nbsp;        switch ( personRequestType ) {</b>
<b class="fc">&nbsp;            case CREATE: typeEnum = CitizenRequestDTO.TypeEnum.CREATE;</b>
&nbsp;            break;
<b class="fc">&nbsp;            case UPDATE: typeEnum = CitizenRequestDTO.TypeEnum.UPDATE;</b>
&nbsp;            break;
<b class="nc">&nbsp;            default: throw new IllegalArgumentException( &quot;Unexpected enum constant: &quot; + personRequestType );</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        return typeEnum;</b>
&nbsp;    }
&nbsp;
&nbsp;    protected PaymentTypeDTO paymentTypeToPaymentTypeDTO(PaymentType paymentType) {
<b class="fc">&nbsp;        if ( paymentType == null ) {</b>
<b class="fc">&nbsp;            return null;</b>
&nbsp;        }
&nbsp;
&nbsp;        PaymentTypeDTO paymentTypeDTO;
&nbsp;
<b class="pc">&nbsp;        switch ( paymentType ) {</b>
<b class="nc">&nbsp;            case BANK_TRANSFER: paymentTypeDTO = PaymentTypeDTO.BANK_TRANSFER;</b>
&nbsp;            break;
<b class="fc">&nbsp;            case CIRCULAR_CHEQUE: paymentTypeDTO = PaymentTypeDTO.CIRCULAR_CHEQUE;</b>
&nbsp;            break;
<b class="nc">&nbsp;            case OTHER_BANK_TRANSFER: paymentTypeDTO = PaymentTypeDTO.OTHER_BANK_TRANSFER;</b>
&nbsp;            break;
<b class="nc">&nbsp;            default: throw new IllegalArgumentException( &quot;Unexpected enum constant: &quot; + paymentType );</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        return paymentTypeDTO;</b>
&nbsp;    }
&nbsp;
&nbsp;    protected AddressDTO addressToAddressDTO(Address address) {
<b class="fc">&nbsp;        if ( address == null ) {</b>
<b class="fc">&nbsp;            return null;</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        AddressDTO addressDTO = new AddressDTO();</b>
&nbsp;
<b class="fc">&nbsp;        addressDTO.setStreet( address.getStreet() );</b>
<b class="fc">&nbsp;        addressDTO.setNumber( address.getNumber() );</b>
<b class="fc">&nbsp;        addressDTO.setBox( address.getBox() );</b>
<b class="fc">&nbsp;        addressDTO.setZip( address.getZip() );</b>
<b class="fc">&nbsp;        addressDTO.setCity( address.getCity() );</b>
<b class="fc">&nbsp;        addressDTO.setCountryCode( address.getCountryCode() );</b>
&nbsp;
<b class="fc">&nbsp;        return addressDTO;</b>
&nbsp;    }
&nbsp;
&nbsp;    protected List&lt;CitizenRequestDTO&gt; personRequestListToCitizenRequestDTOList(List&lt;PersonRequest&gt; list) {
<b class="pc">&nbsp;        if ( list == null ) {</b>
<b class="nc">&nbsp;            return null;</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        List&lt;CitizenRequestDTO&gt; list1 = new ArrayList&lt;CitizenRequestDTO&gt;( list.size() );</b>
<b class="fc">&nbsp;        for ( PersonRequest personRequest : list ) {</b>
<b class="fc">&nbsp;            list1.add( toDto( personRequest ) );</b>
&nbsp;        }
&nbsp;
<b class="fc">&nbsp;        return list1;</b>
&nbsp;    }
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
