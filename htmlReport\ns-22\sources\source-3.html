


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenInfoGeneralRepository</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.citizeninfo.persistence</a>
</div>

<h1>Coverage Summary for Class: CitizenInfoGeneralRepository (be.fgov.onerva.person.backend.citizeninfo.persistence)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
</tr>
  <tr>
    <td class="name">CitizenInfoGeneralRepository$MockitoMock$r0Vtt0J5</td>
  </tr>
  <tr>
    <td class="name">CitizenInfoGeneralRepository$MockitoMock$r0Vtt0J5$auxiliary$gqZwkAjY</td>
  </tr>
  <tr>
    <td class="name">CitizenInfoGeneralRepository$MockitoMock$r0Vtt0J5$auxiliary$okZWr1qs</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.citizeninfo.persistence;
&nbsp;
&nbsp;import be.fgov.onerva.person.backend.citizeninfo.model.CitizenInfoGeneral;
&nbsp;import org.springframework.data.jpa.repository.JpaRepository;
&nbsp;import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
&nbsp;
&nbsp;
&nbsp;import java.util.List;
&nbsp;
&nbsp;public interface CitizenInfoGeneralRepository extends JpaRepository&lt;CitizenInfoGeneral, String&gt;, JpaSpecificationExecutor&lt;CitizenInfoGeneral&gt; {
&nbsp;
&nbsp;    List&lt;CitizenInfoGeneral&gt; getCitizenInfoGeneralByNumBoxIn(List&lt;Integer&gt; numbox);
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
