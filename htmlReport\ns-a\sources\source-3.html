


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenInfoGeneral</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.citizeninfo.model</a>
</div>

<h1>Coverage Summary for Class: CitizenInfoGeneral (be.fgov.onerva.person.backend.citizeninfo.model)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
</tr>
  <tr>
    <td class="name">CitizenInfoGeneral$HibernateInstantiator$ezIGSq9s</td>
  </tr>
  <tr>
    <td class="name">CitizenInfoGeneral$HibernateProxy$6PkSQ2DI</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.citizeninfo.model;
&nbsp;
&nbsp;import lombok.*;
&nbsp;
&nbsp;import jakarta.persistence.Column;
&nbsp;import jakarta.persistence.Entity;
&nbsp;import jakarta.persistence.Id;
&nbsp;import jakarta.persistence.Table;
&nbsp;
&nbsp;@Getter
&nbsp;@Builder
&nbsp;@AllArgsConstructor
&nbsp;@NoArgsConstructor
&nbsp;@Entity
&nbsp;@Table(name = &quot;general_ds&quot;, schema = &quot;dbo&quot;)
&nbsp;public class CitizenInfoGeneral {
&nbsp;    @Id
&nbsp;    private long id;
&nbsp;
&nbsp;    private int numBox;
&nbsp;
&nbsp;    @Column(name = &quot;nom_prenom_gn&quot;)
&nbsp;    private String fullName;
&nbsp;
&nbsp;    @Column(name = &quot;adresse&quot;)
&nbsp;    private String address;
&nbsp;
&nbsp;    @Column(name = &quot;code_post&quot;)
&nbsp;    private Integer zipCode;
&nbsp;
&nbsp;    private Integer codeResid;
&nbsp;
&nbsp;    @Column(name = &quot;sect_op&quot;)
&nbsp;    private Integer OP;
&nbsp;
&nbsp;    @Column(name = &quot;num_br&quot;)
&nbsp;    private Integer unemploymentOffice;
&nbsp;
&nbsp;    @Column(name = &quot;cpte_iban_gn&quot;)
&nbsp;    private String iban;
&nbsp;
&nbsp;    @Column(name = &quot;cpte_bic_gn&quot;)
&nbsp;    private String bic;
&nbsp;
&nbsp;    @Column(name = &quot;cpte_tit_gn&quot;)
&nbsp;    private String bankAccountHolder;
&nbsp;
&nbsp;    @Column(name = &quot;mode_pay_gn&quot;)
&nbsp;    private Integer paymentMode;
&nbsp;
&nbsp;    @Column(name = &quot;flag_v_cpte&quot;)
&nbsp;    private boolean flagVCpte;
&nbsp;
&nbsp;    @Column(name = &quot;date_m_cpte&quot;)
&nbsp;    private Integer dateMCpte;
&nbsp;
&nbsp;    @Column(name = &quot;date_v_cpte&quot;)
&nbsp;    private Integer dateVCpte;
&nbsp;
&nbsp;    @Column(name = &quot;date_naiss_gn&quot;)
&nbsp;    private Integer birthDate;
&nbsp;
&nbsp;    @Column(name = &quot;date_deces_car_gn&quot;)
&nbsp;    private Integer deceasedDate;
&nbsp;
&nbsp;    @Column(name = &quot;langue_gn&quot;)
&nbsp;    private Integer language;
&nbsp;
&nbsp;    @Column(name = &quot;sexe_gn&quot;)
&nbsp;    private int sex;
&nbsp;
&nbsp;    private boolean flagNation;
&nbsp;
&nbsp;    @Column(name = &quot;date_v_siggen&quot;)
&nbsp;    private Integer dateVSiggen;
&nbsp;
&nbsp;    @Column(name= &quot;flag_v_siggen&quot;)
&nbsp;    private boolean flagSiggen;
&nbsp;
&nbsp;    @Column(name= &quot;mandat_syndic&quot;)
&nbsp;    private String  unionDue;
&nbsp;
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
