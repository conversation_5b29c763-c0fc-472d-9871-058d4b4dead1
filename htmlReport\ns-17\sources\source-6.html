


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > PersonEventMapping</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.request.mapper</a>
</div>

<h1>Coverage Summary for Class: PersonEventMapping (be.fgov.onerva.person.backend.request.mapper)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
</tr>
<tr>
  <td class="name">PersonEventMapping</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.request.mapper;
&nbsp;
&nbsp;import org.mapstruct.Mapping;
&nbsp;
&nbsp;import java.lang.annotation.Retention;
&nbsp;import java.lang.annotation.RetentionPolicy;
&nbsp;
&nbsp;@Retention(RetentionPolicy.CLASS)
&nbsp;
&nbsp;@Mapping(target = &quot;id&quot;, source = &quot;request.id&quot;)
&nbsp;@Mapping(target = &quot;ssin&quot;, source = &quot;request.niss&quot;)
&nbsp;@Mapping(target = &quot;additionalProperties&quot;, ignore = true)
&nbsp;@interface PersonEventMapping {
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
