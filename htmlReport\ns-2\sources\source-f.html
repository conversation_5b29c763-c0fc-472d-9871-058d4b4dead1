


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > DataReturnedDTO</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">backend.rest.model</a>
</div>

<h1>Coverage Summary for Class: DataReturnedDTO (backend.rest.model)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">DataReturnedDTO</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/6)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/4)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/11)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package backend.rest.model;
&nbsp;
&nbsp;import java.net.URI;
&nbsp;import java.util.Objects;
&nbsp;import com.fasterxml.jackson.annotation.JsonValue;
&nbsp;import java.time.OffsetDateTime;
&nbsp;import jakarta.validation.Valid;
&nbsp;import jakarta.validation.constraints.*;
&nbsp;import org.hibernate.validator.constraints.*;
&nbsp;import io.swagger.v3.oas.annotations.media.Schema;
&nbsp;
&nbsp;
&nbsp;import java.util.*;
&nbsp;import jakarta.annotation.Generated;
&nbsp;
&nbsp;import com.fasterxml.jackson.annotation.JsonCreator;
&nbsp;import com.fasterxml.jackson.annotation.JsonValue;
&nbsp;
&nbsp;/**
&nbsp; * Gets or Sets DataReturned
&nbsp; */
&nbsp;
<b class="nc">&nbsp;@Generated(value = &quot;org.openapitools.codegen.languages.SpringCodegen&quot;, date = &quot;2025-07-16T14:11:30.627767400+02:00[Europe/Brussels]&quot;, comments = &quot;Generator version: 7.12.0&quot;)</b>
&nbsp;public enum DataReturnedDTO {
&nbsp;  
<b class="nc">&nbsp;  FULL(&quot;FULL&quot;),</b>
&nbsp;  
<b class="nc">&nbsp;  SUMMARY(&quot;SUMMARY&quot;);</b>
&nbsp;
&nbsp;  private String value;
&nbsp;
<b class="nc">&nbsp;  DataReturnedDTO(String value) {</b>
<b class="nc">&nbsp;    this.value = value;</b>
&nbsp;  }
&nbsp;
&nbsp;  @JsonValue
&nbsp;  public String getValue() {
<b class="nc">&nbsp;    return value;</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public String toString() {
<b class="nc">&nbsp;    return String.valueOf(value);</b>
&nbsp;  }
&nbsp;
&nbsp;  @JsonCreator
&nbsp;  public static DataReturnedDTO fromValue(String value) {
<b class="nc">&nbsp;    for (DataReturnedDTO b : DataReturnedDTO.values()) {</b>
<b class="nc">&nbsp;      if (b.value.equals(value)) {</b>
<b class="nc">&nbsp;        return b;</b>
&nbsp;      }
&nbsp;    }
<b class="nc">&nbsp;    throw new IllegalArgumentException(&quot;Unexpected value &#39;&quot; + value + &quot;&#39;&quot;);</b>
&nbsp;  }
&nbsp;}
&nbsp;
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
