


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > PersonRequest</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">be.fgov.onerva.person.backend.request.model</a>
</div>

<h1>Coverage Summary for Class: PersonRequest (be.fgov.onerva.person.backend.request.model)</h1>

<table class="coverageStats">

<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">PersonRequest</td>
<td class="coverageStat">
  <span class="percent">
    80%
  </span>
  <span class="absValue">
    (8/10)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    55.2%
  </span>
  <span class="absValue">
    (16/29)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    67.9%
  </span>
  <span class="absValue">
    (36/53)
  </span>
</td>
</tr>
  <tr>
    <td class="name">PersonRequest$1</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
  </tr>
  <tr>
    <td class="name">PersonRequest$HibernateInstantiator$RFRpVrmF</td>
  </tr>
  <tr>
    <td class="name">PersonRequest$HibernateProxy$deWQ41uo</td>
  </tr>
  <tr>
    <td class="name">PersonRequest$PersonRequestBuilder</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (2/2)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (4/4)
  </span>
</td>
  </tr>
<tr>
  <td class="name"><strong>Total</strong></td>
<td class="coverageStat">
  <span class="percent">
    84.6%
  </span>
  <span class="absValue">
    (11/13)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    55.2%
  </span>
  <span class="absValue">
    (16/29)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    70.7%
  </span>
  <span class="absValue">
    (41/58)
  </span>
</td>
</tr>
</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package be.fgov.onerva.person.backend.request.model;
&nbsp;
&nbsp;import jakarta.persistence.*;
&nbsp;import jakarta.validation.constraints.Max;
&nbsp;import jakarta.validation.constraints.Min;
&nbsp;import jakarta.validation.constraints.NotNull;
&nbsp;import jakarta.validation.constraints.Pattern;
&nbsp;import lombok.*;
&nbsp;import org.apache.commons.lang3.StringUtils;
&nbsp;
&nbsp;import java.time.LocalDate;
&nbsp;import java.time.LocalDateTime;
&nbsp;import java.util.Formatter;
&nbsp;import java.util.Locale;
&nbsp;
&nbsp;@Builder
&nbsp;@NoArgsConstructor(access = AccessLevel.PACKAGE)
&nbsp;@AllArgsConstructor
&nbsp;@Getter
&nbsp;@Entity
&nbsp;public class PersonRequest {
&nbsp;
&nbsp;    public static final int ERROR_LENGTH = 2000;
&nbsp;
&nbsp;    static final int INSS_LENGTH = 11;
&nbsp;    static final int NAMES_LENGTH = 30;
&nbsp;    static final int ID_LENGTH = 19;
&nbsp;    static final int CODE_LENGTH = 5;
&nbsp;    static final int MAX_LAST_NAME_SIZE = 24;
&nbsp;    static final int MAX_ADDRESS_SIZE = 30;
&nbsp;
&nbsp;    public static final String ID_FORMAT = &quot;%0&quot; + ID_LENGTH + &#39;d&#39;;
&nbsp;
&nbsp;    static final String CREATE = &quot;0001&quot;;
&nbsp;    static final String UPDATE = &quot;0002&quot;;
&nbsp;
&nbsp;    @Id
&nbsp;    @GeneratedValue(strategy = GenerationType.IDENTITY)
&nbsp;    private long id;
&nbsp;    @NotNull
&nbsp;    @Column(updatable = false)
&nbsp;    private LocalDateTime created;
&nbsp;    @NotNull
&nbsp;    @Pattern(regexp = &quot;\\d{11}&quot;)
&nbsp;    private String niss;
&nbsp;    private String firstname;
&nbsp;    private String lastname;
&nbsp;    private boolean sent;
&nbsp;    private LocalDateTime updated;
&nbsp;    private int retryCount;
&nbsp;    private Integer returnCode;
&nbsp;    private String error;
&nbsp;    private String correlationId;
&nbsp;    @NotNull
&nbsp;    @Enumerated(EnumType.STRING)
&nbsp;    private PersonRequestType type;
&nbsp;    private Integer nationalityCode;
&nbsp;    @Enumerated(EnumType.STRING)
&nbsp;    private PaymentType paymentType;
&nbsp;    private Boolean unionDue;
&nbsp;    private LocalDate valueDate;
&nbsp;    private Address address;
&nbsp;    private String username;
&nbsp;    private Integer operatorCode;
&nbsp;
&nbsp;    // New fields for foreign addresses, bank info, and birthdate
&nbsp;    private LocalDate birthDate;
&nbsp;    @Column(length = 34)
&nbsp;    private String iban;
&nbsp;    @Column(length = 11)
&nbsp;    private String bic;
&nbsp;    @Column(length = 30)
&nbsp;    private String accountHolder;
&nbsp;    @Min(1)
&nbsp;    @Max(3)
&nbsp;    private Integer languageCode;
&nbsp;    private LocalDate bankInfoValueDate;
&nbsp;    private LocalDate unionDueValueDate;
&nbsp;    @Min(1)
&nbsp;    @Max(99)
&nbsp;    private Integer unemploymentOffice;
&nbsp;
&nbsp;    public void incrementRetryCount() {
<b class="fc">&nbsp;        retryCount++;</b>
&nbsp;    }
&nbsp;
&nbsp;    public void markAsSent() {
<b class="fc">&nbsp;        sent = true;</b>
<b class="fc">&nbsp;        updated = LocalDateTime.now();</b>
&nbsp;    }
&nbsp;
&nbsp;    public void logError(String error) {
<b class="nc">&nbsp;        this.error = error;</b>
<b class="nc">&nbsp;        updated = LocalDateTime.now();</b>
&nbsp;    }
&nbsp;
&nbsp;    public String toRecord() {
<b class="pc">&nbsp;        if (type == PersonRequestType.CREATE) {</b>
<b class="fc">&nbsp;            return toCreateRecord();</b>
&nbsp;        }
<b class="nc">&nbsp;        throw new IllegalStateException(&quot;Unknown type: &quot; + type);</b>
<b class="nc">&nbsp;    }</b>
&nbsp;
<b class="nc">&nbsp;    String toCreateRecord() {</b>
&nbsp;        StringBuilder builder = new StringBuilder(CREATE);
&nbsp;        new Formatter(builder).format(ID_FORMAT, getId());
&nbsp;        builder.append(getNiss())
<b class="fc">&nbsp;                .append(getOperatorCode() == null ? &quot;0000&quot; : String.format(&quot;%04d&quot;, getOperatorCode()))</b>
<b class="fc">&nbsp;                .append(formatNames(getFirstname(), getLastname()));</b>
<b class="fc">&nbsp;</b>
<b class="pc">&nbsp;        return builder.toString();</b>
<b class="fc">&nbsp;    }</b>
&nbsp;
<b class="fc">&nbsp;    static StringBuilder formatNames(String firstname, String lastname) {</b>
&nbsp;        StringBuilder builder = new StringBuilder(50)
&nbsp;                .append(toUpper(lastname, MAX_LAST_NAME_SIZE))
&nbsp;                .append(&#39;,&#39;);
<b class="nc">&nbsp;</b>
<b class="nc">&nbsp;        int spaceLeft = NAMES_LENGTH - builder.length();</b>
<b class="nc">&nbsp;        builder.append(toUpper(firstname, spaceLeft));</b>
<b class="nc">&nbsp;</b>
<b class="nc">&nbsp;        while (builder.length() &lt; NAMES_LENGTH) {</b>
<b class="nc">&nbsp;            builder.append(&#39; &#39;);</b>
<b class="nc">&nbsp;        }</b>
<b class="nc">&nbsp;</b>
<b class="nc">&nbsp;        return builder;</b>
<b class="nc">&nbsp;    }</b>
<b class="nc">&nbsp;</b>
<b class="nc">&nbsp;    static String toUpper(String text, int maxLength) {</b>
&nbsp;        var trimmed = text.trim();
&nbsp;        var formatted = trimmed.length() &gt; maxLength ? trimmed.substring(0, maxLength) : trimmed;
&nbsp;        return StringUtils.stripAccents(formatted.toUpperCase(Locale.ROOT));
<b class="fc">&nbsp;    }</b>
<b class="fc">&nbsp;</b>
<b class="fc">&nbsp;    static String formatAddress(Address address) {</b>
&nbsp;        String street = address.getStreet().trim();
<b class="fc">&nbsp;        String nbr = address.getNumber() == null ? &quot;&quot; : &#39; &#39; + address.getNumber().trim().toUpperCase(Locale.ROOT);</b>
<b class="fc">&nbsp;        String box = address.getBox() == null ? &quot;&quot;</b>
&nbsp;                : &#39; &#39; + address.getBox().trim().toUpperCase(Locale.ROOT).replace(&quot;BOITE&quot;, &quot;BTE&quot;);
<b class="fc">&nbsp;</b>
<b class="fc">&nbsp;        int max = MAX_ADDRESS_SIZE - nbr.length() - box.length();</b>
&nbsp;
&nbsp;        String addressLine = toUpper(street, max) + nbr + box;
<b class="fc">&nbsp;        int size = addressLine.length();</b>
&nbsp;
&nbsp;        return size == MAX_ADDRESS_SIZE ? addressLine : addressLine + &quot; &quot;.repeat(MAX_ADDRESS_SIZE - size);
&nbsp;    }
<b class="fc">&nbsp;</b>
<b class="fc">&nbsp;    public static char toRecord(PaymentType paymentType) {</b>
<b class="fc">&nbsp;        if (paymentType == null) {</b>
&nbsp;            return &#39; &#39;;
&nbsp;        }
&nbsp;        return switch (paymentType) {
<b class="fc">&nbsp;            case BANK_TRANSFER -&gt; &#39;1&#39;;</b>
<b class="pc">&nbsp;            case OTHER_BANK_TRANSFER -&gt; &#39;2&#39;;</b>
<b class="fc">&nbsp;            case CIRCULAR_CHEQUE -&gt; &#39;3&#39;;</b>
<b class="fc">&nbsp;        };</b>
&nbsp;    }
<b class="fc">&nbsp;</b>
&nbsp;    public static class PersonRequestBuilder {
<b class="fc">&nbsp;        private String firstname;</b>
<b class="fc">&nbsp;        private String lastname;</b>
&nbsp;
<b class="fc">&nbsp;        public PersonRequestBuilder firstname(String name) {</b>
&nbsp;            firstname = name.trim();
&nbsp;            return this;
&nbsp;        }
<b class="fc">&nbsp;</b>
<b class="fc">&nbsp;        public PersonRequestBuilder lastname(String name) {</b>
&nbsp;            lastname = name.trim();
<b class="pc">&nbsp;            return this;</b>
<b class="fc">&nbsp;        }</b>
<b class="fc">&nbsp;</b>
<b class="fc">&nbsp;    }</b>
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
