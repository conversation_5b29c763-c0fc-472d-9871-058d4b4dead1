


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > BankUpdateRequestDTO</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">backend.rest.model</a>
</div>

<h1>Coverage Summary for Class: BankUpdateRequestDTO (backend.rest.model)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">BankUpdateRequestDTO</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    66.7%
  </span>
  <span class="absValue">
    (14/21)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/16)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    35.4%
  </span>
  <span class="absValue">
    (17/48)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package backend.rest.model;
&nbsp;
&nbsp;import java.net.URI;
&nbsp;import java.util.Objects;
&nbsp;import backend.rest.model.PaymentTypeDTO;
&nbsp;import com.fasterxml.jackson.annotation.JsonProperty;
&nbsp;import com.fasterxml.jackson.annotation.JsonCreator;
&nbsp;import com.fasterxml.jackson.annotation.JsonTypeName;
&nbsp;import com.fasterxml.jackson.annotation.JsonValue;
&nbsp;import java.time.LocalDate;
&nbsp;import org.springframework.format.annotation.DateTimeFormat;
&nbsp;import org.springframework.lang.Nullable;
&nbsp;import java.time.OffsetDateTime;
&nbsp;import jakarta.validation.Valid;
&nbsp;import jakarta.validation.constraints.*;
&nbsp;import org.hibernate.validator.constraints.*;
&nbsp;import io.swagger.v3.oas.annotations.media.Schema;
&nbsp;
&nbsp;
&nbsp;import java.util.*;
&nbsp;import jakarta.annotation.Generated;
&nbsp;
&nbsp;/**
&nbsp; * Bank information update request
&nbsp; */
&nbsp;
&nbsp;@Schema(name = &quot;BankUpdateRequest&quot;, description = &quot;Bank information update request&quot;)
&nbsp;@JsonTypeName(&quot;BankUpdateRequest&quot;)
&nbsp;@Generated(value = &quot;org.openapitools.codegen.languages.SpringCodegen&quot;, date = &quot;2025-07-16T14:11:30.*********+02:00[Europe/Brussels]&quot;, comments = &quot;Generator version: 7.12.0&quot;)
&nbsp;public class BankUpdateRequestDTO {
&nbsp;
&nbsp;  private String iban;
&nbsp;
&nbsp;  private @Nullable String bic;
&nbsp;
&nbsp;  private @Nullable String accountHolder;
&nbsp;
&nbsp;  private PaymentTypeDTO paymentType;
&nbsp;
&nbsp;  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
&nbsp;  private LocalDate validFrom;
&nbsp;
&nbsp;  public BankUpdateRequestDTO() {
<b class="fc">&nbsp;    super();</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Constructor with only required parameters
&nbsp;   */
<b class="nc">&nbsp;  public BankUpdateRequestDTO(String iban, PaymentTypeDTO paymentType, LocalDate validFrom) {</b>
<b class="nc">&nbsp;    this.iban = iban;</b>
<b class="nc">&nbsp;    this.paymentType = paymentType;</b>
<b class="nc">&nbsp;    this.validFrom = validFrom;</b>
&nbsp;  }
&nbsp;
&nbsp;  public BankUpdateRequestDTO iban(String iban) {
<b class="fc">&nbsp;    this.iban = iban;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * International Bank Account Number
&nbsp;   * @return iban
&nbsp;   */
&nbsp;  @NotNull @Pattern(regexp = &quot;^[A-Z]{2}[0-9]{2}[A-Z0-9]{1,30}$&quot;) @Size(max = 34) 
&nbsp;  @Schema(name = &quot;iban&quot;, example = &quot;****************&quot;, description = &quot;International Bank Account Number&quot;, requiredMode = Schema.RequiredMode.REQUIRED)
&nbsp;  @JsonProperty(&quot;iban&quot;)
&nbsp;  public String getIban() {
<b class="fc">&nbsp;    return iban;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setIban(String iban) {
<b class="fc">&nbsp;    this.iban = iban;</b>
&nbsp;  }
&nbsp;
&nbsp;  public BankUpdateRequestDTO bic(String bic) {
<b class="nc">&nbsp;    this.bic = bic;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Bank Identifier Code (mandatory for non-Belgian IBANs)
&nbsp;   * @return bic
&nbsp;   */
&nbsp;  @Pattern(regexp = &quot;^[A-Z0-9]{8,11}$&quot;) @Size(max = 11) 
&nbsp;  @Schema(name = &quot;bic&quot;, example = &quot;BBRUBEBCXXX&quot;, description = &quot;Bank Identifier Code (mandatory for non-Belgian IBANs)&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;bic&quot;)
&nbsp;  public String getBic() {
<b class="fc">&nbsp;    return bic;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setBic(String bic) {
<b class="fc">&nbsp;    this.bic = bic;</b>
&nbsp;  }
&nbsp;
&nbsp;  public BankUpdateRequestDTO accountHolder(String accountHolder) {
<b class="nc">&nbsp;    this.accountHolder = accountHolder;</b>
<b class="nc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Account holder name
&nbsp;   * @return accountHolder
&nbsp;   */
&nbsp;  @Size(max = 30) 
&nbsp;  @Schema(name = &quot;accountHolder&quot;, example = &quot;DUPONT JACQUES&quot;, description = &quot;Account holder name&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;accountHolder&quot;)
&nbsp;  public String getAccountHolder() {
<b class="fc">&nbsp;    return accountHolder;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setAccountHolder(String accountHolder) {
<b class="fc">&nbsp;    this.accountHolder = accountHolder;</b>
&nbsp;  }
&nbsp;
&nbsp;  public BankUpdateRequestDTO paymentType(PaymentTypeDTO paymentType) {
<b class="fc">&nbsp;    this.paymentType = paymentType;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get paymentType
&nbsp;   * @return paymentType
&nbsp;   */
&nbsp;  @NotNull @Valid 
&nbsp;  @Schema(name = &quot;paymentType&quot;, requiredMode = Schema.RequiredMode.REQUIRED)
&nbsp;  @JsonProperty(&quot;paymentType&quot;)
&nbsp;  public PaymentTypeDTO getPaymentType() {
<b class="fc">&nbsp;    return paymentType;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setPaymentType(PaymentTypeDTO paymentType) {
<b class="fc">&nbsp;    this.paymentType = paymentType;</b>
&nbsp;  }
&nbsp;
&nbsp;  public BankUpdateRequestDTO validFrom(LocalDate validFrom) {
<b class="fc">&nbsp;    this.validFrom = validFrom;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Valid from date for bank information
&nbsp;   * @return validFrom
&nbsp;   */
&nbsp;  @NotNull @Valid 
&nbsp;  @Schema(name = &quot;validFrom&quot;, description = &quot;Valid from date for bank information&quot;, requiredMode = Schema.RequiredMode.REQUIRED)
&nbsp;  @JsonProperty(&quot;validFrom&quot;)
&nbsp;  public LocalDate getValidFrom() {
<b class="fc">&nbsp;    return validFrom;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setValidFrom(LocalDate validFrom) {
<b class="fc">&nbsp;    this.validFrom = validFrom;</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public boolean equals(Object o) {
<b class="nc">&nbsp;    if (this == o) {</b>
<b class="nc">&nbsp;      return true;</b>
&nbsp;    }
<b class="nc">&nbsp;    if (o == null || getClass() != o.getClass()) {</b>
<b class="nc">&nbsp;      return false;</b>
&nbsp;    }
<b class="nc">&nbsp;    BankUpdateRequestDTO bankUpdateRequest = (BankUpdateRequestDTO) o;</b>
<b class="nc">&nbsp;    return Objects.equals(this.iban, bankUpdateRequest.iban) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.bic, bankUpdateRequest.bic) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.accountHolder, bankUpdateRequest.accountHolder) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.paymentType, bankUpdateRequest.paymentType) &amp;&amp;</b>
<b class="nc">&nbsp;        Objects.equals(this.validFrom, bankUpdateRequest.validFrom);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public int hashCode() {
<b class="nc">&nbsp;    return Objects.hash(iban, bic, accountHolder, paymentType, validFrom);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public String toString() {
<b class="nc">&nbsp;    StringBuilder sb = new StringBuilder();</b>
<b class="nc">&nbsp;    sb.append(&quot;class BankUpdateRequestDTO {\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    iban: &quot;).append(toIndentedString(iban)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    bic: &quot;).append(toIndentedString(bic)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    accountHolder: &quot;).append(toIndentedString(accountHolder)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    paymentType: &quot;).append(toIndentedString(paymentType)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    validFrom: &quot;).append(toIndentedString(validFrom)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;}&quot;);</b>
<b class="nc">&nbsp;    return sb.toString();</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Convert the given object to string with each line indented by 4 spaces
&nbsp;   * (except the first line).
&nbsp;   */
&nbsp;  private String toIndentedString(Object o) {
<b class="nc">&nbsp;    if (o == null) {</b>
<b class="nc">&nbsp;      return &quot;null&quot;;</b>
&nbsp;    }
<b class="nc">&nbsp;    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</b>
&nbsp;  }
&nbsp;}
&nbsp;
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
