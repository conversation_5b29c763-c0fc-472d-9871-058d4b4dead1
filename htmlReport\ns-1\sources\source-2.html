


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenApi</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">backend.api</a>
</div>

<h1>Coverage Summary for Class: CitizenApi (backend.api)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">CitizenApi</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/9)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/12)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    0%
  </span>
  <span class="absValue">
    (0/21)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;/**
&nbsp; * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.12.0).
&nbsp; * https://openapi-generator.tech
&nbsp; * Do not edit the class manually.
&nbsp; */
&nbsp;package backend.api;
&nbsp;
&nbsp;import backend.rest.model.CitizenCreationRequestDTO;
&nbsp;import backend.rest.model.CitizenDTO;
&nbsp;import backend.rest.model.CitizenPageDTO;
&nbsp;import backend.rest.model.CitizenUpdateRequestDTO;
&nbsp;import io.swagger.v3.oas.annotations.ExternalDocumentation;
&nbsp;import io.swagger.v3.oas.annotations.Operation;
&nbsp;import io.swagger.v3.oas.annotations.Parameter;
&nbsp;import io.swagger.v3.oas.annotations.Parameters;
&nbsp;import io.swagger.v3.oas.annotations.media.ArraySchema;
&nbsp;import io.swagger.v3.oas.annotations.media.Content;
&nbsp;import io.swagger.v3.oas.annotations.media.Schema;
&nbsp;import io.swagger.v3.oas.annotations.responses.ApiResponse;
&nbsp;import io.swagger.v3.oas.annotations.security.SecurityRequirement;
&nbsp;import io.swagger.v3.oas.annotations.tags.Tag;
&nbsp;import io.swagger.v3.oas.annotations.enums.ParameterIn;
&nbsp;import org.springframework.http.HttpStatus;
&nbsp;import org.springframework.http.MediaType;
&nbsp;import org.springframework.http.ResponseEntity;
&nbsp;import org.springframework.validation.annotation.Validated;
&nbsp;import org.springframework.web.bind.annotation.*;
&nbsp;import org.springframework.web.context.request.NativeWebRequest;
&nbsp;import org.springframework.web.multipart.MultipartFile;
&nbsp;
&nbsp;import jakarta.validation.Valid;
&nbsp;import jakarta.validation.constraints.*;
&nbsp;import java.util.List;
&nbsp;import java.util.Map;
&nbsp;import java.util.Optional;
&nbsp;import jakarta.annotation.Generated;
&nbsp;
&nbsp;@Generated(value = &quot;org.openapitools.codegen.languages.SpringCodegen&quot;, date = &quot;2025-07-16T14:11:30.627767400+02:00[Europe/Brussels]&quot;, comments = &quot;Generator version: 7.12.0&quot;)
&nbsp;@Validated
&nbsp;@Tag(name = &quot;Citizen&quot;, description = &quot;the Citizen API&quot;)
&nbsp;public interface CitizenApi {
&nbsp;
&nbsp;    default Optional&lt;NativeWebRequest&gt; getRequest() {
<b class="nc">&nbsp;        return Optional.empty();</b>
&nbsp;    }
&nbsp;
&nbsp;    /**
&nbsp;     * POST /citizen
&nbsp;     * Create a new Citizen
&nbsp;     *
&nbsp;     * @param businessDomain Reference the business domain where the citizen need to be created (required)
&nbsp;     * @param allowance True citizen will request allowance, false a citizen will not immediately request allowance (required)
&nbsp;     * @param citizenCreationRequestDTO  (required)
&nbsp;     * @return A valid request to create a new citizen has been created (status code 201)
&nbsp;     *         or An error occurred during the processing of the request (status code 400)
&nbsp;     *         or The requested citizen already exists (status code 409)
&nbsp;     */
&nbsp;    @Operation(
&nbsp;        operationId = &quot;createCitizen&quot;,
&nbsp;        description = &quot;Create a new Citizen&quot;,
&nbsp;        tags = { &quot;Citizen&quot; },
&nbsp;        responses = {
&nbsp;            @ApiResponse(responseCode = &quot;201&quot;, description = &quot;A valid request to create a new citizen has been created&quot;),
&nbsp;            @ApiResponse(responseCode = &quot;400&quot;, description = &quot;An error occurred during the processing of the request&quot;),
&nbsp;            @ApiResponse(responseCode = &quot;409&quot;, description = &quot;The requested citizen already exists&quot;)
&nbsp;        }
&nbsp;    )
&nbsp;    @RequestMapping(
&nbsp;        method = RequestMethod.POST,
&nbsp;        value = &quot;/citizen&quot;
&nbsp;    )
&nbsp;    
&nbsp;    default ResponseEntity&lt;Void&gt; createCitizen(
&nbsp;        @NotNull @Parameter(name = &quot;businessDomain&quot;, description = &quot;Reference the business domain where the citizen need to be created&quot;, required = true, in = ParameterIn.QUERY) @Valid @RequestParam(value = &quot;businessDomain&quot;, required = true) String businessDomain,
&nbsp;        @NotNull @Parameter(name = &quot;allowance&quot;, description = &quot;True citizen will request allowance, false a citizen will not immediately request allowance&quot;, required = true, in = ParameterIn.QUERY) @Valid @RequestParam(value = &quot;allowance&quot;, required = true) Boolean allowance,
&nbsp;        @Parameter(name = &quot;CitizenCreationRequestDTO&quot;, description = &quot;&quot;, required = true) @Valid @RequestBody CitizenCreationRequestDTO citizenCreationRequestDTO
&nbsp;    ) {
<b class="nc">&nbsp;        return new ResponseEntity&lt;&gt;(HttpStatus.NOT_IMPLEMENTED);</b>
&nbsp;
&nbsp;    }
&nbsp;
&nbsp;
&nbsp;    /**
&nbsp;     * GET /citizen/{niss}
&nbsp;     * Get a citizen base on her niss
&nbsp;     *
&nbsp;     * @param niss  (required)
&nbsp;     * @return Citizen (status code 200)
&nbsp;     */
&nbsp;    @Operation(
&nbsp;        operationId = &quot;getByNiss&quot;,
&nbsp;        description = &quot;Get a citizen base on her niss&quot;,
&nbsp;        tags = { &quot;Citizen&quot; },
&nbsp;        responses = {
&nbsp;            @ApiResponse(responseCode = &quot;200&quot;, description = &quot;Citizen&quot;, content = {
&nbsp;                @Content(mediaType = &quot;application/json&quot;, schema = @Schema(implementation = CitizenDTO.class))
&nbsp;            })
&nbsp;        }
&nbsp;    )
&nbsp;    @RequestMapping(
&nbsp;        method = RequestMethod.GET,
&nbsp;        value = &quot;/citizen/{niss}&quot;,
&nbsp;        produces = { &quot;application/json&quot; }
&nbsp;    )
&nbsp;    
&nbsp;    default ResponseEntity&lt;CitizenDTO&gt; getByNiss(
&nbsp;        @Parameter(name = &quot;niss&quot;, description = &quot;&quot;, required = true, in = ParameterIn.PATH) @PathVariable(&quot;niss&quot;) String niss
&nbsp;    ) {
<b class="nc">&nbsp;        getRequest().ifPresent(request -&gt; {</b>
<b class="nc">&nbsp;            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader(&quot;Accept&quot;))) {</b>
<b class="nc">&nbsp;                if (mediaType.isCompatibleWith(MediaType.valueOf(&quot;application/json&quot;))) {</b>
<b class="nc">&nbsp;                    String exampleString = &quot;{ \&quot;zipCode\&quot; : 6, \&quot;firstname\&quot; : \&quot;firstname\&quot;, \&quot;agent\&quot; : true, \&quot;niss\&quot; : \&quot;niss\&quot;, \&quot;numbox\&quot; : 0, \&quot;pensionNumber\&quot; : 1, \&quot;lastname\&quot; : \&quot;lastname\&quot; }&quot;;</b>
<b class="nc">&nbsp;                    ApiUtil.setExampleResponse(request, &quot;application/json&quot;, exampleString);</b>
&nbsp;                    break;
&nbsp;                }
&nbsp;            }
&nbsp;        });
<b class="nc">&nbsp;        return new ResponseEntity&lt;&gt;(HttpStatus.NOT_IMPLEMENTED);</b>
&nbsp;
&nbsp;    }
&nbsp;
&nbsp;
&nbsp;    /**
&nbsp;     * GET /citizen/numbox/{numbox}
&nbsp;     * Get a citizen base on her numbox
&nbsp;     *
&nbsp;     * @param numbox  (required)
&nbsp;     * @return Citizen (status code 200)
&nbsp;     */
&nbsp;    @Operation(
&nbsp;        operationId = &quot;getByNumbox&quot;,
&nbsp;        description = &quot;Get a citizen base on her numbox&quot;,
&nbsp;        tags = { &quot;Citizen&quot; },
&nbsp;        responses = {
&nbsp;            @ApiResponse(responseCode = &quot;200&quot;, description = &quot;Citizen&quot;, content = {
&nbsp;                @Content(mediaType = &quot;application/json&quot;, schema = @Schema(implementation = CitizenDTO.class))
&nbsp;            })
&nbsp;        }
&nbsp;    )
&nbsp;    @RequestMapping(
&nbsp;        method = RequestMethod.GET,
&nbsp;        value = &quot;/citizen/numbox/{numbox}&quot;,
&nbsp;        produces = { &quot;application/json&quot; }
&nbsp;    )
&nbsp;    
&nbsp;    default ResponseEntity&lt;CitizenDTO&gt; getByNumbox(
&nbsp;        @Parameter(name = &quot;numbox&quot;, description = &quot;&quot;, required = true, in = ParameterIn.PATH) @PathVariable(&quot;numbox&quot;) Integer numbox
&nbsp;    ) {
<b class="nc">&nbsp;        getRequest().ifPresent(request -&gt; {</b>
<b class="nc">&nbsp;            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader(&quot;Accept&quot;))) {</b>
<b class="nc">&nbsp;                if (mediaType.isCompatibleWith(MediaType.valueOf(&quot;application/json&quot;))) {</b>
<b class="nc">&nbsp;                    String exampleString = &quot;{ \&quot;zipCode\&quot; : 6, \&quot;firstname\&quot; : \&quot;firstname\&quot;, \&quot;agent\&quot; : true, \&quot;niss\&quot; : \&quot;niss\&quot;, \&quot;numbox\&quot; : 0, \&quot;pensionNumber\&quot; : 1, \&quot;lastname\&quot; : \&quot;lastname\&quot; }&quot;;</b>
<b class="nc">&nbsp;                    ApiUtil.setExampleResponse(request, &quot;application/json&quot;, exampleString);</b>
&nbsp;                    break;
&nbsp;                }
&nbsp;            }
&nbsp;        });
<b class="nc">&nbsp;        return new ResponseEntity&lt;&gt;(HttpStatus.NOT_IMPLEMENTED);</b>
&nbsp;
&nbsp;    }
&nbsp;
&nbsp;
&nbsp;    /**
&nbsp;     * GET /citizen
&nbsp;     * Search citizen based on criteria
&nbsp;     *
&nbsp;     * @param query Uses RSQL syntax to search through citizens (See: https://molgenis.gitbooks.io/molgenis/content/guide-rsql.html\&amp;quot;)  Available fields are:   - numBox   - fullName   - id (That refers to numPens) (required)
&nbsp;     * @param pageNumber  (optional, default to 0)
&nbsp;     * @param pageSize  (optional, default to 10)
&nbsp;     * @return Citizen (status code 200)
&nbsp;     */
&nbsp;    @Operation(
&nbsp;        operationId = &quot;searchCitizen&quot;,
&nbsp;        description = &quot;Search citizen based on criteria&quot;,
&nbsp;        tags = { &quot;Citizen&quot; },
&nbsp;        responses = {
&nbsp;            @ApiResponse(responseCode = &quot;200&quot;, description = &quot;Citizen&quot;, content = {
&nbsp;                @Content(mediaType = &quot;application/json&quot;, schema = @Schema(implementation = CitizenPageDTO.class))
&nbsp;            })
&nbsp;        }
&nbsp;    )
&nbsp;    @RequestMapping(
&nbsp;        method = RequestMethod.GET,
&nbsp;        value = &quot;/citizen&quot;,
&nbsp;        produces = { &quot;application/json&quot; }
&nbsp;    )
&nbsp;    
&nbsp;    default ResponseEntity&lt;CitizenPageDTO&gt; searchCitizen(
&nbsp;        @NotNull @Parameter(name = &quot;query&quot;, description = &quot;Uses RSQL syntax to search through citizens (See: https://molgenis.gitbooks.io/molgenis/content/guide-rsql.html\&quot;)  Available fields are:   - numBox   - fullName   - id (That refers to numPens)&quot;, required = true, in = ParameterIn.QUERY) @Valid @RequestParam(value = &quot;query&quot;, required = true) String query,
&nbsp;        @Min(0) @Parameter(name = &quot;pageNumber&quot;, description = &quot;&quot;, in = ParameterIn.QUERY) @Valid @RequestParam(value = &quot;pageNumber&quot;, required = false, defaultValue = &quot;0&quot;) Integer pageNumber,
&nbsp;        @Parameter(name = &quot;pageSize&quot;, description = &quot;&quot;, in = ParameterIn.QUERY) @Valid @RequestParam(value = &quot;pageSize&quot;, required = false, defaultValue = &quot;10&quot;) Integer pageSize
&nbsp;    ) {
<b class="nc">&nbsp;        getRequest().ifPresent(request -&gt; {</b>
<b class="nc">&nbsp;            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader(&quot;Accept&quot;))) {</b>
<b class="nc">&nbsp;                if (mediaType.isCompatibleWith(MediaType.valueOf(&quot;application/json&quot;))) {</b>
<b class="nc">&nbsp;                    String exampleString = &quot;{ \&quot;isFirst\&quot; : true, \&quot;pageNumber\&quot; : 0, \&quot;isLast\&quot; : true, \&quot;totalPage\&quot; : 1, \&quot;pageSize\&quot; : 6, \&quot;content\&quot; : [ { \&quot;zipCode\&quot; : 6, \&quot;firstname\&quot; : \&quot;firstname\&quot;, \&quot;agent\&quot; : true, \&quot;niss\&quot; : \&quot;niss\&quot;, \&quot;numbox\&quot; : 0, \&quot;pensionNumber\&quot; : 1, \&quot;lastname\&quot; : \&quot;lastname\&quot; }, { \&quot;zipCode\&quot; : 6, \&quot;firstname\&quot; : \&quot;firstname\&quot;, \&quot;agent\&quot; : true, \&quot;niss\&quot; : \&quot;niss\&quot;, \&quot;numbox\&quot; : 0, \&quot;pensionNumber\&quot; : 1, \&quot;lastname\&quot; : \&quot;lastname\&quot; } ], \&quot;totalElements\&quot; : 5 }&quot;;</b>
<b class="nc">&nbsp;                    ApiUtil.setExampleResponse(request, &quot;application/json&quot;, exampleString);</b>
&nbsp;                    break;
&nbsp;                }
&nbsp;            }
&nbsp;        });
<b class="nc">&nbsp;        return new ResponseEntity&lt;&gt;(HttpStatus.NOT_IMPLEMENTED);</b>
&nbsp;
&nbsp;    }
&nbsp;
&nbsp;
&nbsp;    /**
&nbsp;     * PUT /citizen/{niss}
&nbsp;     * Update a citizen with a given niss
&nbsp;     *
&nbsp;     * @param niss  (required)
&nbsp;     * @param username The username that made the change for audit purposes. (required)
&nbsp;     * @param citizenUpdateRequestDTO  (required)
&nbsp;     * @return Citizen update request accepted (status code 204)
&nbsp;     *         or The request was invalid. (status code 400)
&nbsp;     *         or Citizen not found (status code 404)
&nbsp;     */
&nbsp;    @Operation(
&nbsp;        operationId = &quot;updateCitizen&quot;,
&nbsp;        description = &quot;Update a citizen with a given niss&quot;,
&nbsp;        tags = { &quot;Citizen&quot; },
&nbsp;        responses = {
&nbsp;            @ApiResponse(responseCode = &quot;204&quot;, description = &quot;Citizen update request accepted&quot;),
&nbsp;            @ApiResponse(responseCode = &quot;400&quot;, description = &quot;The request was invalid.&quot;),
&nbsp;            @ApiResponse(responseCode = &quot;404&quot;, description = &quot;Citizen not found&quot;)
&nbsp;        }
&nbsp;    )
&nbsp;    @RequestMapping(
&nbsp;        method = RequestMethod.PUT,
&nbsp;        value = &quot;/citizen/{niss}&quot;
&nbsp;    )
&nbsp;    
&nbsp;    default ResponseEntity&lt;Void&gt; updateCitizen(
&nbsp;        @Parameter(name = &quot;niss&quot;, description = &quot;&quot;, required = true, in = ParameterIn.PATH) @PathVariable(&quot;niss&quot;) String niss,
&nbsp;        @NotNull @Parameter(name = &quot;username&quot;, description = &quot;The username that made the change for audit purposes.&quot;, required = true, in = ParameterIn.QUERY) @Valid @RequestParam(value = &quot;username&quot;, required = true) String username,
&nbsp;        @Parameter(name = &quot;CitizenUpdateRequestDTO&quot;, description = &quot;&quot;, required = true) @Valid @RequestBody CitizenUpdateRequestDTO citizenUpdateRequestDTO
&nbsp;    ) {
<b class="nc">&nbsp;        return new ResponseEntity&lt;&gt;(HttpStatus.NOT_IMPLEMENTED);</b>
&nbsp;
&nbsp;    }
&nbsp;
&nbsp;}
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
