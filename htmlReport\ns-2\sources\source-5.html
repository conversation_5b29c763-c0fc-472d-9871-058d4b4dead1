


<!DOCTYPE html>
<html id="htmlId">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8"> 
  <title>Coverage Report > CitizenDTO</title>
  <style type="text/css">
    @import "../../css/coverage.css";
    @import "../../css/idea.min.css";
  </style>
  <script type="text/javascript" src="../../js/highlight.min.js"></script>
  <script type="text/javascript" src="../../js/highlightjs-line-numbers.min.js"></script>
</head>

<body>
<div class="content">
<div class="breadCrumbs">
Current scope:     <a href="../../index.html">all classes</a>
    <span class="separator">|</span>
    <a href="../index.html">backend.rest.model</a>
</div>

<h1>Coverage Summary for Class: CitizenDTO (backend.rest.model)</h1>

<table class="coverageStats">
<tr>
  <th class="name">Class</th>
<th class="coverageStat 
">
  Class, %
</th>
<th class="coverageStat 
">
  Method, %
</th>
<th class="coverageStat 
">
  Branch, %
</th>
<th class="coverageStat 
">
  Line, %
</th>
</tr>
<tr>
  <td class="name">CitizenDTO</td>
<td class="coverageStat">
  <span class="percent">
    100%
  </span>
  <span class="absValue">
    (1/1)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    88.5%
  </span>
  <span class="absValue">
    (23/26)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    45%
  </span>
  <span class="absValue">
    (9/20)
  </span>
</td>
<td class="coverageStat">
  <span class="percent">
    69.6%
  </span>
  <span class="absValue">
    (39/56)
  </span>
</td>
</tr>

</table>

<br/>
<br/>


<pre>
<code class="sourceCode" id="sourceCode">&nbsp;package backend.rest.model;
&nbsp;
&nbsp;import java.net.URI;
&nbsp;import java.util.Objects;
&nbsp;import com.fasterxml.jackson.annotation.JsonProperty;
&nbsp;import com.fasterxml.jackson.annotation.JsonCreator;
&nbsp;import com.fasterxml.jackson.annotation.JsonTypeName;
&nbsp;import org.springframework.lang.Nullable;
&nbsp;import java.time.OffsetDateTime;
&nbsp;import jakarta.validation.Valid;
&nbsp;import jakarta.validation.constraints.*;
&nbsp;import org.hibernate.validator.constraints.*;
&nbsp;import io.swagger.v3.oas.annotations.media.Schema;
&nbsp;
&nbsp;
&nbsp;import java.util.*;
&nbsp;import jakarta.annotation.Generated;
&nbsp;
&nbsp;/**
&nbsp; * Citizen information
&nbsp; */
&nbsp;
&nbsp;@Schema(name = &quot;Citizen&quot;, description = &quot;Citizen information&quot;)
&nbsp;@JsonTypeName(&quot;Citizen&quot;)
&nbsp;@Generated(value = &quot;org.openapitools.codegen.languages.SpringCodegen&quot;, date = &quot;2025-07-16T14:11:30.627767400+02:00[Europe/Brussels]&quot;, comments = &quot;Generator version: 7.12.0&quot;)
<b class="fc">&nbsp;public class CitizenDTO {</b>
&nbsp;
&nbsp;  private @Nullable String niss;
&nbsp;
&nbsp;  private @Nullable String firstname;
&nbsp;
&nbsp;  private @Nullable String lastname;
&nbsp;
&nbsp;  private @Nullable Integer numbox;
&nbsp;
&nbsp;  private @Nullable Integer zipCode;
&nbsp;
&nbsp;  private @Nullable Integer pensionNumber;
&nbsp;
&nbsp;  private @Nullable Boolean agent;
&nbsp;
&nbsp;  public CitizenDTO niss(String niss) {
<b class="fc">&nbsp;    this.niss = niss;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get niss
&nbsp;   * @return niss
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;niss&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;niss&quot;)
&nbsp;  public String getNiss() {
<b class="fc">&nbsp;    return niss;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setNiss(String niss) {
<b class="fc">&nbsp;    this.niss = niss;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenDTO firstname(String firstname) {
<b class="fc">&nbsp;    this.firstname = firstname;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get firstname
&nbsp;   * @return firstname
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;firstname&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;firstname&quot;)
&nbsp;  public String getFirstname() {
<b class="fc">&nbsp;    return firstname;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setFirstname(String firstname) {
<b class="fc">&nbsp;    this.firstname = firstname;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenDTO lastname(String lastname) {
<b class="fc">&nbsp;    this.lastname = lastname;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get lastname
&nbsp;   * @return lastname
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;lastname&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;lastname&quot;)
&nbsp;  public String getLastname() {
<b class="fc">&nbsp;    return lastname;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setLastname(String lastname) {
<b class="fc">&nbsp;    this.lastname = lastname;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenDTO numbox(Integer numbox) {
<b class="fc">&nbsp;    this.numbox = numbox;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get numbox
&nbsp;   * @return numbox
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;numbox&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;numbox&quot;)
&nbsp;  public Integer getNumbox() {
<b class="fc">&nbsp;    return numbox;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setNumbox(Integer numbox) {
<b class="fc">&nbsp;    this.numbox = numbox;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenDTO zipCode(Integer zipCode) {
<b class="fc">&nbsp;    this.zipCode = zipCode;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get zipCode
&nbsp;   * @return zipCode
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;zipCode&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;zipCode&quot;)
&nbsp;  public Integer getZipCode() {
<b class="fc">&nbsp;    return zipCode;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setZipCode(Integer zipCode) {
<b class="fc">&nbsp;    this.zipCode = zipCode;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenDTO pensionNumber(Integer pensionNumber) {
<b class="fc">&nbsp;    this.pensionNumber = pensionNumber;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get pensionNumber
&nbsp;   * @return pensionNumber
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;pensionNumber&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;pensionNumber&quot;)
&nbsp;  public Integer getPensionNumber() {
<b class="fc">&nbsp;    return pensionNumber;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setPensionNumber(Integer pensionNumber) {
<b class="fc">&nbsp;    this.pensionNumber = pensionNumber;</b>
&nbsp;  }
&nbsp;
&nbsp;  public CitizenDTO agent(Boolean agent) {
<b class="fc">&nbsp;    this.agent = agent;</b>
<b class="fc">&nbsp;    return this;</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Get agent
&nbsp;   * @return agent
&nbsp;   */
&nbsp;  
&nbsp;  @Schema(name = &quot;agent&quot;, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
&nbsp;  @JsonProperty(&quot;agent&quot;)
&nbsp;  public Boolean getAgent() {
<b class="fc">&nbsp;    return agent;</b>
&nbsp;  }
&nbsp;
&nbsp;  public void setAgent(Boolean agent) {
<b class="fc">&nbsp;    this.agent = agent;</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public boolean equals(Object o) {
<b class="pc">&nbsp;    if (this == o) {</b>
<b class="nc">&nbsp;      return true;</b>
&nbsp;    }
<b class="pc">&nbsp;    if (o == null || getClass() != o.getClass()) {</b>
<b class="nc">&nbsp;      return false;</b>
&nbsp;    }
<b class="fc">&nbsp;    CitizenDTO citizen = (CitizenDTO) o;</b>
<b class="pc">&nbsp;    return Objects.equals(this.niss, citizen.niss) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.firstname, citizen.firstname) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.lastname, citizen.lastname) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.numbox, citizen.numbox) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.zipCode, citizen.zipCode) &amp;&amp;</b>
<b class="pc">&nbsp;        Objects.equals(this.pensionNumber, citizen.pensionNumber) &amp;&amp;</b>
<b class="fc">&nbsp;        Objects.equals(this.agent, citizen.agent);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public int hashCode() {
<b class="nc">&nbsp;    return Objects.hash(niss, firstname, lastname, numbox, zipCode, pensionNumber, agent);</b>
&nbsp;  }
&nbsp;
&nbsp;  @Override
&nbsp;  public String toString() {
<b class="nc">&nbsp;    StringBuilder sb = new StringBuilder();</b>
<b class="nc">&nbsp;    sb.append(&quot;class CitizenDTO {\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    niss: &quot;).append(toIndentedString(niss)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    firstname: &quot;).append(toIndentedString(firstname)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    lastname: &quot;).append(toIndentedString(lastname)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    numbox: &quot;).append(toIndentedString(numbox)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    zipCode: &quot;).append(toIndentedString(zipCode)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    pensionNumber: &quot;).append(toIndentedString(pensionNumber)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;    agent: &quot;).append(toIndentedString(agent)).append(&quot;\n&quot;);</b>
<b class="nc">&nbsp;    sb.append(&quot;}&quot;);</b>
<b class="nc">&nbsp;    return sb.toString();</b>
&nbsp;  }
&nbsp;
&nbsp;  /**
&nbsp;   * Convert the given object to string with each line indented by 4 spaces
&nbsp;   * (except the first line).
&nbsp;   */
&nbsp;  private String toIndentedString(Object o) {
<b class="nc">&nbsp;    if (o == null) {</b>
<b class="nc">&nbsp;      return &quot;null&quot;;</b>
&nbsp;    }
<b class="nc">&nbsp;    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</b>
&nbsp;  }
&nbsp;}
&nbsp;
</code>
</pre>
</div>

<script type="text/javascript">
(function() {
    var msie = false, msie9 = false;
    /*@cc_on
      msie = true;
      @if (@_jscript_version >= 9)
        msie9 = true;
      @end
    @*/

    if (!msie || msie && msie9) {
      hljs.highlightAll()
      hljs.initLineNumbersOnLoad();
    }
})();
</script>

<div class="footer">
    
    <div style="float:right;">generated on 2025-07-17 14:10</div>
</div>
</body>
</html>
