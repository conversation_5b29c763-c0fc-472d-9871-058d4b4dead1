package be.fgov.onerva.person.backend.request.service;

import be.fgov.onerva.person.backend.util.StringUtils;
import jakarta.jms.JMSException;
import jakarta.jms.TextMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.jms.annotation.JmsListener;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.stereotype.Component;

import static be.fgov.onerva.person.backend.util.StringUtils.truncateAndPadLeft;
import static be.fgov.onerva.person.backend.util.StringUtils.truncateAndPadRight;

/**
 * Local simulator for mainframe responses.
 * This component is only active in the local profile and simulates the
 * mainframe
 * by listening to the output queue and generating appropriate success
 * responses.
 * It listens to the input queue and sends success responses to the output queue.
 t.
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Profile("local")
public class LocalPersonResponseSimulator {

    private final JmsTemplate jmsTemplate;

    @Value("${queue.out}")
    private String outputQueue;

    @Value("${local.simulated-error-code}")
    private Integer simulatedErrorCode;

    /**
     * Listens to the output queue and simulates mainframe responses.
     * This method is triggered when a message is sent to the output queue.
     * It parses the request message, creates a success response, and sends it back
     * to the input queue.
     *
     * @param message The request message
     * @throws JMSException If there's an error processing the message
     */
    @JmsListener(destination = "${queue.in}")
    public void simulateResponse(TextMessage message) throws JMSException {
        String requestPayload = message.getText();
        log.debug("Received request for simulation: {}", requestPayload);

        try {
            // Determine the request type and create appropriate response
            String response;
            if (requestPayload.startsWith("0001")) { // CREATE
                response = createSuccessResponse(requestPayload);
            } else if (requestPayload.startsWith("0002")) { // UPDATE
                response = updateSuccessResponse(requestPayload, simulatedErrorCode);
            } else {
                log.warn("Unknown request type: {}", requestPayload);
                return;
            }

            // Send the response back to the input queue
            log.debug("Sending simulated response: {}", response);
            jmsTemplate.convertAndSend(outputQueue, response);
        } catch (Exception e) {
            log.error("Error simulating response for request: {}", requestPayload, e);
        }
    }

    /**
     * Creates a success response for a CREATE request.
     * Format: names + inss + id + errorCode
     *
     * @param request The CREATE request payload
     * @return The success response
     */
    private String createSuccessResponse(String request) {
        // Constants for CREATE request format
        String CREATE_CODE = "0001";
        int ID_LEN = 19;
        int INSS_LEN = 11;
        int NAMES_LEN = 30;

        // Extract data from request
        String id = request.substring(CREATE_CODE.length(), CREATE_CODE.length() + ID_LEN);
        String inss = request.substring(CREATE_CODE.length() + ID_LEN, CREATE_CODE.length() + ID_LEN + INSS_LEN);
        // Skip operator code (4 chars)
        String names = request.substring(CREATE_CODE.length() + ID_LEN + INSS_LEN + 4);

        // Format response: names + inss + id + errorCode
        // For CREATE, success code is 1
        return truncateAndPadRight(names, NAMES_LEN) +
                inss +
                id +
                "+0001"; // Success code for CREATE
    }

    /**
     * Creates a success response for an UPDATE request.
     * Format: type + id + names + inss + errorCode
     *
     * @param request The UPDATE request payload
     * @return The success response
     */
    private String updateSuccessResponse(String request, Integer errorCode) {
        // Constants for UPDATE request format
        String UPDATE_CODE = "0002";
        int ID_LEN = 19;
        int INSS_LEN = 11;
        int NAMES_LEN = 30;

        // Extract data from request (UPDATE messages are 360 chars long)
        String id = request.substring(UPDATE_CODE.length(), UPDATE_CODE.length() + ID_LEN);
        String inss = request.substring(UPDATE_CODE.length() + ID_LEN, UPDATE_CODE.length() + ID_LEN + INSS_LEN);

        // For UPDATE requests, we'll use a simplified approach for the simulator
        String names = truncateAndPadRight("SIMULATED,RESPONSE", NAMES_LEN);

        // Format response: type + id + names + inss + errorCode
        // For UPDATE, success code is 0
        return UPDATE_CODE +
                id +
                names +
                inss +
                truncateAndPadLeft(errorCode.toString(), 5, '0');
    }
}
